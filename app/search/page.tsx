'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Footer from '../components/Footer/Footer';
import { Button } from "@/app/components/ui/button";
import { IsolatedInput } from '../components/ui/IsolatedInput';

// 类型定义
interface ProjectData {
  projectId: string;
  tissueOrCellType?: string;
  cellLine?: string;
  disease?: string;
  [key: string]: string | undefined;
}

interface SampleData {
  sraAccession: string;
  tissueCellType?: string;
  cellLine?: string;
  condition?: string;
  [key: string]: string | undefined;
}

// 静态选项作为后备
const fallbackProjectOptions = ["TEDD00001", "TEDD00002", "TEDD00003"];
const fallbackTissueOptions = ["Brain Tissue", "Liver Tissue", "Kidney Tissue"];
const fallbackCellLineOptions = ["A549", "HeL<PERSON>", "HEK293T"];
const fallbackDiseaseOptions = ["Lung Adenocarcinoma", "Breast Cancer", "Normal"];
const fallbackSraOptions = ["SRR1257233", "SRR1257234", "SRR1257235"];
const geneSymbolOptions = ["TP53", "PIK3CA", "ARID1A", "PTEN", "APC", "KRAS", "EGFR", "VHL", "FGFR3", "IDH1", "BRAF", "CDH1", "TBX3", "ATRX", "CIC", "PBRM1", "STK11", "NFE2L2", "KMT2D", "MUC16"];
const geneIdOptions = [
  "ENSG00000141510",
  "ENSG00000121879",
  "ENSG00000117713",
  "ENSG00000171862",
  "ENSG00000134982",
  "ENSG00000133703",
  "ENSG00000146648",
  "ENSG00000134086",
  "ENSG00000068078",
  "ENSG00000138413",
  "ENSG00000157764",
  "ENSG00000039068",
  "ENSG00000135111",
  "ENSG00000085224",
  "ENSG00000079432",
  "ENSG00000163939",
  "ENSG00000118046",
  "ENSG00000116044",
  "ENSG00000167548",
  "ENSG00000181143"
];

// 将 SearchField 移到组件外部，避免重新创建
const SearchField = React.memo(({ label, value, onChange, getOptions, eg, linkUrl, isLoading }: {
  label: string,
  value: string,
  onChange: (val: string) => void,
  getOptions: (inputValue: string) => string[],
  eg: string,
  linkUrl?: string,
  isLoading?: boolean
}) => {
  const router = useRouter();
  
  const handleExampleClick = useCallback(() => {
    if (linkUrl) {
      router.push(linkUrl);
    }
  }, [linkUrl, router]);

  // 使用 useMemo 缓存解析结果
  const { egPrefix, egValue, isValueItalic } = useMemo(() => {
    const parts = eg.split(' ');
    return {
      egPrefix: parts[0],
      egValue: parts.slice(1).join(' '),
      isValueItalic: parts.slice(1).join(' ') === 'TP53'
    };
  }, [eg]);

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-900">{label}</label>
      <div className="w-full">
        <IsolatedInput
          value={value}
          onChange={onChange}
          getOptions={getOptions}
          placeholder={isLoading ? "Loading..." : "Select or type..."}
        />
        <p className="text-xs text-gray-500 mt-0.5">
          <em className="italic">{egPrefix}</em>{' '}
          {linkUrl ? (
            <button
              onClick={handleExampleClick}
              className={`hover:underline ${isValueItalic ? 'italic' : ''}`}
              style={{ color: '#337ab7' }}
            >
              {egValue}
            </button>
          ) : (
            <span className={isValueItalic ? 'italic' : ''}>{egValue}</span>
          )}
        </p>
      </div>
    </div>
  );
});

SearchField.displayName = 'SearchField';

export default function SearchPage() {
  const router = useRouter();

  // 简化状态管理 - 直接使用独立的状态
  const [activeSearchType, setActiveSearchType] = useState<'project' | 'sample' | 'gene' | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // 分离各个字段的状态，避免复杂的对象更新
  const [projectId, setProjectId] = useState('');
  const [sraAccession, setSraAccession] = useState('');
  const [geneSymbol, setGeneSymbol] = useState('');
  const [geneId, setGeneId] = useState('');

  // 为每个搜索模块创建独立的状态
  const [projectSearch, setProjectSearch] = useState({
    tissueType: '',
    cellLine: '',
    disease: ''
  });
  
  const [sampleSearch, setSampleSearch] = useState({
    tissueType: '',
    cellLine: '',
    disease: ''
  });
  
  const [geneSearch, setGeneSearch] = useState({
    tissueType: '',
    cellLine: '',
    disease: ''
  });

  // API数据状态
  const [projectsData, setProjectsData] = useState<ProjectData[]>([]);
  const [samplesData, setSamplesData] = useState<SampleData[]>([]);
  const [geneSymbolsData, setGeneSymbolsData] = useState<string[]>([]);
  const [geneIdsData, setGeneIdsData] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 获取API数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // 并行获取所有数据
        const [projectsResponse, samplesResponse, geneSymbolsResponse, geneIdsResponse] = await Promise.all([
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/all`),
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/samples/all`),
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/gene-info/all-gene-symbols`),
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/gene-info/all-gene-ids`)
        ]);

        if (projectsResponse.ok) {
          const projectsData = await projectsResponse.json();
          setProjectsData(Array.isArray(projectsData) ? projectsData : []);
        } else {
          console.warn('Failed to fetch projects data:', projectsResponse.status);
        }

        if (samplesResponse.ok) {
          const samplesData = await samplesResponse.json();
          setSamplesData(Array.isArray(samplesData) ? samplesData : []);
        } else {
          console.warn('Failed to fetch samples data:', samplesResponse.status);
        }

        if (geneSymbolsResponse.ok) {
          const geneSymbolsData = await geneSymbolsResponse.json();
          const sortedSymbols = Array.isArray(geneSymbolsData) ? geneSymbolsData.sort() : [];
          setGeneSymbolsData(sortedSymbols);
        } else {
          console.warn('Failed to fetch gene symbols data:', geneSymbolsResponse.status);
        }

        if (geneIdsResponse.ok) {
          const geneIdsData = await geneIdsResponse.json();
          const sortedIds = Array.isArray(geneIdsData) ? geneIdsData.sort() : [];
          setGeneIdsData(sortedIds);
        } else {
          console.warn('Failed to fetch gene IDs data:', geneIdsResponse.status);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // 使用 useMemo 缓存选项数据，避免每次重新计算
  const projectOptions = useMemo(() => {
    if (isLoading || !projectsData || projectsData.length === 0) {
      return fallbackProjectOptions;
    }
    return Array.from(new Set(
      projectsData
        .map(item => item.projectId)
        .filter((value): value is string => typeof value === 'string' && value.length > 0)
    ));
  }, [projectsData, isLoading]);

  const getProjectOptions = useCallback((inputValue: string) => {
    if (!inputValue) return projectOptions.slice(0, 20);
    return projectOptions
      .filter(option => option.toLowerCase().includes(inputValue.toLowerCase()))
      .slice(0, 20);
  }, [projectOptions]);

  const tissueOptions = useMemo(() => {
    if (isLoading) {
      return fallbackTissueOptions;
    }
    const allTissueTypes = [
      ...projectsData.map(p => p.tissueOrCellType),
      ...samplesData.map(s => s.tissueCellType)
    ].filter((item): item is string => Boolean(item && typeof item === 'string'));
    return Array.from(new Set(allTissueTypes));
  }, [projectsData, samplesData, isLoading]);

  const getTissueOptions = useCallback((inputValue: string) => {
    if (!inputValue) return tissueOptions.slice(0, 20);
    return tissueOptions
      .filter(option => option.toLowerCase().includes(inputValue.toLowerCase()))
      .slice(0, 20);
  }, [tissueOptions]);

  const cellLineOptions = useMemo(() => {
    if (isLoading) {
      return fallbackCellLineOptions;
    }
    const allCellLines = [
      ...projectsData.map(p => p.cellLine),
      ...samplesData.map(s => s.cellLine)
    ].filter((item): item is string => Boolean(item && typeof item === 'string'));
    return Array.from(new Set(allCellLines));
  }, [projectsData, samplesData, isLoading]);

  const getCellLineOptions = useCallback((inputValue: string) => {
    if (!inputValue) return cellLineOptions.slice(0, 20);
    return cellLineOptions
      .filter(option => option.toLowerCase().includes(inputValue.toLowerCase()))
      .slice(0, 20);
  }, [cellLineOptions]);

  const diseaseOptions = useMemo(() => {
    if (isLoading) {
      return fallbackDiseaseOptions;
    }
    const allDiseases = [
      ...projectsData.map(p => p.disease),
      ...samplesData.map(s => s.condition)
    ].filter((item): item is string => Boolean(item && typeof item === 'string'));
    return Array.from(new Set(allDiseases));
  }, [projectsData, samplesData, isLoading]);

  const getDiseaseOptions = useCallback((inputValue: string) => {
    if (!inputValue) return diseaseOptions.slice(0, 20);
    return diseaseOptions
      .filter(option => option.toLowerCase().includes(inputValue.toLowerCase()))
      .slice(0, 20);
  }, [diseaseOptions]);

  const sraOptions = useMemo(() => {
    if (isLoading || !samplesData || samplesData.length === 0) {
      return fallbackSraOptions;
    }
    return Array.from(new Set(
      samplesData
        .map(item => item.sraAccession)
        .filter((value): value is string => typeof value === 'string' && value.length > 0)
    ));
  }, [samplesData, isLoading]);

  const getSraOptions = useCallback((inputValue: string) => {
    if (!inputValue) return sraOptions.slice(0, 20);
    return sraOptions
      .filter(option => option.toLowerCase().includes(inputValue.toLowerCase()))
      .slice(0, 20);
  }, [sraOptions]);

  const getGeneSymbolOptions = useCallback((inputValue: string) => {
    if (!inputValue) return geneSymbolOptions;
    
    const options = isLoading || !geneSymbolsData || geneSymbolsData.length === 0 
      ? geneSymbolOptions 
      : geneSymbolsData;
    
    return options
      .filter(option => option.toLowerCase().includes(inputValue.toLowerCase()))
      .slice(0, 20);
  }, [geneSymbolsData, isLoading]);

  const getGeneIdOptions = useCallback((inputValue: string) => {
    if (!inputValue) return geneIdOptions;
    
    const options = isLoading || !geneIdsData || geneIdsData.length === 0 
      ? geneIdOptions 
      : geneIdsData;
    
    return options
      .filter(option => option.toLowerCase().includes(inputValue.toLowerCase()))
      .slice(0, 20);
  }, [geneIdsData, isLoading]);

  // 项目搜索的字段更新处理
  const handleProjectIdChange = useCallback((value: string) => {
    setProjectId(value);
    if (value && !activeSearchType) setActiveSearchType('project');
  }, [activeSearchType]);

  const handleProjectTissueChange = useCallback((value: string) => {
    setProjectSearch(prev => ({ ...prev, tissueType: value }));
    if (value && !activeSearchType) setActiveSearchType('project');
  }, [activeSearchType]);

  const handleProjectCellLineChange = useCallback((value: string) => {
    setProjectSearch(prev => ({ ...prev, cellLine: value }));
    if (value && !activeSearchType) setActiveSearchType('project');
  }, [activeSearchType]);

  const handleProjectDiseaseChange = useCallback((value: string) => {
    setProjectSearch(prev => ({ ...prev, disease: value }));
    if (value && !activeSearchType) setActiveSearchType('project');
  }, [activeSearchType]);

  // 样本搜索的字段更新处理
  const handleSampleSraChange = useCallback((value: string) => {
    setSraAccession(value);
    if (value && !activeSearchType) setActiveSearchType('sample');
  }, [activeSearchType]);

  const handleSampleTissueChange = useCallback((value: string) => {
    setSampleSearch(prev => ({ ...prev, tissueType: value }));
    if (value && !activeSearchType) setActiveSearchType('sample');
  }, [activeSearchType]);

  const handleSampleCellLineChange = useCallback((value: string) => {
    setSampleSearch(prev => ({ ...prev, cellLine: value }));
    if (value && !activeSearchType) setActiveSearchType('sample');
  }, [activeSearchType]);

  const handleSampleDiseaseChange = useCallback((value: string) => {
    setSampleSearch(prev => ({ ...prev, disease: value }));
    if (value && !activeSearchType) setActiveSearchType('sample');
  }, [activeSearchType]);

  // 基因搜索的字段更新处理
  const handleGeneSymbolChange = useCallback((value: string) => {
    setGeneSymbol(value);
    if (value) {
      setGeneId(''); // Gene Symbol 和 Gene ID 互斥
      if (!activeSearchType) setActiveSearchType('gene');
    }
  }, [activeSearchType]);

  const handleGeneIdChange = useCallback((value: string) => {
    setGeneId(value);
    if (value) {
      setGeneSymbol(''); // Gene Symbol 和 Gene ID 互斥
      if (!activeSearchType) setActiveSearchType('gene');
    }
  }, [activeSearchType]);

  const handleGeneTissueChange = useCallback((value: string) => {
    setGeneSearch(prev => ({ ...prev, tissueType: value }));
    if (value && !activeSearchType) setActiveSearchType('gene');
  }, [activeSearchType]);

  const handleGeneCellLineChange = useCallback((value: string) => {
    setGeneSearch(prev => ({ ...prev, cellLine: value }));
    if (value && !activeSearchType) setActiveSearchType('gene');
  }, [activeSearchType]);

  const handleGeneDiseaseChange = useCallback((value: string) => {
    setGeneSearch(prev => ({ ...prev, disease: value }));
    if (value && !activeSearchType) setActiveSearchType('gene');
  }, [activeSearchType]);

  const handleSubmit = () => {
    // 清除之前的错误信息
    setErrorMessage('');
    
    // 检查是否有输入内容
    const hasProjectInput = projectId || projectSearch.tissueType || projectSearch.cellLine || projectSearch.disease;
    const hasSampleInput = sraAccession || sampleSearch.tissueType || sampleSearch.cellLine || sampleSearch.disease;
    const hasGeneInput = geneSymbol || geneId || geneSearch.tissueType || geneSearch.cellLine || geneSearch.disease;
    
    const hasInput = hasProjectInput || hasSampleInput || hasGeneInput;
    
    if (!activeSearchType || !hasInput) {
      setErrorMessage('Please select a search type and enter at least one search condition.');
      return;
    }

    // 根据搜索类型进行不同的处理
    if (activeSearchType === 'project') {
      // 构建查询参数
      const searchParams = new URLSearchParams();
      
      // 添加搜索关键字（如果有projectId）
      if (projectId) {
        searchParams.set('search', projectId);
      }

      // 添加过滤器参数
      if (projectSearch.tissueType) {
        searchParams.set('tissueType', projectSearch.tissueType);
      }
      if (projectSearch.cellLine) {
        searchParams.set('cellLine', projectSearch.cellLine);
      }
      if (projectSearch.disease) {
        searchParams.set('disease', projectSearch.disease);
      }
      
      // 跳转到dataset页面
      router.push(`/browse/dataset?${searchParams.toString()}`);
    } else if (activeSearchType === 'sample') {
      // 构建查询参数
      const searchParams = new URLSearchParams();
      
      // 添加搜索关键字（如果有sraAccession）
      if (sraAccession) {
        searchParams.set('search', sraAccession);
      }

      // 添加过滤器参数
      if (sampleSearch.tissueType) {
        searchParams.set('tissueType', sampleSearch.tissueType);
      }
      if (sampleSearch.cellLine) {
        searchParams.set('cellLine', sampleSearch.cellLine);
      }
      if (sampleSearch.disease) {
        searchParams.set('disease', sampleSearch.disease);
      }
      
      // 跳转到sample页面
      router.push(`/browse/sample?${searchParams.toString()}`);
    } else if (activeSearchType === 'gene') {
      // Gene搜索必须有Gene Symbol或Gene ID中的一个
      if (!geneSymbol && !geneId) {
        setErrorMessage('Either Gene Symbol or Gene ID is required for gene search.');
        return;
      }

      // 构建查询参数
      const searchParams = new URLSearchParams();

      // 添加必需的Gene Symbol或Gene ID参数
      if (geneSymbol) {
        searchParams.set('geneSymbol', geneSymbol);
      } else if (geneId) {
        searchParams.set('geneId', geneId);
      }

      // 添加过滤器参数
      if (geneSearch.tissueType) {
        searchParams.set('tissueType', geneSearch.tissueType);
      }
      if (geneSearch.cellLine) {
        searchParams.set('cellLine', geneSearch.cellLine);
      }
      if (geneSearch.disease) {
        searchParams.set('disease', geneSearch.disease);
      }
      
      // 跳转到gene页面
      router.push(`/browse/gene?${searchParams.toString()}`);
    } else {
      // 其他搜索类型的处理逻辑可以在这里添加
      setErrorMessage(`${activeSearchType} search functionality will be implemented soon.`);
    }
  };

  const handleReset = () => {
    setActiveSearchType(null);
    setErrorMessage('');
    setProjectId('');
    setSraAccession('');
    setGeneSymbol('');
    setGeneId('');
    
    // 重置所有搜索模块的状态
    setProjectSearch({
      tissueType: '',
      cellLine: '',
      disease: ''
    });
    setSampleSearch({
      tissueType: '',
      cellLine: '',
      disease: ''
    });
    setGeneSearch({
      tissueType: '',
      cellLine: '',
      disease: ''
    });
  };


  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span style={{ color: '#337ab7' }}>Search</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1" style={{ margin: '0 8%' }}>
        <div className="py-10">


          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* 左侧两列容器 */}
            <div className="lg:col-span-2 space-y-10">
              
              {/* Top row: Project & Sample */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-10">
                {/* Search by Project */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-[#267DD4] text-white text-center py-3">
                    <h2 className="text-xl font-semibold">Search by Dataset</h2>
                  </div>
                  <div className="p-5 space-y-4">
                    <SearchField
                      key="project-id"
                      label="Dataset ID"
                      value={projectId}
                      onChange={handleProjectIdChange}
                      getOptions={getProjectOptions}
                      eg="e.g. TEDD00001"
                      linkUrl="/browse/dataset?search=TEDD00001"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="project-tissue"
                      label="Tissue/Cell Type"
                      value={projectSearch.tissueType}
                      onChange={handleProjectTissueChange}
                      getOptions={getTissueOptions}
                      eg="e.g. Brain Tissue"
                      linkUrl="/browse/dataset?tissueType=Brain%20Tissue"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="project-cellline"
                      label="Cell Line"
                      value={projectSearch.cellLine}
                      onChange={handleProjectCellLineChange}
                      getOptions={getCellLineOptions}
                      eg="e.g. A549"
                      linkUrl="/browse/dataset?cellLine=A549"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="project-condition"
                      label="Condition"
                      value={projectSearch.disease}
                      onChange={handleProjectDiseaseChange}
                      getOptions={getDiseaseOptions}
                      eg="e.g. Lung Adenocarcinoma"
                      linkUrl="/browse/dataset?disease=Lung%20Adenocarcinoma"
                      isLoading={isLoading}
                    />
                  </div>
                </div>

                {/* Search by Sample */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-[#267DD4] text-white text-center py-3">
                    <h2 className="text-xl font-semibold">Search by Sample</h2>
                  </div>
                  <div className="p-5 space-y-4">
                    <SearchField
                      key="sample-sra"
                      label="SRA Accession"
                      value={sraAccession}
                      onChange={handleSampleSraChange}
                      getOptions={getSraOptions}
                      eg="e.g. SRR1257233"
                      linkUrl="/browse/sample?search=SRR1257233"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="sample-tissue"
                      label="Tissue/Cell Type"
                      value={sampleSearch.tissueType}
                      onChange={handleSampleTissueChange}
                      getOptions={getTissueOptions}
                      eg="e.g. Brain Tissue"
                      linkUrl="/browse/sample?tissueType=Brain%20Tissue"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="sample-cellline"
                      label="Cell Line"
                      value={sampleSearch.cellLine}
                      onChange={handleSampleCellLineChange}
                      getOptions={getCellLineOptions}
                      eg="e.g. A549"
                      linkUrl="/browse/sample?cellLine=A549"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="sample-condition"
                      label="Condition"
                      value={sampleSearch.disease}
                      onChange={handleSampleDiseaseChange}
                      getOptions={getDiseaseOptions}
                      eg="e.g. Lung Adenocarcinoma"
                      linkUrl="/browse/sample?disease=Lung%20Adenocarcinoma"
                      isLoading={isLoading}
                    />
                  </div>
                </div>
              </div>

              {/* Search by Gene - now inside the left container */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="bg-[#267DD4] text-white text-center py-3">
                  <h2 className="text-xl font-semibold">Search by Gene</h2>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {/* Row 1 */}
                    <SearchField
                      key="gene-symbol"
                      label="Gene Symbol"
                      value={geneSymbol}
                      onChange={handleGeneSymbolChange}
                      getOptions={getGeneSymbolOptions}
                      eg="e.g. TP53"
                      linkUrl="/browse/gene?geneSymbol=TP53"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="gene-id"
                      label="Gene ID"
                      value={geneId}
                      onChange={handleGeneIdChange}
                      getOptions={getGeneIdOptions}
                      eg="e.g. ENSG00000139618"
                      linkUrl="/browse/gene?geneId=ENSG00000139618"
                      isLoading={isLoading}
                    />
                    {/* Row 2 */}
                    <SearchField
                      key="gene-tissue"
                      label="Tissue/Cell Type"
                      value={geneSearch.tissueType}
                      onChange={handleGeneTissueChange}
                      getOptions={getTissueOptions}
                      eg="e.g. Brain tissue"
                      isLoading={isLoading}
                    />
                    <SearchField
                      key="gene-cellline"
                      label="Cell Line"
                      value={geneSearch.cellLine}
                      onChange={handleGeneCellLineChange}
                      getOptions={getCellLineOptions}
                      eg="e.g. A549"
                      isLoading={isLoading}
                    />
                    {/* Row 3 */}
                    <SearchField
                      key="gene-condition"
                      label="Condition"
                      value={geneSearch.disease}
                      onChange={handleGeneDiseaseChange}
                      getOptions={getDiseaseOptions}
                      eg="e.g. Lung adenocarcinoma"
                      isLoading={isLoading}
                    />
                  </div>
                </div>
              </div>

            </div>

            {/* Right column for Tips */}
            <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full flex flex-col">
                  <div className="bg-[#267DD4] text-white text-center py-3 relative">
                    <h2 className="text-xl font-semibold">Search Tips</h2>
                  </div>
                  <div className="p-6 flex-grow">
                    <div className="text-gray-700 leading-relaxed space-y-5 text-lg">
                      <p>Three advanced search modules are available:</p>
                      <p><strong>1.</strong> Users can directly access their dataset of interest by entering or selecting a specific Dataset ID, Tissue/Cell Type, Cell Line, or Condition.</p>
                      <p><strong>2.</strong> Users can search for specific samples by entering SRA Accession numbers along with sample characteristics.</p>
                      <p><strong>3.</strong> By searching for Gene Symbol or Gene ID (either one is required) along with Tissue/Cell Type, Cell Line or Condition, users can obtain the TE, TR and EVI values of the selected gene and transcripts, as well as the corresponding UTR information for transcripts across the selected biological context. Gene Symbol and Gene ID are mutually exclusive, users can only select one of them at a time.</p>
                    </div>
                  </div>
                </div>
            </div>

          </div>
          
          {/* 错误提示区域 */}
          {errorMessage && (
            <div className="mt-8 mx-auto max-w-md">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{errorMessage}</p>
                  </div>
                  <div className="ml-auto pl-3">
                    <div className="-mx-1.5 -my-1.5">
                      <button
                        type="button"
                        onClick={() => setErrorMessage('')}
                        className="inline-flex rounded-md bg-red-50 p-1.5 text-red-500 hover:bg-red-100"
                      >
                        <span className="sr-only">Close</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* 按钮区域 */}
          <div className="mt-12 flex justify-center space-x-6">
            <Button
              onClick={handleSubmit}
              className="btn-submit-wide"
            >
              Submit
            </Button>
            <Button
              onClick={handleReset}
              variant="outline"
              className="btn-reset"
            >
              Reset
            </Button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8">
        <Footer />
      </div>
    </div>
  );
}
 