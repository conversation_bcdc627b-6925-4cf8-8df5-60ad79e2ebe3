import { TranscriptData, DataType } from './types';

// 疾病大类与condition的映射关系
const CONDITION_GROUPS = {
  "Infections": [
    "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
    "Adult Hepatocellular Carcinoma; HCV Transfection",
    "B95-8 Epstein-Barr Virus Infection",
    "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
    "Human Cytomegalovirus Infection",
    "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
    "IAV Transfection",
    "Lung Adenocarcinoma; IAV Infection",
    "Lung Adenocarcinoma; SARS-CoV-2 Infection",
    "M81 Epstein-Barr Virus Infection",
    "Toxoplasma Infection"
  ],
  "Gastrointestinal System Cancer": [
    "Adult Hepatocellular Carcinoma",
    "Childhood Hepatocellular Carcinoma",
    "Colon Adenocarcinoma",
    "Colon Carcinoma",
    "Esophageal Squamous Cell Carcinoma",
    "Hepatoblastoma",
    "Hepatocellular Carcinoma",
    "Intrahepatic Cholangiocarcinoma"
  ],
  "Musculoskeletal System Cancer": [
    "Osteosarcoma"
  ],
  "Reproductive Organ Cancer": [
    "Human Papillomavirus-related Endocervical Adenocarcinoma",
    "Ovarian Cancer",
    "Ovarian Endometrioid Carcinoma",
    "Prostate Cancer",
    "Prostate Carcinoma"
  ],
  "Respiratory System Cancer": [
    "Lung Adenocarcinoma",
    "Lung Large Cell Carcinoma"
  ],
  "Urinary System Cancer": [
    "Kidney Rhabdoid Cancer",
    "Kidney Tumor",
    "Renal Cell Carcinoma"
  ],
  "Genetic Disease": [
    "Duchenne Muscular Dystrophy",
    "Hbs1L Deficiency",
    "Roberts Syndrome",
    "Tuberous Sclerosis Complex"
  ],
  "Other": [
    "Neoplastic Transformation",
    "Patient-derived",
    "Treacher Collins Syndrome"
  ],
  "Nervous System Cancer": [
    "Astrocytoma",
    "Brain Glioma",
    "Neuroblastoma"
  ],
  "Hematologic Cancer": [
    "Acute Myeloid Leukemia",
    "Adult Acute Myeloid Leukemia",
    "Childhood T Acute Lymphoblastic Leukemia",
    "Childhood T Lymphoblastic Lymphoma",
    "Chronic Myeloid Leukemia",
    "Multiple Myeloma"
  ],
  "Breast Cancer": [
    "Breast Adenocarcinoma",
    "Breast Carcinoma"
  ],
  "Head And Neck Cancer": [
    "Head And Neck Squamous Cell Carcinoma",
    "Tongue Squamous Cell Carcinoma"
  ],
  "Endocrine Gland Cancer": [
    "Pancreatic Adenocarcinoma"
  ]
};
// 获取条件的分组
const getConditionGroup = (condition: string): string => {
  for (const [groupName, conditions] of Object.entries(CONDITION_GROUPS)) {
    if (conditions.includes(condition)) {
      return groupName;
    }
  }
  return 'Normal'; // 未分类的条件改为Normal
};

// 马卡龙色系 - 使用与其他分析页面相同的颜色方案
export const macaronColors = [
  '#FFD1DC', '#B5EAD7', '#C7CEEA', '#FFDAC1',
  '#E0BBE4', '#FFB7B2', '#FFCCCB', '#DDA0DD',
  '#F0E68C', '#98FB98', '#87CEEB', '#DEB887'
];

// 下载图表为图片
export const downloadChartImage = (
  dataType: DataType,
  displayName: string,
  transcriptId?: string,
  analysisType?: string,
  categories?: string[],
  projectIds?: string[]
) => {
  const container = document.getElementById(`boxplot-container-${dataType}`);
  if (!container) return;

  const svg = container.querySelector('svg');
  if (!svg) return;

  // 创建canvas
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  // 设置canvas大小
  const svgData = new XMLSerializer().serializeToString(svg);
  const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
  const svgUrl = URL.createObjectURL(svgBlob);

  const img = new Image();
  img.onload = function() {
    // 预留标题区域高度
    const headerHeight = 60;

    const cats = Array.isArray(categories) ? categories.filter(c => c !== 'ALL') : [];
    const pids = Array.isArray(projectIds) ? projectIds : [];

    // 计算右侧图例所需宽度
    const maxItemsPerColumn = 20;
    const columnWidth = 170;
    const categoryLegendColumns = cats.length > 0 ? Math.ceil(cats.length / maxItemsPerColumn) : 0;
    const projectLegendColumns = pids.length > 0 ? Math.ceil(pids.length / maxItemsPerColumn) : 0;
    const categoryLegendWidth = categoryLegendColumns * columnWidth + (categoryLegendColumns > 0 ? 20 : 0);
    const projectLegendWidth = projectLegendColumns * columnWidth + (projectLegendColumns > 0 ? 20 : 0);
    const legendsGap = categoryLegendColumns > 0 && projectLegendColumns > 0 ? 40 : 0;
    const legendsTotalWidth = categoryLegendWidth + projectLegendWidth + legendsGap;

    canvas.width = img.width + legendsTotalWidth;
    canvas.height = img.height + headerHeight;

    // 背景
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制标题（参考 gene-level 风格）
    const transcriptIdForTitle = transcriptId || 'transcript';
    const analysisLabelMap: Record<string, string> = {
      'Tissue/Cell Type': 'tissues/cell types',
      'Cell Line': 'cell lines',
      'Condition': 'conditions'
    };
    const analysisLabel = analysisType ? analysisLabelMap[analysisType] || analysisType.toLowerCase() + 's' : 'contexts';
    const title = `${displayName} of ${transcriptIdForTitle} in different ${analysisLabel}`;
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(title, canvas.width / 2, Math.max(24, headerHeight / 2 + 8));

    // 绘制原SVG图像，向下偏移标题高度
    ctx.drawImage(img, 0, headerHeight);


    // 绘制右侧图例
    let legendStartX = img.width + 20;
    const baseY = headerHeight + 10;

    // 1) 分类图例（analysisType 标签）
    if (categoryLegendColumns > 0) {
      const analysisLabelMap: Record<string, string> = {
        'Tissue/Cell Type': 'Tissue/Cell Type',
        'Cell Line': 'Cell Line',
        'Condition': 'Condition'
      };
      const analysisLabel = analysisType ? analysisLabelMap[analysisType] : 'context';
      ctx.fillStyle = '#111827';
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'start';
      ctx.fillText(analysisLabel, legendStartX, baseY);

      ctx.font = '11px Arial';
      const y = baseY + 16;
      cats.forEach((cat, idx) => {
        const colIndex = Math.floor(idx / maxItemsPerColumn);
        const rowIndex = idx % maxItemsPerColumn;
        const x = legendStartX + colIndex * columnWidth;
        const yy = y + rowIndex * 16;
        const color = macaronColors[(categories ? categories.indexOf(cat) : idx) % macaronColors.length];

        // 色块
        ctx.fillStyle = color;
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 0.5;
        ctx.fillRect(x, yy - 8, 12, 12);
        ctx.strokeRect(x, yy - 8, 12, 12);

        // 文字
        ctx.fillStyle = '#374151';
        ctx.textAlign = 'start';
        ctx.fillText(cat, x + 16, yy + 2);
      });

      legendStartX += categoryLegendWidth + (projectLegendColumns > 0 ? 20 : 0);
    }

    // 2) 数据集图例（Dataset Id）
    if (projectLegendColumns > 0) {
      ctx.fillStyle = '#111827';
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'start';
      ctx.fillText('Dataset ID', legendStartX, baseY);

      ctx.font = '11px Arial';
      const y = baseY + 16;
      pids.forEach((pid, idx) => {
        const colIndex = Math.floor(idx / maxItemsPerColumn);
        const rowIndex = idx % maxItemsPerColumn;
        const x = legendStartX + colIndex * columnWidth;
        const yy = y + rowIndex * 16;
        const color = macaronColors[idx % macaronColors.length];

        // 圆点
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x + 6, yy - 2, 5, 0, 2 * Math.PI);
        ctx.fill();

        // 文字
        ctx.fillStyle = '#374151';
        ctx.textAlign = 'start';
        ctx.fillText(pid, x + 16, yy + 2);
      });
    }
    
    // 下载
    const link = document.createElement('a');

    // 构建更详细的文件名
    const analysisTypeForFile = (() => {
      switch (analysisType) {
        case 'Tissue/Cell Type':
          return 'tissue_cell_types';
        case 'Cell Line':
          return 'cell_lines';
        case 'Condition':
          return 'conditions';
        default:
          return 'analysis';
      }
    })();

    // 生成包含转录本ID、分析类型和指标的文件名
    const transcriptIdForFile = transcriptId || 'transcript';
    const metricForFile = displayName.toLowerCase();

    const fileName = `${transcriptIdForFile}_${metricForFile}_${analysisTypeForFile}_boxplot`.replace(/[^a-zA-Z0-9_]/g, '_') + '.png';

    link.download = fileName;
    link.href = canvas.toDataURL('image/png');
    link.click();
    
    URL.revokeObjectURL(svgUrl);
  };
  img.src = svgUrl;
};

// 获取特定数据类型的可用选项
export const getAvailableOptionsForDataType = (
  translationData: TranscriptData[],
  analysisType: string,
  dataType: DataType
): string[] => {
  const getConditionValue = (item: TranscriptData): string => {
    switch (analysisType) {
      case 'Tissue/Cell Type':
        return item.tissueCellType;
      case 'Cell Line':
        return item.cellLine;
      case 'Condition':
        return item.disease;
      default:
        return '';
    }
  };

  const options = new Set<string>();
  
  translationData.forEach(item => {
    let value: number | null = null;
    
    switch (dataType) {
      case 'te':
        value = item.te;
        break;
      case 'tr':
        value = item.tr;
        break;
      case 'evi':
        value = item.evi;
        break;
    }
    
    // 只有当该数据类型有有效值时，才包含该条件
    if (value !== null && value !== undefined && value > 0) {
      const conditionValue = getConditionValue(item);
      if (conditionValue) {
        options.add(conditionValue);
      }
    }
  });
  
  const optionsArray = Array.from(options);
  
  // 对于Condition分析类型，按疾病大类分组排序
  if (analysisType === 'Condition') {
    return optionsArray.sort((a, b) => {
      const groupA = getConditionGroup(a);
      const groupB = getConditionGroup(b);
      
      if (groupA !== groupB) {
        return groupA.localeCompare(groupB);
      }
      return a.localeCompare(b);
    });
  }
  
  // 其他分析类型使用默认排序
  return optionsArray.sort();
};

// 检查数据中是否存在有效的TR或EVI值
export const getAvailableDataTypes = (translationData: TranscriptData[]): string[] => {
  const types = ['TE']; // TE总是存在
  
  const hasTR = translationData.some(item => item.tr && item.tr > 0);
  const hasEVI = translationData.some(item => item.evi && item.evi > 0);
  
  if (hasTR) types.push('TR');
  if (hasEVI) types.push('EVI');
  
  return types;
};

// 准备箱线图数据
export const prepareBoxPlotData = (
  translationData: TranscriptData[],
  selectedTranscriptId: string,
  analysisType: string,
  dataType: DataType,
  selectedOptions: Set<string>
) => {
  const getConditionValue = (item: TranscriptData): string => {
    switch (analysisType) {
      case 'Tissue/Cell Type':
        return item.tissueCellType;
      case 'Cell Line':
        return item.cellLine;
      case 'Condition':
        return item.disease;
      default:
        return '';
    }
  };

  const boxPlotData: Array<{
    transcriptId: string;
    category: string;
    value: number;
    projectId: string;
  }> = [];
  
  const projectIdsSet = new Set<string>();

  translationData.forEach(item => {
    if (item.transcriptId !== selectedTranscriptId) return;

    const categoryValue = getConditionValue(item);
    if (!categoryValue) return;

    // 只包含用户选中的类别
    if (!selectedOptions.has(categoryValue)) return;

    let value: number | null = null;
    switch (dataType) {
      case 'te':
        value = item.te;
        break;
      case 'tr':
        value = item.tr;
        break;
      case 'evi':
        value = item.evi;
        break;
    }

    // 只有当前数据类型有有效值时才添加到ALL类别
    if (value && value > 0) {
      // ALL category - 只包含当前数据类型的有效数据
      boxPlotData.push({
        transcriptId: selectedTranscriptId,
        category: 'ALL',
        value: Math.log2(value),
        projectId: item.projectId
      });

      // Category only (without ProjectId) - 只包含当前数据类型的有效数据
      boxPlotData.push({
        transcriptId: selectedTranscriptId,
        category: categoryValue,
        value: Math.log2(value),
        projectId: item.projectId
      });

      projectIdsSet.add(item.projectId);
    }
  });

  return { 
    data: boxPlotData, 
    projectIds: Array.from(projectIdsSet).sort() 
  };
};