'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Download, ChevronDown } from 'lucide-react';
import { Button } from '@/app/components/ui/button';
import { TranscriptData, AnalysisType, DataType } from './types';
import { getAvailableOptionsForDataType, getAvailableDataTypes, downloadChartImage as downloadChart } from './utils';
import TissueCellTypeAnalysis from './TissueCellTypeAnalysis';
import CellLineAnalysis from './CellLineAnalysis';
import ConditionAnalysis from './ConditionAnalysis';

export default function TranscriptIndicesPage() {
  const [transcriptIds, setTranscriptIds] = useState<string[]>([]);
  const [selectedTranscriptId, setSelectedTranscriptId] = useState<string>('');
  const [customTranscriptId, setCustomTranscriptId] = useState<string>('');
  const [showTranscriptDropdown, setShowTranscriptDropdown] = useState(false);
  const [transcriptQuery, setTranscriptQuery] = useState('');
  const [translationData, setTranslationData] = useState<TranscriptData[]>([]);
  const [analysisType, setAnalysisType] = useState<AnalysisType>('Tissue/Cell Type');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [isClient, setIsClient] = useState<boolean>(false);

  // 针对 TE / TR / EVI 三种数据类型分别维护选中集合，互不干扰
  const initialSelectedMap: Record<DataType, Set<string>> = {
    te: new Set(),
    tr: new Set(),
    evi: new Set(),
  };
  const [selectedOptionsMap, setSelectedOptionsMap] = useState<Record<DataType, Set<string>>>(initialSelectedMap);
  const [showDownloadOptions, setShowDownloadOptions] = useState<boolean>(false);

  // 引用
  const transcriptInputRef = useRef<HTMLInputElement>(null);
  const transcriptDropdownRef = useRef<HTMLDivElement>(null);
  const debounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  // 防抖搜索函数
  const debouncedSearch = useCallback((query: string) => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    
    debounceTimeout.current = setTimeout(() => {
      setTranscriptQuery(query);
    }, 300);
  }, []);

  // 过滤后的转录本ID（只显示匹配的前50个）
  const filteredTranscriptOptions = useMemo(() => {
    if (!transcriptQuery) return transcriptIds.slice(0, 20);
    return transcriptIds
      .filter(id => id.toLowerCase().includes(transcriptQuery.toLowerCase()))
      .slice(0, 20);
  }, [transcriptIds, transcriptQuery]);

  // 设置客户端状态
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化加载所有转录本ID
  useEffect(() => {
    const loadAllTranscriptIds = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/transcriptinfo/all-transcript-ids`);
        if (!response.ok) {
          throw new Error('Failed to fetch transcript IDs');
        }
        const data = await response.json();
        // 排序所有数据
        const sortedData = data.sort();
        setTranscriptIds(sortedData);
        // 初始不设置过滤，让useMemo处理
        if (sortedData.length > 0) {
          setSelectedTranscriptId(sortedData[0]);
          setCustomTranscriptId(sortedData[0]);
        }
      } catch (error) {
        console.error('Error loading transcript IDs:', error);
        setError('Failed to load transcript IDs');
      }
    };

    loadAllTranscriptIds();
  }, []);

  // 加载转录本翻译数据
  const loadTranslationData = async (transcriptId: string) => {
    if (!transcriptId) return;

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/translation-indices/transcriptId/${transcriptId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch translation data');
      }
      const data = await response.json();
      setTranslationData(data);
    } catch (error) {
      console.error('Error loading translation data:', error);
      setError('Failed to load translation data');
    } finally {
      setIsLoading(false);
    }
  };

  // 根据当前 translationData & analysisType 初始化 / 更新各数据类型的默认选中集合（全部选中）
  useEffect(() => {
    if (translationData.length === 0) return;

    const newMap: Record<DataType, Set<string>> = { te: new Set(), tr: new Set(), evi: new Set() };

    (['te', 'tr', 'evi'] as DataType[]).forEach((dt) => {
      const opts = getAvailableOptionsForDataType(translationData, analysisType, dt);
      opts.forEach((opt: string) => newMap[dt].add(opt));
    });

    setSelectedOptionsMap(newMap);
  }, [translationData, analysisType]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (transcriptDropdownRef.current && !transcriptDropdownRef.current.contains(event.target as Node) &&
          transcriptInputRef.current && !transcriptInputRef.current.contains(event.target as Node)) {
        setShowTranscriptDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 自定义滚动条功能 - 支持多个图表
  useEffect(() => {
    const setupScrollbar = (dataType: string) => {
      const boxplotContainer = document.getElementById(`boxplot-container-${dataType}`);
      const scrollbarThumb = document.getElementById(`custom-scrollbar-thumb-${dataType}`);
      const scrollbarTrack = document.getElementById(`custom-scrollbar-track-${dataType}`);
      
      if (!boxplotContainer || !scrollbarThumb || !scrollbarTrack) {
        return null;
      }

      let isDragging = false;
      let startX = 0;
      let startScrollLeft = 0;

      // 计算滚动条thumb的位置和大小
      const updateScrollbar = () => {
        const containerWidth = boxplotContainer.clientWidth;
        const scrollWidth = boxplotContainer.scrollWidth;
        const scrollLeft = boxplotContainer.scrollLeft;
        
        if (scrollWidth <= containerWidth) {
          scrollbarThumb.style.display = 'none';
          return;
        }
        
        scrollbarThumb.style.display = 'block';
        
        // 计算thumb的宽度（比例）
        const trackWidth = scrollbarTrack.clientWidth;
        const thumbWidth = Math.max((containerWidth / scrollWidth) * trackWidth, 30);
        scrollbarThumb.style.width = `${thumbWidth}px`;
        
        // 计算thumb的位置
        const maxThumbLeft = trackWidth - thumbWidth;
        const maxScrollLeft = scrollWidth - containerWidth;
        if (maxScrollLeft > 0) {
          const scrollRatio = scrollLeft / maxScrollLeft;
          const thumbLeft = scrollRatio * maxThumbLeft;
          scrollbarThumb.style.left = `${thumbLeft}px`;
        }
      };

      // 拖拽开始
      const handleMouseDown = (e: MouseEvent) => {
        isDragging = true;
        startX = e.clientX;
        startScrollLeft = boxplotContainer.scrollLeft;
        scrollbarThumb.style.cursor = 'grabbing';
        e.preventDefault();
      };

      // 拖拽中
      const handleMouseMove = (e: MouseEvent) => {
        if (!isDragging) return;

        const deltaX = e.clientX - startX;
        const trackWidth = scrollbarTrack.clientWidth;
        const thumbWidth = parseFloat(scrollbarThumb.style.width) || 30;
        const maxThumbLeft = trackWidth - thumbWidth;
        
        if (maxThumbLeft > 0) {
          const containerWidth = boxplotContainer.clientWidth;
          const scrollWidth = boxplotContainer.scrollWidth;
          const maxScrollLeft = scrollWidth - containerWidth;
          
          if (maxScrollLeft > 0) {
            const scrollRatio = deltaX / maxThumbLeft;
            const newScrollLeft = startScrollLeft + scrollRatio * maxScrollLeft;
            boxplotContainer.scrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));
          }
        }
      };

      // 拖拽结束
      const handleMouseUp = () => {
        isDragging = false;
        scrollbarThumb.style.cursor = 'grab';
      };

      // 点击轨道
      const handleTrackClick = (e: MouseEvent) => {
        if (e.target === scrollbarThumb) return;
        
        const rect = scrollbarTrack.getBoundingClientRect();
        const clickX = e.clientX - rect.left;
        const trackWidth = scrollbarTrack.clientWidth;
        const thumbWidth = parseFloat(scrollbarThumb.style.width) || 30;
        
        const containerWidth = boxplotContainer.clientWidth;
        const scrollWidth = boxplotContainer.scrollWidth;
        const maxScrollLeft = scrollWidth - containerWidth;
        const maxThumbLeft = trackWidth - thumbWidth;
        
        if (maxThumbLeft > 0 && maxScrollLeft > 0) {
          const targetThumbLeft = Math.max(0, Math.min(clickX - thumbWidth / 2, maxThumbLeft));
          const scrollRatio = targetThumbLeft / maxThumbLeft;
          boxplotContainer.scrollLeft = scrollRatio * maxScrollLeft;
        }
      };

      // 容器滚动时更新滚动条
      const handleScroll = () => {
        requestAnimationFrame(updateScrollbar);
      };

      // 窗口大小改变时更新滚动条
      const handleResize = () => {
        requestAnimationFrame(updateScrollbar);
      };

      // 绑定事件
      scrollbarThumb.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      scrollbarTrack.addEventListener('click', handleTrackClick);
      boxplotContainer.addEventListener('scroll', handleScroll);
      window.addEventListener('resize', handleResize);

      // 初始化滚动条 - 增加延迟确保DOM完全渲染和数据加载完成
      const initTimer = setTimeout(updateScrollbar, 200);
      // 再次检查以防第一次初始化失败
      const recheckTimer = setTimeout(updateScrollbar, 500);

      // 添加MutationObserver来监听DOM变化
      const observer = new MutationObserver(() => {
        setTimeout(updateScrollbar, 50);
      });
      
      observer.observe(boxplotContainer, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
      });

      // 返回清理函数
      return () => {
        clearTimeout(initTimer);
        clearTimeout(recheckTimer);
        observer.disconnect();
        scrollbarThumb.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        scrollbarTrack.removeEventListener('click', handleTrackClick);
        boxplotContainer.removeEventListener('scroll', handleScroll);
        window.removeEventListener('resize', handleResize);
      };
    };

    const availableTypes = getAvailableDataTypes(translationData);
    const cleanupFunctions: (() => void)[] = [];

    // 为每个图表设置滚动条
    availableTypes.forEach((type) => {
      const dataType = type.toLowerCase();
      const cleanup = setupScrollbar(dataType);
      if (cleanup) {
        cleanupFunctions.push(cleanup);
      }
    });

    // 清理所有事件监听器
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [translationData, selectedOptionsMap, selectedTranscriptId]); // 当数据、选项或转录本变化时重新初始化滚动条

  // 处理输入框变化
  const handleTranscriptInputChange = (value: string) => {
    setCustomTranscriptId(value);
    // 触发防抖搜索
    debouncedSearch(value);
    // 显示下拉框
    setShowTranscriptDropdown(value.length > 0);
  };

  // 处理Submit
  const handleSubmit = () => {
    const transcriptIdToUse = customTranscriptId || selectedTranscriptId;
    if (transcriptIdToUse) {
      setSelectedTranscriptId(transcriptIdToUse);
    }
  };

  // 处理Reset - 只恢复选择框到默认值
  const handleReset = () => {
    // 恢复到页面初始状态的默认值
    setCustomTranscriptId(transcriptIds.length > 0 ? transcriptIds[0] : '');
    setSelectedTranscriptId(transcriptIds.length > 0 ? transcriptIds[0] : '');
    setAnalysisType('Tissue/Cell Type');
    setShowTranscriptDropdown(false);
    setError('');
  };

  // 处理选项选择
  const handleOptionSelect = (dataType: DataType, option: string, isChecked: boolean) => {
    setSelectedOptionsMap((prev) => {
      const newSet = new Set(prev[dataType]);
      if (isChecked) {
        newSet.add(option);
      } else {
        newSet.delete(option);
      }
      return { ...prev, [dataType]: newSet };
    });
  };

  // 下载图表为图片
  const downloadChartImage = (dataType: DataType, displayName: string) => {
    downloadChart(dataType, displayName, selectedTranscriptId, analysisType);
  };

  // 下载数据为CSV或JSON格式
  const downloadData = (format: 'csv' | 'json') => {
    if (translationData.length === 0) return;

    const filename = `transcript_${selectedTranscriptId}_${analysisType.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}_data`;

    if (format === 'csv') {
      // CSV转义函数：处理包含特殊字符的值，确保符合RFC 4180标准
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);

        // 如果包含逗号、双引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }

        return str;
      };

      // CSV格式
      const headers = ['transcriptId', 'projectId', 'bioprojectId', 'tissueCellType', 'cellLine', 'disease', 'te', 'tr', 'evi', 'geneId', 'geneSymbol'];
      const csvContent = [headers.join(',')];

      translationData.forEach(item => {
        const row = [
          escapeCsvValue(item.transcriptId),
          escapeCsvValue(item.projectId),
          escapeCsvValue(item.bioprojectId),
          escapeCsvValue(item.tissueCellType),
          escapeCsvValue(item.cellLine),
          escapeCsvValue(item.disease),
          escapeCsvValue(item.te),
          escapeCsvValue(item.tr || ''),
          escapeCsvValue(item.evi || ''),
          escapeCsvValue(item.geneId),
          escapeCsvValue(item.geneSymbol)
        ];
        csvContent.push(row.join(','));
      });

      const blob = new Blob([csvContent.join('\n')], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${filename}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    } else {
      // JSON格式
      const jsonContent = JSON.stringify(translationData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${filename}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
    
    setShowDownloadOptions(false);
  };

  // 当转录本 ID 改变时重新拉取数据
  useEffect(() => {
    if (selectedTranscriptId) {
      loadTranslationData(selectedTranscriptId);
    }
  }, [selectedTranscriptId]);

  // 渲染分析组件
  const renderAnalysisComponent = () => {
    const commonProps = {
      translationData,
      selectedOptionsMap,
      setSelectedOptionsMap,
      selectedTranscriptId,
      handleOptionSelect,
      downloadChartImage,
      getAvailableOptionsForDataType
    };

    switch (analysisType) {
      case 'Tissue/Cell Type':
        return <TissueCellTypeAnalysis {...commonProps} />;
      case 'Cell Line':
        return <CellLineAnalysis {...commonProps} />;
      case 'Condition':
        return <ConditionAnalysis {...commonProps} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span style={{ color: '#337ab7' }}>TE/TR/EVI of Transcripts across Biological Contexts</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
          {/* 标签页 */}
          <div className="mb-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="flex">
                {(['Tissue/Cell Type', 'Cell Line', 'Condition'] as AnalysisType[]).map((type) => (
                  <button
                    key={type}
                    onClick={() => setAnalysisType(type)}
                    className={`flex-1 py-3 font-medium text-sm border-r border-gray-200 last:border-r-0 text-center transition-colors ${
                      analysisType === type
                        ? 'bg-[#267DD4] text-white'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    TE/TR/EVI in {type}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* 说明文字 */}
          <div className="mb-6 text-gray-700 text-sm leading-relaxed">
            {(() => {
              switch (analysisType) {
                case 'Tissue/Cell Type':
                  return 'The following figures show the box plot of TE, TR and EVI values (if available) for the input transcript across different tissues/cell types, with distinct points representing different datasets. The left panel provides checkbox filters for selecting tissues/cell types of interest to compare.';
                case 'Cell Line':
                  return 'The following figures show the box plot of TE, TR and EVI values (if available) for the input transcript across different cell lines, with distinct points representing different datasets. The left panel provides checkbox filters for selecting cell lines of interest to compare.';
                case 'Condition':
                  return 'The following figures show the box plot of TE, TR and EVI values (if available) for the input transcript across different conditions, with distinct points representing different datasets. The left panel provides checkbox filters for selecting conditions of interest to compare.';
                default:
                  return '';
              }
            })()}
          </div>

          {/* 转录本选择区域 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between">
              <div></div> {/* 左侧空白 */}
              <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700 whitespace-nowrap">Transcript ID:</label>
                <div className="relative">
                  <input
                    ref={transcriptInputRef}
                    type="text"
                    placeholder="Transcript ID"
                    value={customTranscriptId}
                    onChange={(e) => {
                      const value = e.target.value;
                      handleTranscriptInputChange(value);
                    }}
                    onFocus={() => {
                      if (transcriptIds.length > 0) {
                        setShowTranscriptDropdown(true);
                      }
                    }}
                    className="w-64 h-10 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
                  />
                  <div 
                    className="absolute inset-y-0 right-0 flex items-center pr-2 cursor-pointer"
                    onClick={() => {
                      if (transcriptInputRef.current) {
                        transcriptInputRef.current.focus();
                        if (transcriptIds.length > 0) {
                          setShowTranscriptDropdown(true);
                        }
                      }
                    }}
                  >
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>

                  {/* 下拉选项 */}
                  {showTranscriptDropdown && filteredTranscriptOptions.length > 0 && (
                    <div 
                      ref={transcriptDropdownRef}
                      className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
                    >
                      {filteredTranscriptOptions.map((id) => (
                        <div
                          key={id}
                          className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                          onClick={() => {
                            setCustomTranscriptId(id);
                            setShowTranscriptDropdown(false);
                          }}
                        >
                          {id}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

                {/* Submit和Reset按钮 */}
                <div className="flex gap-3">
                  <Button
                    onClick={handleSubmit}
                    disabled={isLoading}
                    className={`btn-submit ${isLoading ? 'loading' : ''}`}
                  >
                    {isLoading ? 'Loading...' : 'Submit'}
                  </Button>
                  <Button
                    onClick={handleReset}
                    variant="outline"
                    className="btn-reset"
                  >
                    Reset
                  </Button>
                </div>
              </div>
              
              {/* 右侧下载按钮 */}
              {translationData.length > 0 && (
                <div className="relative">
                  <button
                    onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                    className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  
                  {showDownloadOptions && (
                    <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-300 rounded-lg shadow-lg z-50">
                      <div className="py-1">
                        <button
                          onClick={() => downloadData('csv')}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                        >
                          Download as CSV
                        </button>
                        <button
                          onClick={() => downloadData('json')}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                        >
                          Download as JSON
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* 渲染分析组件 */}
          {isClient && translationData.length > 0 && renderAnalysisComponent()}

          {/* 加载中状态 */}
          {isLoading && (
            <div className="text-center py-8">
              <p className="text-gray-600">Loading...</p>
            </div>
          )}

          {/* 无数据状态 */}
          {!isLoading && !error && translationData.length === 0 && selectedTranscriptId && (
            <div className="text-center py-8">
              <p className="text-gray-600">No data found for the selected transcript ID.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}