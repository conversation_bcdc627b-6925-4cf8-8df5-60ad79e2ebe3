import React from 'react';
import { Download } from 'lucide-react';
import * as d3 from 'd3';
import { TranscriptData, DataType, AnalysisComponentProps, AnalysisType } from './types';
import { macaronColors, prepareBoxPlotData, downloadChartImage as downloadChart } from './utils';
import { CONDITION_GROUPS, CONDITION_GROUP_COLORS } from './ConditionAnalysis';

interface BaseAnalysisComponentProps extends AnalysisComponentProps {
  analysisType: AnalysisType;
  getConditionValue: (item: TranscriptData) => string;
  organizeConditionsByGroup?: (conditions: string[]) => {[key: string]: string[]};
}

export default function BaseAnalysisComponent({
  translationData,
  selectedOptionsMap,
  setSelectedOptionsMap,
  selectedTranscriptId,
  handleOptionSelect,
  downloadChartImage: _, // eslint-disable-line @typescript-eslint/no-unused-vars
  getAvailableOptionsForDataType,
  analysisType,
  getConditionValue: __, // eslint-disable-line @typescript-eslint/no-unused-vars
  organizeConditionsByGroup
}: BaseAnalysisComponentProps) {

  // 渲染单个箱线图
  const renderSingleBoxPlot = (dataType: DataType, displayName: string) => {
    const { data, projectIds } = prepareBoxPlotData(
      translationData,
      selectedTranscriptId,
      analysisType,
      dataType,
      selectedOptionsMap[dataType]
    );
    
    // 为当前数据类型获取特定的可用选项
    const typeSpecificOptions = getAvailableOptionsForDataType(translationData, analysisType, dataType);
    
    // 检查当前选择的选项中哪些对当前数据类型有效
    const selectedOptions = selectedOptionsMap[dataType];
    const validSelectedOptions = Array.from(selectedOptions).filter((option: string) => 
      typeSpecificOptions.includes(option)
    );
    
    const isTypeSelectAll = validSelectedOptions.length === typeSpecificOptions.length;

    // 只显示用户选中的类别，如果没有选中任何类别则不显示图表
    const categories = Array.from(selectedOptions).filter((category: string) =>
      category !== 'ALL'
    ).sort((a, b) => {
      // 对于Condition分析类型，按疾病大类分组排序
      if (analysisType === 'Condition') {
        // 使用导入的疾病分组函数
        const getConditionGroup = (condition: string): string => {
          for (const [groupName, conditions] of Object.entries(CONDITION_GROUPS)) {
            if (conditions.includes(condition)) {
              return groupName;
            }
          }
          return 'Normal';
        };
        
        const groupA = getConditionGroup(a);
        const groupB = getConditionGroup(b);
        
        if (groupA !== groupB) {
          return groupA.localeCompare(groupB);
        }
        return a.localeCompare(b);
      }
      
      // 其他分析类型使用默认排序
      return a.localeCompare(b);
    });

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6" key={dataType}>
        <div className="flex" style={{ height: '600px' }}>
          {/* 左侧过滤器 - 动态宽度 */}
          <div className={`${organizeConditionsByGroup ? 'w-80' : 'w-64'} pr-3 border-r border-gray-200 flex flex-col`}>
            {/* 标题和全选 */}
            <div className="mb-3 pb-2 border-b border-gray-200">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isTypeSelectAll}
                  onChange={(e) => {
                    setSelectedOptionsMap((prev) => {
                      const newSet = new Set(prev[dataType]);
                      if (e.target.checked) {
                        typeSpecificOptions.forEach((opt: string) => newSet.add(opt));
                      } else {
                        typeSpecificOptions.forEach((opt: string) => newSet.delete(opt));
                      }
                      return { ...prev, [dataType]: newSet };
                    });
                  }}
                  className="rounded border-gray-300 text-[#337ab7] focus:ring-[#337ab7]"
                  style={{ transform: 'scale(0.9)' }}
                />
                <span className="ml-2 text-sm font-medium text-gray-900">ALL</span>
              </label>
            </div>

            {/* 条件选项 - 根据是否有分组功能决定布局 */}
            <div className="flex-1 overflow-y-auto pr-1">
              {organizeConditionsByGroup ? (
                /* 分组布局 */
                <div className="space-y-4">
                  {Object.entries(organizeConditionsByGroup(typeSpecificOptions)).map(([groupName, groupConditions]) => (
                    <div key={groupName} className="space-y-2">
                      {/* 分组标题 */}
                      <div className="font-medium text-sm text-gray-800 border-b border-gray-200 pb-1">
                        {groupName}
                      </div>
                      {/* 分组内的条件 */}
                      <div className="space-y-1 ml-4">
                        {groupConditions.map((option: string) => (
                          <label key={option} className="flex items-start py-1 px-1 hover:bg-blue-50 rounded text-xs cursor-pointer">
                            <input
                              type="checkbox"
                              checked={selectedOptions.has(option)}
                              onChange={(e) => handleOptionSelect(dataType, option, e.target.checked)}
                              className="rounded border-gray-300 text-[#337ab7] focus:ring-[#337ab7] mt-0.5 mr-2 flex-shrink-0"
                              style={{ transform: 'scale(0.8)' }}
                            />
                            <span className="text-gray-700 leading-tight break-words text-xs">{option}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                /* 默认单列布局 */
                <div className="grid grid-cols-1 gap-0.5">
                  {typeSpecificOptions.map((option: string) => (
                    <label key={option} className="flex items-start py-1 px-1 hover:bg-blue-50 rounded text-xs cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedOptions.has(option)}
                        onChange={(e) => handleOptionSelect(dataType, option, e.target.checked)}
                        className="rounded border-gray-300 text-[#337ab7] focus:ring-[#337ab7] mt-0.5 mr-2 flex-shrink-0"
                        style={{ transform: 'scale(0.8)' }}
                      />
                      <span className="text-gray-700 leading-tight break-words text-xs">{option}</span>
                    </label>
                  ))}
                </div>
              )}
            </div>
            
            {/* 统计信息 */}
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                Selected: {validSelectedOptions.length} / {typeSpecificOptions.length}
              </div>
            </div>
          </div>

          {/* 箱线图和图例区域 - 优化布局 */}
          <div className="flex-1 pl-3 min-w-0 flex flex-col">
            <div className="mb-3 flex items-center justify-between">
              <div></div> {/* 左侧空白 */}
              <h3 className="font-bold text-gray-900 text-center text-lg flex-1">
                {displayName} of {selectedTranscriptId} in different {(() => {
                  const analysisLabelMap: Record<string, string> = {
                    'Tissue/Cell Type': 'tissues/cell types',
                    'Cell Line': 'cell lines',
                    'Condition': 'conditions'
                  };
                  return analysisLabelMap[analysisType] || (analysisType || 'context').toLowerCase() + 's';
                })()}
              </h3>
              <button
                onClick={() => {
                  // 与右侧图例一致：仅包含有数据且被选中的类别
                  const categoriesWithData = Array.from(new Set(data.map(d => d.category))).filter(c => c !== 'ALL');
                  const displayCategories = Array.from(selectedOptionsMap[dataType]).filter((opt: string) => opt !== 'ALL' && categoriesWithData.includes(opt));

                  downloadChart(
                    dataType,
                    displayName,
                    selectedTranscriptId,
                    analysisType,
                    displayCategories,
                    projectIds
                  );
                }}
                className="bg-white hover:bg-gray-100 border border-gray-300 text-gray-700 p-2 rounded-md transition-colors"
                title="Download chart as image"
              >
                <Download className="h-4 w-4" />
              </button>
            </div>
            
            <div className="flex-1 border border-gray-300 rounded-lg bg-white overflow-hidden">
              <div className="h-full p-3 flex flex-col">
                {/* 图表和图例容器 */}
                <div 
                  id={`boxplot-container-${dataType}`}
                  className="flex-1 border border-gray-200 rounded-t relative"
                  style={{ 
                    overflowX: 'auto',
                    overflowY: 'auto',
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none'
                  }}
                >
                  <style jsx>{`
                    #boxplot-container-${dataType}::-webkit-scrollbar {
                      display: none;
                    }
                  `}</style>
                  <div className="flex">
                    {/* 箱线图区域 */}
                    <div className="flex-shrink-0 ml-6">
                      <div
                        ref={(node) => {
                          if (node) {
                            d3.select(node).selectAll("*").remove();

                            // 设置图表尺寸和边距 - 紧凑布局
                            const margin = { top: 30, right: 60, bottom: 80, left: 50 };
                            const fixedCategoryWidth = 70;
                            // 当没有选中类别时，使用足够的宽度显示提示信息
                            const width = categories.length > 0
                              ? Math.max(500, categories.length * fixedCategoryWidth + margin.left + margin.right)
                              : 800;
                            const height = 450;

                            const innerHeight = height - margin.top - margin.bottom;

                            // 创建SVG
                            const svg = d3.select(node)
                              .append("svg")
                              .attr("width", width)
                              .attr("height", height);

                            const g = svg.append("g")
                              .attr("transform", `translate(${margin.left},${margin.top})`);

                            // 计算Y轴范围
                            let yMin, yMax;
                            if (categories.length > 0 && data.length > 0) {
                              // 如果有选中类别且有数据，基于实际数据计算范围
                              const filteredData = data.filter(d => categories.includes(d.category));
                              const allValues = filteredData.map(d => d.value);
                              yMin = d3.min(allValues) || 0;
                              yMax = d3.max(allValues) || 0;
                            } else {
                              // 如果没有选中类别或没有数据，使用默认范围
                              yMin = 0;
                              yMax = 1;
                            }

                            // 只有在有选中类别时才绘制坐标轴和数据
                            if (categories.length > 0) {
                              const yScale = d3.scaleLinear()
                                .domain([yMin - 0.1, yMax + 0.1])
                                .nice()
                                .range([innerHeight, 0]);

                              // 绘制Y轴
                              g.append("g")
                                .call(d3.axisLeft(yScale))
                                .style("font-size", "10px");

                              // Y轴标签
                              g.append("text")
                                .attr("transform", "rotate(-90)")
                                .attr("y", 0 - margin.left)
                                .attr("x", 0 - (innerHeight / 2))
                                .attr("dy", "1em")
                                .style("text-anchor", "middle")
                                .style("font-size", "12px")
                                .style("font-weight", "bold")
                                .text(`log2(${displayName})`);

                              // 创建X轴比例尺
                              const xScale = d3.scaleBand()
                                .domain(categories)
                                .range([0, categories.length * fixedCategoryWidth])
                                .padding(0.3);

                              // 绘制X轴
                              g.append("g")
                                .attr("transform", `translate(0,${innerHeight})`)
                                .call(d3.axisBottom(xScale))
                                .selectAll("text")
                                .attr("transform", "rotate(30)")
                                .style("text-anchor", "start")
                                .style("font-size", "9px");

                            // 绘制箱线图
                            categories.forEach((category) => {
                              const categoryData = data.filter(d => d.category === category);
                              if (categoryData.length === 0) return;

                              const values = categoryData.map(d => d.value).sort(d3.ascending);
                              const q1 = d3.quantile(values, 0.25) || 0;
                              const median = d3.quantile(values, 0.5) || 0;
                              const q3 = d3.quantile(values, 0.75) || 0;
                              const iqr = q3 - q1;
                              const min = Math.max(q1 - 1.5 * iqr, d3.min(values) || 0);
                              const max = Math.min(q3 + 1.5 * iqr, d3.max(values) || 0);

                              const x = xScale(category) || 0;
                              const boxWidth = xScale.bandwidth();

                              // 箱体颜色 - 与图例保持一致，使用马卡龙色系
                              const categoryIndex = categories.indexOf(category);
                              const color = macaronColors[categoryIndex % macaronColors.length];

                              // 绘制箱体
                              g.append("rect")
                                .attr("x", x)
                                .attr("y", yScale(q3))
                                .attr("width", boxWidth)
                                .attr("height", yScale(q1) - yScale(q3))
                                .attr("fill", color)
                                .attr("stroke", "black")
                                .attr("stroke-width", 1);

                              // 中位数线
                              g.append("line")
                                .attr("x1", x)
                                .attr("x2", x + boxWidth)
                                .attr("y1", yScale(median))
                                .attr("y2", yScale(median))
                                .attr("stroke", "black")
                                .attr("stroke-width", 2);

                              // 上下须
                              g.append("line")
                                .attr("x1", x + boxWidth/2)
                                .attr("x2", x + boxWidth/2)
                                .attr("y1", yScale(q3))
                                .attr("y2", yScale(max))
                                .attr("stroke", "black");

                              g.append("line")
                                .attr("x1", x + boxWidth/2)
                                .attr("x2", x + boxWidth/2)
                                .attr("y1", yScale(q1))
                                .attr("y2", yScale(min))
                                .attr("stroke", "black");

                              // 上下端点
                              g.append("line")
                                .attr("x1", x + boxWidth*0.25)
                                .attr("x2", x + boxWidth*0.75)
                                .attr("y1", yScale(max))
                                .attr("y2", yScale(max))
                                .attr("stroke", "black");

                              g.append("line")
                                .attr("x1", x + boxWidth*0.25)
                                .attr("x2", x + boxWidth*0.75)
                                .attr("y1", yScale(min))
                                .attr("y2", yScale(min))
                                .attr("stroke", "black");

                              // 绘制数据点
                              categoryData.forEach(d => {
                                const projectIndex = projectIds.indexOf(d.projectId);
                                const pointColor = macaronColors[projectIndex % macaronColors.length];

                                const dataIndex = categoryData.indexOf(d);
                                const offsetX = ((dataIndex % 5) - 2) * boxWidth * 0.06;
                                g.append("circle")
                                  .attr("cx", x + boxWidth/2 + offsetX)
                                  .attr("cy", yScale(d.value))
                                  .attr("r", 2.5)
                                  .attr("fill", pointColor)
                                  .attr("stroke", "black")
                                  .attr("stroke-width", 0.5)
                                  .append("title")
                                  .text(`${d.category}: ${d.value.toFixed(3)} (${d.projectId})`);
                              });
                            });
                            } else {
                              // 当没有选中类别时，显示提示信息
                              svg.append("text")
                                .attr("x", width / 2)
                                .attr("y", height / 2)
                                .attr("text-anchor", "middle")
                                .attr("dominant-baseline", "central")
                                .attr("font-size", "16px")
                                .attr("font-family", "Arial, sans-serif")
                                .attr("fill", "#6b7280")
                                .text("Please select at least one condition from the left panel to view the boxplot");
                            }
                          }
                        }}
                      />
                    </div>

                    {/* 右侧图例 - 只在有选中类别时显示 */}
                    {categories.length > 0 && (
                      <div className="pl-6 border-gray-200 flex-shrink-0 overflow-y-auto">
                        <div className="flex gap-8">
                          {/* Cell_Name 列表 */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2 text-xs">
                              {(() => {
                                const analysisLabelMap: Record<string, string> = {
                                  'Tissue/Cell Type': 'Tissue/Cell Type',
                                  'Cell Line': 'Cell Line',
                                  'Condition': 'Condition'
                                };
                                return analysisLabelMap[analysisType] || (analysisType || 'context');
                              })()}
                            </h4>
                          <div className="flex gap-x-4">
                            {(() => {
                              const categoriesWithData = Array.from(new Set(data.map(d => d.category))).filter((c) => c !== 'ALL');
                              const displayCategories = Array.from(selectedOptionsMap[dataType]).filter((opt) => opt !== 'ALL' && categoriesWithData.includes(opt));

                              const columns: string[][] = [];
                              const maxItemsPerColumn = 20;

                              if (displayCategories.length > 0) {
                                  for (let i = 0; i < displayCategories.length; i += maxItemsPerColumn) {
                                      columns.push(displayCategories.slice(i, i + maxItemsPerColumn));
                                  }
                              }

                              return columns.map((col, colIdx) => (
                                <div key={colIdx} className="space-y-1">
                                  {col.map((cellName: string) => {
                                    // 使用与箱线图相同的颜色分配逻辑 - 基于在categories中的索引
                                    const categoryIndex = categories.indexOf(cellName);
                                    const color = macaronColors[categoryIndex % macaronColors.length];
                                    return (
                                      <div key={cellName} className="flex items-center whitespace-nowrap">
                                        <div className="w-3 h-2 mr-1.5 border border-black flex-shrink-0" style={{ backgroundColor: color }}></div>
                                        <span className="text-xs text-gray-700 leading-tight">{cellName}</span>
                                      </div>
                                    );
                                  })}
                                </div>
                              ));
                            })()}
                          </div>
                        </div>

                        {/* Dataset Id 列表 */}
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 text-xs">Dataset ID</h4>
                          <div className="flex gap-x-4">
                            {(() => {
                              const columns: string[][] = [];
                              const maxItemsPerColumn = 20;

                              if (projectIds.length > 0) {
                                  for (let i = 0; i < projectIds.length; i += maxItemsPerColumn) {
                                      columns.push(projectIds.slice(i, i + maxItemsPerColumn));
                                  }
                              }

                              return columns.map((col, colIdx) => (
                                <div key={colIdx} className="space-y-1">
                                  {col.map((projectId: string) => {
                                    // 使用与数据点相同的颜色分配逻辑 - 基于在projectIds中的索引
                                    const projectIndex = projectIds.indexOf(projectId);
                                    const color = macaronColors[projectIndex % macaronColors.length];
                                    return (
                                      <div key={projectId} className="flex items-center whitespace-nowrap">
                                        <div className="w-2.5 h-2.5 rounded-full mr-1.5 flex-shrink-0" style={{ backgroundColor: color }}></div>
                                        <span className="text-xs text-gray-700 leading-tight">{projectId}</span>
                                      </div>
                                    );
                                  })}
                                </div>
                              ));
                            })()}
                          </div>
                        </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 自定义滚动条 - 只在有选中类别时显示 */}
                {categories.length > 0 && (
                  <div className="bg-gray-100 h-4 rounded-b flex items-center px-2 border-t border-gray-200">
                    <div 
                      id={`custom-scrollbar-track-${dataType}`}
                      className="flex-1 bg-gray-200 h-2 rounded-full cursor-pointer relative"
                    >
                      <div 
                        id={`custom-scrollbar-thumb-${dataType}`}
                        className="bg-gray-400 h-2 rounded-full absolute top-0 cursor-grab hover:bg-gray-500 transition-colors"
                        style={{ minWidth: '30px' }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* 根据数据可用性渲染相应的箱线图 */}
      {renderSingleBoxPlot('te', 'TE')}
      {translationData.some(item => item.tr && item.tr > 0) && renderSingleBoxPlot('tr', 'TR')}
      {translationData.some(item => item.evi && item.evi > 0) && renderSingleBoxPlot('evi', 'EVI')}
    </div>
  );
}