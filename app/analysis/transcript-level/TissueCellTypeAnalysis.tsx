import React from 'react';
import BaseAnalysisComponent from './BaseAnalysisComponent';
import { AnalysisComponentProps, TranscriptData } from './types';

export default function TissueCellTypeAnalysis(props: AnalysisComponentProps) {
  const getConditionValue = (item: TranscriptData) => item.tissueCellType;

  return (
    <BaseAnalysisComponent
      {...props}
      analysisType="Tissue/Cell Type"
      getConditionValue={getConditionValue}
    />
  );
}