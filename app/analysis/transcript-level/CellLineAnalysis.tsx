import React from 'react';
import BaseAnalysisComponent from './BaseAnalysisComponent';
import { AnalysisComponentProps, TranscriptData } from './types';

export default function CellLineAnalysis(props: AnalysisComponentProps) {
  const getConditionValue = (item: TranscriptData) => item.cellLine;

  return (
    <BaseAnalysisComponent
      {...props}
      analysisType="Cell Line"
      getConditionValue={getConditionValue}
    />
  );
}