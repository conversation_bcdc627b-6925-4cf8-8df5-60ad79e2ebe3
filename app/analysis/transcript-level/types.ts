export interface TranscriptData {
  id: number;
  transcriptId: string;
  projectId: string;
  bioprojectId: string;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  tr: number | null;
  evi: number | null;
  te: number;
  geneId: string;
  geneSymbol: string;
}

export type AnalysisType = 'Tissue/Cell Type' | 'Cell Line' | 'Condition';
export type DataType = 'te' | 'tr' | 'evi';

export interface BoxPlotData {
  transcriptId: string;
  category: string;
  value: number;
  projectId: string;
}

export interface AnalysisComponentProps {
  translationData: TranscriptData[];
  selectedOptionsMap: Record<DataType, Set<string>>;
  setSelectedOptionsMap: React.Dispatch<React.SetStateAction<Record<DataType, Set<string>>>>;
  selectedTranscriptId: string;
  handleOptionSelect: (dataType: DataType, option: string, isChecked: boolean) => void;
  downloadChartImage: (dataType: DataType, displayName: string) => void;
  getAvailableOptionsForDataType: (translationData: TranscriptData[], analysisType: string, dataType: DataType) => string[];
}