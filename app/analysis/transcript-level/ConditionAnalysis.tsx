import React from 'react';
import BaseAnalysisComponent from './BaseAnalysisComponent';
import { AnalysisComponentProps, TranscriptData } from './types';

// 疾病大类与condition的映射关系
export const CONDITION_GROUPS = {
  "Infections": [
    "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
    "Adult Hepatocellular Carcinoma; HCV Transfection",
    "B95-8 Epstein-Barr Virus Infection",
    "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
    "Human Cytomegalovirus Infection",
    "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
    "IAV Transfection",
    "Lung Adenocarcinoma; IAV Infection",
    "Lung Adenocarcinoma; SARS-CoV-2 Infection",
    "M81 Epstein-Barr Virus Infection",
    "Toxoplasma Infection"
  ],
  "Gastrointestinal System Cancer": [
    "Adult Hepatocellular Carcinoma",
    "Childhood Hepatocellular Carcinoma",
    "Colon Adenocarcinoma",
    "Colon Carcinoma",
    "Esophageal Squamous Cell Carcinoma",
    "Hepatoblastoma",
    "Hepatocellular Carcinoma",
    "Intrahepatic Cholangiocarcinoma"
  ],
  "Musculoskeletal System Cancer": [
    "Osteosarcoma"
  ],
  "Reproductive Organ Cancer": [
    "Human Papillomavirus-related Endocervical Adenocarcinoma",
    "Ovarian Cancer",
    "Ovarian Endometrioid Carcinoma",
    "Prostate Cancer",
    "Prostate Carcinoma"
  ],
  "Respiratory System Cancer": [
    "Lung Adenocarcinoma",
    "Lung Large Cell Carcinoma"
  ],
  "Urinary System Cancer": [
    "Kidney Rhabdoid Cancer",
    "Kidney Tumor",
    "Renal Cell Carcinoma"
  ],
  "Genetic Disease": [
    "Duchenne Muscular Dystrophy",
    "Hbs1L Deficiency",
    "Roberts Syndrome",
    "Tuberous Sclerosis Complex"
  ],
  "Other": [
    "Neoplastic Transformation",
    "Patient-derived",
    "Treacher Collins Syndrome"
  ],
  "Nervous System Cancer": [
    "Astrocytoma",
    "Brain Glioma",
    "Neuroblastoma"
  ],
  "Hematologic Cancer": [
    "Acute Myeloid Leukemia",
    "Adult Acute Myeloid Leukemia",
    "Childhood T Acute Lymphoblastic Leukemia",
    "Childhood T Lymphoblastic Lymphoma",
    "Chronic Myeloid Leukemia",
    "Multiple Myeloma"
  ],
  "Breast Cancer": [
    "Breast Adenocarcinoma",
    "Breast Carcinoma"
  ],
  "Head And Neck Cancer": [
    "Head And Neck Squamous Cell Carcinoma",
    "Tongue Squamous Cell Carcinoma"
  ],
  "Endocrine Gland Cancer": [
    "Pancreatic Adenocarcinoma"
  ]
};

// 疾病大类颜色配置
export const CONDITION_GROUP_COLORS: { [key: string]: string } = {
  "Infections": "#FF6B6B",                    // 红色系 - 感染性疾病
  "Gastrointestinal System Cancer": "#4ECDC4", // 青色系 - 消化系统癌症
  "Musculoskeletal System Cancer": "#45B7D1",  // 蓝色系 - 肌肉骨骼系统癌症
  "Reproductive Organ Cancer": "#96CEB4",      // 绿色系 - 生殖器官癌症
  "Respiratory System Cancer": "#FFEAA7",      // 黄色系 - 呼吸系统癌症
  "Urinary System Cancer": "#DDA0DD",          // 紫色系 - 泌尿系统癌症
  "Genetic Disease": "#FFB347",                // 橙色系 - 遗传性疾病
  "Other": "#D3D3D3",                          // 灰色系 - 其他
  "Nervous System Cancer": "#F8BBD9",          // 粉色系 - 神经系统癌症
  "Hematologic Cancer": "#E17055",             // 深橙色系 - 血液系统癌症
  "Breast Cancer": "#FD79A8",                  // 玫瑰色系 - 乳腺癌
  "Head And Neck Cancer": "#A29BFE",           // 淡紫色系 - 头颈部癌症
  "Endocrine Gland Cancer": "#00B894",         // 深绿色系 - 内分泌腺癌症
  "Normal": "#B0BEC5"                          // 浅灰色系 - 正常/未分类
};

// 获取条件的分组
const getConditionGroup = (condition: string): string => {
  for (const [groupName, conditions] of Object.entries(CONDITION_GROUPS)) {
    if (conditions.includes(condition)) {
      return groupName;
    }
  }
  return 'Normal'; // 未分类的条件改为Normal
};

// 按分组组织可用条件
const organizeConditionsByGroup = (conditions: string[]) => {
  const groups: {[key: string]: string[]} = {};

  conditions.forEach(condition => {
    const group = getConditionGroup(condition);
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(condition);
  });

  // 排序分组和分组内的条件
  const sortedGroups: {[key: string]: string[]} = {};
  Object.keys(groups).sort().forEach(groupName => {
    sortedGroups[groupName] = groups[groupName].sort();
  });

  return sortedGroups;
};

export default function ConditionAnalysis(props: AnalysisComponentProps) {
  const getConditionValue = (item: TranscriptData) => item.disease;

  return (
    <BaseAnalysisComponent
      {...props}
      analysisType="Condition"
      getConditionValue={getConditionValue}
      organizeConditionsByGroup={organizeConditionsByGroup}
    />
  );
}