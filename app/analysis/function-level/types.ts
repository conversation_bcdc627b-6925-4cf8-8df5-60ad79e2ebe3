// 数据类型定义
export interface KEGGAnnotationData {
  id: number;
  geneSymbol: string;
  geneId: string;
  pathwayDescription: string;
  pathwayId: string;
  translationProjectIds: string; // 新的字段，包含分号分隔的项目ID列表
  // 保留旧字段以兼容性，标记为可选
  tissueCellType?: string;
  cellLine?: string;
  disease?: string;
}

export interface GOAnnotationData {
  id: number;
  geneSymbol: string;
  geneId: string;
  goTerm: string;
  goDomain: string;
  goId: string;
  translationProjectIds: string; // 新的字段，包含分号分隔的项目ID列表
  // 保留旧字段以兼容性，标记为可选
  tissueCellType?: string;
  cellLine?: string;
  disease?: string;
}

// 转录数据类型定义
export interface TranslationIndicesData {
  id: number;
  transcriptId: string;
  projectId: string;
  bioprojectId: string;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  tr: number | null;
  evi: number | null;
  te: number | null;
  geneId: string;
  geneSymbol: string;
  threeUtrComp: string;
  fiveUtrComp: string;
}

// Gene数据类型定义
export interface GeneData {
  id: number;
  geneSymbol: string;
  geneId: string;
  projectId: string;
  expressedTranscriptNumber: number;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  chromosome: string;
  tr: number | null;
  evi: number | null;
  te: number;
  averageTE: number | null;
  averageTR: number | null;
  averageEVI: number | null;
}

// 箱图数据接口
export interface BoxPlotData {
  transcriptId: string;
  geneId: string;
  category: string;
  value: number;
  projectId: string;
}

// 柱状图数据接口
export interface BarChartData {
  category: string;
  value: number;
  geneId: string;
  count: number; // 样本数量
}

// 转录数据别名
export type TranslationData = TranslationIndicesData;

export type AnalysisType = 'KEGG analysis' | 'GO analysis';

// 过滤器关系类型定义
export interface FilterRelations {
  tissueToCell: { [key: string]: string[] };
  tissueToDisease: { [key: string]: string[] };
  cellToTissue: { [key: string]: string[] };
  cellToDisease: { [key: string]: string[] };
  diseaseToTissue: { [key: string]: string[] };
  diseaseToCell: { [key: string]: string[] };
}

// 组件Props类型定义
export interface FunctionLevelPageProps {
  analysisType: AnalysisType;
  onAnalysisTypeChange: (type: AnalysisType) => void;
}

export interface SearchSectionProps {
  analysisType: AnalysisType;
  searchInput: string;
  onSearchInputChange: (value: string) => void;
  showDropdown: boolean;
  setShowDropdown: (show: boolean) => void;
  filteredOptions: string[];
  onSearchSelect: (term: string) => void;
  isLoading: boolean;
  error: string;
}

export interface FilterSectionProps {
  availableTissueCellTypes: string[];
  availableCellLines: string[];
  availableDiseases: string[];
  selectedTissueCellTypes: string[];
  selectedCellLines: string[];
  selectedDiseases: string[];
  onTissueCellTypeChange: (types: string[]) => void;
  onCellLineChange: (lines: string[]) => void;
  onDiseaseChange: (diseases: string[]) => void;
  showMoreTissues: boolean;
  showMoreCellLines: boolean;
  showMoreDiseases: boolean;
  setShowMoreTissues: (show: boolean) => void;
  setShowMoreCellLines: (show: boolean) => void;
  setShowMoreDiseases: (show: boolean) => void;
}

export interface TableSectionProps {
  analysisType: AnalysisType;
  keggTableData: KEGGAnnotationData[];
  goTableData: GOAnnotationData[];
  selectedGeneIds: Set<string>;
  onGeneSelectionChange: (geneIds: Set<string>) => void;
  sortField: string;
  sortDirection: 'asc' | 'desc';
  onSort: (field: string) => void;
  searchKeyword: string;
  onSearchKeywordChange: (keyword: string) => void;
  keggCurrentPage: number;
  keggPageSize: number;
  goCurrentPage: number;
  goPageSize: number;
  onKeggPageChange: (page: number) => void;
  onGoPageChange: (page: number) => void;
  onKeggPageSizeChange: (size: number) => void;
  onGoPageSizeChange: (size: number) => void;
}

export interface BoxPlotSectionProps {
  translationData: TranslationIndicesData[];
  selectedGeneIds: Set<string>;
  showResults: boolean;
}