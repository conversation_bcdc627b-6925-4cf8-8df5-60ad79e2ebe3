import React from 'react';
import * as d3 from 'd3';
import { Download } from 'lucide-react';
import { GeneData, BarChartData } from '../types';

interface GeneLevelDisplayProps {
  geneData: GeneData[];
  selectedGeneIds: Set<string>;
  selectedTissueCellTypes: string[];
  selectedCellLines: string[];
  selectedDiseases: string[];
}

export default function GeneLevelDisplay({
  geneData,
  selectedGeneIds,
  selectedTissueCellTypes,
  selectedCellLines,
  selectedDiseases
}: GeneLevelDisplayProps) {
  // 马卡龙色系
  const macaronColors = [
    '#FFD1DC', '#B5EAD7', '#C7CEEA', '#FFDAC1',
    '#E0BBE4', '#FFB7B2', '#FFCCCB', '#DDA0DD',
    '#F0E68C', '#98FB98', '#87CEEB', '#DEB887'
  ];

  // 准备柱状图数据 - 使用平均值
  const prepareBarChartData = (dataType: 'averageTE' | 'averageTR' | 'averageEVI'): { data: BarChartData[] } => {
    if (!geneData.length) return { data: [] };

    // 确定要使用的字段名
    let sourceField: 'te' | 'tr' | 'evi' | 'averageTE' | 'averageTR' | 'averageEVI';
    let averageField: 'averageTE' | 'averageTR' | 'averageEVI';
    
    switch (dataType) {
      case 'averageTE':
        sourceField = 'te';
        averageField = 'averageTE';
        break;
      case 'averageTR':
        sourceField = 'tr';
        averageField = 'averageTR';
        break;
      case 'averageEVI':
        sourceField = 'evi';
        averageField = 'averageEVI';
        break;
    }

    const filteredData = geneData.filter(item => {
      // 优先使用原始值，其次使用平均值
      const sourceValue = item[sourceField];
      const averageValue = item[averageField];
      return (sourceValue !== null && sourceValue !== undefined) || 
             (averageValue !== null && averageValue !== undefined);
    });

    const barChartData: BarChartData[] = [];

    // 按geneId分组并计算平均值
    const groupedData = new Map<string, number[]>();
    
    filteredData.forEach(item => {
      // 优先使用原始值
      let value = item[sourceField];
      if (value === null || value === undefined) {
        value = item[averageField];
      }
      
      if (value !== null && value !== undefined && typeof value === 'number' && !isNaN(value)) {
        if (!groupedData.has(item.geneId)) {
          groupedData.set(item.geneId, []);
        }
        groupedData.get(item.geneId)!.push(value);
      }
    });

    // 计算每个基因的平均值，然后应用log2转换，并获取对应的 gene symbol
    groupedData.forEach((values, geneId) => {
      if (values.length > 0) {
        const averageValue = values.reduce((sum, val) => sum + val, 0) / values.length;
        // 先求平均值，再求log2
        const log2Value = Math.log2(averageValue);
        // 查找对应的基因信息，优先使用 geneSymbol
        const geneInfo = filteredData.find(item => item.geneId === geneId);
        const displayName = (geneInfo?.geneSymbol && geneInfo.geneSymbol.trim()) ? geneInfo.geneSymbol : geneId;
        
        barChartData.push({
          category: displayName,
          value: log2Value,
          geneId: geneId,
          count: values.length // 用于显示样本数量
        });
      }
    });

    return { data: barChartData };
  };

  // 获取可用的数据类型
  const getAvailableDataTypes = () => {
    const types: Array<{ type: 'averageTE' | 'averageTR' | 'averageEVI', displayName: string }> = [];

    // 检查TE数据 - 优先检查原始值te字段，其次检查averageTE字段
    if (geneData.some(item =>
      (item.te !== null && item.te !== undefined && typeof item.te === 'number' && !isNaN(item.te)) ||
      (item.averageTE !== null && item.averageTE !== undefined && typeof item.averageTE === 'number' && !isNaN(item.averageTE))
    )) {
      types.push({ type: 'averageTE', displayName: 'TE' });
    }

    // 检查TR数据 - 优先检查原始值tr字段，其次检查averageTR字段
    if (geneData.some(item =>
      (item.tr !== null && item.tr !== undefined && typeof item.tr === 'number' && !isNaN(item.tr)) ||
      (item.averageTR !== null && item.averageTR !== undefined && typeof item.averageTR === 'number' && !isNaN(item.averageTR))
    )) {
      types.push({ type: 'averageTR', displayName: 'TR' });
    }

    // 检查EVI数据 - 优先检查原始值evi字段，其次检查averageEVI字段
    if (geneData.some(item =>
      (item.evi !== null && item.evi !== undefined && typeof item.evi === 'number' && !isNaN(item.evi)) ||
      (item.averageEVI !== null && item.averageEVI !== undefined && typeof item.averageEVI === 'number' && !isNaN(item.averageEVI))
    )) {
      types.push({ type: 'averageEVI', displayName: 'EVI' });
    }

    return types;
  };

  // 图片下载功能
  const downloadChartImage = (dataType: 'averageTE' | 'averageTR' | 'averageEVI', displayName: string) => {
    const { data } = prepareBarChartData(dataType);
    if (data.length === 0) return;

    // 生成分类标签，优先使用 geneSymbol
    const categories = Array.from(selectedGeneIds).map(geneId => {
      const geneInfo = geneData.find(item => item.geneId === geneId);
      return (geneInfo?.geneSymbol && geneInfo.geneSymbol.trim()) ? geneInfo.geneSymbol : geneId;
    }).sort();

    // 统一与页面 SVG 渲染的尺寸/布局逻辑
    const margin = { top: 60, right: 200, bottom: 180, left: 80 };

    // 与页面一致的柱宽与总宽计算
    const fixedBarWidthSvg = Math.max(60, 400 / categories.length);
    const actualBarWidth = Math.min(80, fixedBarWidthSvg);
    const dynamicWidth = Math.max(1000, categories.length * (actualBarWidth + 20) + margin.left + margin.right);
    const height = 480; // 与页面一致

    // 创建Canvas并设置尺寸
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    const width = dynamicWidth;
    canvas.width = width;
    canvas.height = height;

    // 背景色
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);

    // 图表区域
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // 计算比例尺，与页面一致的柱宽与间距
    const allValues = data.map(d => d.value).filter(v => typeof v === 'number' && !isNaN(v));
    const totalBarsWidth = actualBarWidth * categories.length;
    const totalSpacing = chartWidth - totalBarsWidth;
    const spacingBetweenBars = categories.length > 1 ? totalSpacing / (categories.length + 1) : totalSpacing / 2;

    // 如果所有值都是正数，从0开始；如果有负值，则包含负值范围
    const minValue = Math.min(...allValues);
    const maxValue = Math.max(...allValues);
    const yMin = minValue >= 0 ? 0 : minValue - 0.5;
    const yMax = maxValue + 0.5;

    const getBarX = (index: number) => margin.left + spacingBetweenBars + index * (actualBarWidth + spacingBetweenBars / categories.length);
    const getBarWidth = () => actualBarWidth;
    // 使用 d3 线性比例尺，确保与页面 SVG 的纵轴一致
    const yScale = d3.scaleLinear()
      .domain([yMin, yMax])
      .range([margin.top + chartHeight, margin.top]);

    // 坐标轴（与页面一致：仅绘制左轴线与底部轴线，不绘制边框矩形）
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    // 左轴线
    ctx.beginPath();
    ctx.moveTo(margin.left, margin.top);
    ctx.lineTo(margin.left, margin.top + chartHeight);
    ctx.stroke();
    // 底部轴线
    ctx.beginPath();
    ctx.moveTo(margin.left, margin.top + chartHeight);
    ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight);
    ctx.stroke();

    // 顶部标题（与页面标题一致）
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    const titleText = `${displayName} (log2 of average values) in ${selectedCellLines.length > 0 ? selectedCellLines[0] : selectedTissueCellTypes[0]}_${selectedDiseases[0]}`;
    ctx.fillText(titleText, width / 2, Math.max(22, margin.top / 2));

    // 绘制Y轴刻度与标签：与页面 SVG 一致使用 d3 的 ticks 与格式
    const ticks = yScale.ticks(10);
    const tickFormat = yScale.tickFormat(10);
    ticks.forEach((t) => {
      const y = yScale(t);
      // 刻度线（向左）
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(margin.left, y);
      ctx.lineTo(margin.left - 6, y);
      ctx.stroke();

      // 标签
      ctx.fillStyle = '#333333';
      ctx.font = '10px Arial';
      ctx.textAlign = 'right';
      ctx.textBaseline = 'middle';
      ctx.fillText(String(tickFormat(t)), margin.left - 10, y);
    });

    // Y轴标签
    ctx.save();
    ctx.translate(20, margin.top + chartHeight / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${displayName} - log2 of average values`, 0, 0);
    ctx.restore();

    // X轴刻度线 - 添加到每个横坐标标签位置
    categories.forEach((category, index) => {
      const x = getBarX(index) + getBarWidth() / 2;
      const baseY = margin.top + chartHeight;
      
      // 绘制刻度线
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, baseY);
      ctx.lineTo(x, baseY + 6); // 刻度线长度6像素
      ctx.stroke();
    });

    // X轴标签 - 与页面 SVG 风格对齐：左对齐锚点，增加与轴线的间距
    categories.forEach((category, index) => {
      const x = getBarX(index) + getBarWidth() / 2;
      const y = margin.top + chartHeight + 22; // 增加与轴线的距离，避免过近

      const labelText = String(category);

      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(Math.PI / 4); // 45度旋转

      // 与页面一致的样式：左对齐、无背景，深灰色文字
      ctx.fillStyle = '#555555';
      ctx.font = 'bold 10px Arial';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'alphabetic';
      ctx.fillText(labelText, 0, 0);
      ctx.restore();
    });

    // 绘制柱状图 - 使用固定宽度
    categories.forEach((category, index) => {
      const categoryData = data.find(d => d.category === category);
      if (!categoryData) return;

      // 正确计算柱子位置和高度，与SVG保持一致
      const zeroY = yScale(0);
      const valueY = yScale(categoryData.value);
      const barX = getBarX(index);
      
      let barY, barHeight;
      if (categoryData.value >= 0) {
        // 正值：从值到零线
        barY = valueY;
        barHeight = zeroY - valueY;
      } else {
        // 负值：从零线到值
        barY = zeroY;
        barHeight = valueY - zeroY;
      }

      // 柱子颜色
      const color = macaronColors[index % macaronColors.length];
      
      // 直接绘制柱子主体，简洁样式
      ctx.fillStyle = color;
      ctx.fillRect(barX, barY, getBarWidth(), barHeight);

      // 在柱子内部显示数值 - 智能定位避免遮挡X轴标签
      const isPositive = categoryData.value >= 0;
      // 将标签放在柱子内部，距离顶部/底部一定距离
      const textY = isPositive ? 
        barY + 20 : // 正值：距离柱子顶部20px
        barY + barHeight - 20; // 负值：距离柱子底部20px
      const textX = barX + getBarWidth() / 2;
      
      // 只有当柱子足够高时才显示数值
      if (Math.abs(barHeight) > 35) {
        // 数值 - 移除背景，直接显示文字
        const valueText = categoryData.value.toFixed(2);
        ctx.font = 'bold 10px Arial';
        
        // 直接显示文字，不要背景
        ctx.fillStyle = '#333333'; // 深色文字
        ctx.textAlign = 'center';
        ctx.fillText(valueText, textX, textY - 4);

        // 样本数量标签
        ctx.fillStyle = '#666666';
        ctx.font = '8px Arial';
        ctx.fillText(`(n=${categoryData.count})`, textX, textY + 12);
      } else {
        // 柱子太矮时，将标签放在柱子顶部外侧，但位置更靠近柱子
        const fallbackY = isPositive ? barY - 8 : barY + barHeight + 16;
        const valueText = categoryData.value.toFixed(2);
        
        // 直接显示文字，不要背景
        ctx.fillStyle = '#333333';
        ctx.font = 'bold 9px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(valueText, textX, fallbackY - 2);
        
        ctx.fillStyle = '#666666';
        ctx.font = '7px Arial';
        ctx.fillText(`(n=${categoryData.count})`, textX, fallbackY + 10);
      }
    });

    // 不再绘制零值横线，避免与底部轴线重复

    // 绘制图例
    const legendStartX = margin.left + chartWidth + 20;

    ctx.fillStyle = '#333333';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Gene Symbol', legendStartX, 30);

    categories.forEach((category, index) => {
      const color = macaronColors[index % macaronColors.length];
      const legendY = 50 + index * 15;

      ctx.fillStyle = color;
      ctx.fillRect(legendStartX, legendY - 5, 10, 10);
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 0.5;
      ctx.strokeRect(legendStartX, legendY - 5, 10, 10);

      ctx.fillStyle = '#333333';
      ctx.font = '9px Arial';
      ctx.fillText(String(category), legendStartX + 15, legendY + 2);
    });

    // 下载图片
    const link = document.createElement('a');
    const filename = `gene_${displayName}_${selectedCellLines.length > 0 ? selectedCellLines[0] : selectedTissueCellTypes[0]}_${selectedDiseases[0]}` + '.png';
    
    link.download = filename;
    link.href = canvas.toDataURL();
    link.click();
  };

  // 渲染单个柱状图
  const renderSingleBarChart = (dataType: 'averageTE' | 'averageTR' | 'averageEVI', displayName: string) => {
    const { data } = prepareBarChartData(dataType);
    
    if (data.length === 0) {
      return null;
    }

    // 生成分类标签，优先使用 geneSymbol
    const categories = Array.from(selectedGeneIds).map(geneId => {
      const geneInfo = geneData.find(item => item.geneId === geneId);
      return (geneInfo?.geneSymbol && geneInfo.geneSymbol.trim()) ? geneInfo.geneSymbol : geneId;
    }).sort();

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex" style={{ height: '480px' }}>
          <div className="flex-1 min-w-0 flex flex-col">
            <div className="mb-3 flex items-center justify-between">
              <div></div>
              <h3 className="font-bold text-gray-900 text-center text-lg flex-1">
                {displayName} (log2 of average values) in {selectedCellLines.length > 0 ? selectedCellLines[0] : selectedTissueCellTypes[0]}_{selectedDiseases[0]}
              </h3>
              <button
                onClick={() => downloadChartImage(dataType, displayName)}
                className="bg-white hover:bg-gray-100 border border-gray-300 text-gray-700 p-2 rounded-md"
                style={{ backgroundColor: '#ffffff', borderColor: '#d1d5db', color: '#374151' }}
                title="Download chart as image"
              >
                <Download className="h-4 w-4" />
              </button>
            </div>
            
            <div className="flex-1">
              <div 
                className="h-full border border-gray-200 rounded scroll-container"
                style={{
                  maxHeight: '500px',
                  overflowX: 'scroll',
                  overflowY: 'hidden'
                }}
              >
                <div style={{ 
                  minWidth: `${Math.max(1000, categories.length * 100)}px`, 
                  width: `${Math.max(1000, categories.length * 100)}px`, // 显式设置宽度
                  height: '450px' 
                }}>
                  <svg
                    style={{ width: '100%', height: '100%' }}
                    ref={(node) => {
                    if (node) {
                      d3.select(node).selectAll("*").remove();

                      if (data.length > 0 && categories.length > 0) {
                        const svg = d3.select(node);
                        // 统一使用与下载图片相同的margin和尺寸
                        const margin = { top: 60, right: 200, bottom: 180, left: 80 }; // 增加bottom边距
                        
                        // 计算动态宽度
                        const fixedBarWidth = Math.max(60, 400 / categories.length); // 确保最小宽度
                        const dynamicWidth = Math.max(1000, categories.length * (fixedBarWidth + 20) + margin.left + margin.right);
                        const height = 480; // 相应增加高度以适应增加的bottom margin
                        
                        svg.attr("width", dynamicWidth).attr("height", height);
                        
                        const innerWidth = dynamicWidth - margin.left - margin.right;
                        const innerHeight = height - margin.top - margin.bottom;

                        const allValues = data.map(d => d.value).filter(v => typeof v === 'number' && !isNaN(v));
                        // 如果所有值都是正数，从0开始；如果有负值，则包含负值范围
                        const minValue = d3.min(allValues) || 0;
                        const maxValue = d3.max(allValues) || 0;
                        const yMin = minValue >= 0 ? 0 : minValue - 0.5;
                        const yMax = maxValue + 0.5;

                        // 设置固定的柱子宽度，避免太宽或太窄
                        const actualBarWidth = Math.min(80, fixedBarWidth);
                        const totalBarsWidth = actualBarWidth * categories.length;
                        const totalSpacing = innerWidth - totalBarsWidth;
                        const spacingBetweenBars = categories.length > 1 ? totalSpacing / (categories.length + 1) : totalSpacing / 2;

                        // 创建自定义位置函数，使用固定宽度
                        const getBarX = (index: number) => spacingBetweenBars + index * (actualBarWidth + spacingBetweenBars / categories.length);
                        const getBarWidth = () => actualBarWidth;

                        // 保持原有的xScale用于X轴标签，但调整domain和range以匹配固定宽度布局
                        // const xScale = d3.scaleBand()
                        //   .domain(categories.map(String))
                        //   .range([0, innerWidth])
                        //   .padding(0.2);

                        const yScale = d3.scaleLinear()
                          .domain([yMin, yMax])
                          .range([innerHeight, 0]);

                        const g = svg.append("g")
                          .attr("transform", `translate(${margin.left},${margin.top})`);

                        // Y轴（不绘制网格线）
                        g.append("g")
                          .call(d3.axisLeft(yScale));

                        // Y轴标签
                        g.append("text")
                          .attr("transform", "rotate(-90)")
                          .attr("y", 0 - margin.left)
                          .attr("x", 0 - (innerHeight / 2))
                          .attr("dy", "1em")
                          .style("text-anchor", "middle")
                          .style("font-size", "12px")
                          .style("font-weight", "bold")
                          .text(`${displayName} - log2 of average values`);

                        // X轴 - 创建自定义X轴，标签位置对应柱子中心
                        const xAxisGroup = g.append("g")
                          .attr("transform", `translate(0,${innerHeight})`);
                        
                        // 绘制X轴线
                        xAxisGroup.append("line")
                          .attr("x1", 0)
                          .attr("x2", innerWidth)
                          .attr("y1", 0)
                          .attr("y2", 0)
                          .attr("stroke", "#000")
                          .attr("stroke-width", 1);
                        
                        // 添加X轴刻度线，位置对应柱子中心
                        categories.forEach((category, index) => {
                          const barCenterX = getBarX(index) + getBarWidth() / 2;
                          
                          // 绘制刻度线
                          xAxisGroup.append("line")
                            .attr("x1", barCenterX)
                            .attr("x2", barCenterX)
                            .attr("y1", 0)
                            .attr("y2", 6) // 刻度线长度6像素
                            .attr("stroke", "#000")
                            .attr("stroke-width", 1);
                        });
                        
                        // 添加自定义X轴标签，位置对应柱子中心
                        categories.forEach((category, index) => {
                          const barCenterX = getBarX(index) + getBarWidth() / 2;
                          
                          xAxisGroup.append("text")
                            .attr("x", barCenterX)
                            .attr("y", 15) // 调整Y位置
                            .attr("transform", `rotate(45, ${barCenterX}, 15)`)
                            .attr("text-anchor", "start")
                            .attr("dx", "0.2em") // 减少偏移，让标签更接近刻度线中心
                            .attr("dy", "0.05em") // 减少偏移
                            .style("font-size", "10px")
                            .style("font-weight", "bold")
                            .style("fill", "#555555")
                            .text(String(category));
                        });

                        // 绘制柱状图 - 使用固定宽度
                        categories.forEach((category, index) => {
                          const categoryData = data.find(d => d.category === category);
                          if (!categoryData) return;

                          const color = macaronColors[index % macaronColors.length];
                          const barX = getBarX(index);
                          const barWidth = getBarWidth();
                          
                          // 正确计算柱子位置和高度，避免超出边界
                          const zeroY = yScale(0);
                          const valueY = yScale(categoryData.value);
                          
                          let barY, barHeight;
                          if (categoryData.value >= 0) {
                            // 正值：从值到零线
                            barY = valueY;
                            barHeight = zeroY - valueY;
                          } else {
                            // 负值：从零线到值
                            barY = zeroY;
                            barHeight = valueY - zeroY;
                          }
                          
                          // 确保柱子不超出图表边界
                          barHeight = Math.max(0, Math.min(barHeight, innerHeight - Math.max(0, barY)));
                          if (barY < 0) {
                            barHeight += barY;
                            barY = 0;
                          }
                          if (barY + barHeight > innerHeight) {
                            barHeight = innerHeight - barY;
                          }

                          // 柱子 - 移除阴影效果
                          const barGroup = g.append("g");
                          
                          // 主柱子 - 简洁的矩形样式
                          barGroup.append("rect")
                            .attr("x", barX)
                            .attr("y", barY)
                            .attr("width", barWidth)
                            .attr("height", barHeight) // 不需要Math.abs，已经是正值
                            .attr("fill", color)
                            .append("title")
                            .text(`${category}: ${categoryData.value.toFixed(3)} (n=${categoryData.count})`);

                          // 数值标签 - 智能定位，优先放在柱子内部
                          const isPositive = categoryData.value >= 0;
                          
                          // 根据柱子高度决定标签位置
                          if (barHeight > 35) {
                            // 柱子足够高：放在柱子内部
                            const textY = isPositive ? 
                              barY + 20 : // 正值：距离柱子顶部20px
                              barY + barHeight - 20; // 负值：距离柱子底部20px
                            const textX = barX + barWidth / 2;
                            
                            // 数值标签 - 移除背景，直接显示文字
                            const valueText = categoryData.value.toFixed(2);
                            
                            // 直接显示文字，不要背景
                            g.append("text")
                              .attr("x", textX)
                              .attr("y", textY - 4)
                              .attr("text-anchor", "middle")
                              .attr("font-size", "10px")
                              .attr("font-weight", "bold")
                              .attr("fill", "#333333") // 深色文字
                              .text(valueText);

                            // 样本数量标签
                            g.append("text")
                              .attr("x", textX)
                              .attr("y", textY + 12)
                              .attr("text-anchor", "middle")
                              .attr("font-size", "8px")
                              .attr("fill", "#666666")
                              .text(`(n=${categoryData.count})`);
                          } else {
                            // 柱子太矮：放在柱子外侧，但位置更靠近
                            const textY = isPositive ? barY - 8 : barY + barHeight + 16;
                            const textX = barX + barWidth / 2;
                            
                            // 数值文字 - 移除背景
                            const valueText = categoryData.value.toFixed(2);
                            
                            // 直接显示文字，不要背景
                            g.append("text")
                              .attr("x", textX)
                              .attr("y", textY - 2)
                              .attr("text-anchor", "middle")
                              .attr("font-size", "9px")
                              .attr("font-weight", "bold")
                              .attr("fill", "#333")
                              .text(valueText);

                            // 样本数量标签
                            g.append("text")
                              .attr("x", textX)
                              .attr("y", textY + 10)
                              .attr("text-anchor", "middle")
                              .attr("font-size", "7px")
                              .attr("fill", "#666")
                              .text(`(n=${categoryData.count})`);
                          }
                        });

                        // 图例
                        const legendStartX = margin.left + innerWidth + 20;

                        svg.append("text")
                          .attr("x", legendStartX)
                          .attr("y", 30)
                          .attr("font-size", "12px")
                          .attr("font-weight", "bold")
                          .attr("fill", "#333")
                          .text('Gene Symbol');

                        categories.forEach((category, index) => {
                          const color = macaronColors[index % macaronColors.length];
                          const legendY = 50 + index * 15;

                          svg.append("rect")
                            .attr("x", legendStartX)
                            .attr("y", legendY - 5)
                            .attr("width", 10)
                            .attr("height", 10)
                            .attr("fill", color)
                            .attr("stroke", "#000")
                            .attr("stroke-width", 0.5);

                          svg.append("text")
                            .attr("x", legendStartX + 15)
                            .attr("y", legendY + 2)
                            .attr("font-size", "9px")
                            .attr("fill", "#333")
                            .text(category);
                        });
                      }
                    }
                  }}
                />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const availableDataTypes = getAvailableDataTypes();
  
  // 调试信息

  if (availableDataTypes.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
        <p className="text-gray-500">No data available for the selected genes and conditions.</p>
      </div>
    );
  }

  return (
    <div>
      {/* 添加样式确保滚动条始终可见 */}
      <style jsx>{`
        .scroll-container {
          /* 强制显示滚动条 */
          overflow-x: scroll !important;
          overflow-y: hidden !important;
        }
        .scroll-container::-webkit-scrollbar {
          height: 12px !important;
          width: 12px !important;
        }
        .scroll-container::-webkit-scrollbar-track {
          background: #f1f1f1 !important;
          border-radius: 6px !important;
        }
        .scroll-container::-webkit-scrollbar-thumb {
          background: #CBD5E0 !important;
          border-radius: 6px !important;
        }
        .scroll-container::-webkit-scrollbar-thumb:hover {
          background: #A0AEC0 !important;
        }
        .scroll-container::-webkit-scrollbar-corner {
          background: #f1f1f1 !important;
        }
        /* Firefox */
        .scroll-container {
          scrollbar-width: thin !important;
          scrollbar-color: #CBD5E0 #f1f1f1 !important;
        }
      `}</style>
      
      <div className="bg-white border-l border-r border-gray-200 px-6 py-4">
        <p className="text-gray-700 text-sm leading-relaxed">
          The following figure shows the bar chart of average TE, TR, and EVI values (if available) for genes within the selected pathway/GO term across the chosen biological context. When a gene is present in more than one dataset, the average value is calculated. The dataset count (n) is shown below each bar.
        </p>
      </div>

      <div className="bg-white border-l border-r border-gray-200 px-6 py-6">
        {availableDataTypes.map(({ type, displayName }) => 
          <div key={type}>
            {renderSingleBarChart(type, displayName)}
          </div>
        )}
      </div>
    </div>
  );
}