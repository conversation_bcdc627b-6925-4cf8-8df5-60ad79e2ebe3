import React from 'react';
import * as d3 from 'd3';
import { Download } from 'lucide-react';
import { TranslationData, BoxPlotData } from '../types';

interface TranscriptLevelDisplayProps {
  translationData: TranslationData[];
  selectedGeneIds: Set<string>;
  selectedTissueCellTypes: string[];
  selectedCellLines: string[];
  selectedDiseases: string[];
}

export default function TranscriptLevelDisplay({
  translationData,
  selectedGeneIds,
  selectedTissueCellTypes,
  selectedCellLines,
  selectedDiseases
}: TranscriptLevelDisplayProps) {
  // 马卡龙色系
  const macaronColors = [
    '#FFD1DC', '#B5EAD7', '#C7CEEA', '#FFDAC1',
    '#E0BBE4', '#FFB7B2', '#FFCCCB', '#DDA0DD',
    '#F0E68C', '#98FB98', '#87CEEB', '#DEB887'
  ];

  // 准备箱图数据
  const prepareBoxPlotData = (dataType: 'te' | 'tr' | 'evi'): { data: BoxPlotData[] } => {
    if (!translationData.length) return { data: [] };

    const filteredData = translationData.filter(item => {
      const value = item[dataType];
      return value !== null && value !== undefined;
    });

    const boxPlotData: BoxPlotData[] = [];

    filteredData.forEach(item => {
      const value = item[dataType];
      if (value !== null && value !== undefined && typeof value === 'number' && !isNaN(value) && value > 0) {
        // 使用log2转换原始值
        const log2Value = Math.log2(value);
        // 优先使用 Gene Symbol，如果不存在或为空则回退到 GENE ID
        const displayLabel = item.geneSymbol && item.geneSymbol.trim() ? item.geneSymbol : item.geneId;
        boxPlotData.push({
          category: displayLabel,
          value: log2Value,
          transcriptId: item.transcriptId,
          geneId: item.geneId,
          projectId: item.projectId
        });
      }
    });

    return { data: boxPlotData };
  };

  // 获取可用的数据类型
  const getAvailableDataTypes = () => {
    const types: Array<{ type: 'te' | 'tr' | 'evi', displayName: string }> = [];

    // 检查TE数据
    if (translationData.some(item =>
      item.te !== null &&
      item.te !== undefined &&
      typeof item.te === 'number' &&
      !isNaN(item.te)
    )) {
      types.push({ type: 'te', displayName: 'TE' });
    }

    // 检查TR数据
    if (translationData.some(item =>
      item.tr !== null &&
      item.tr !== undefined &&
      typeof item.tr === 'number' &&
      !isNaN(item.tr)
    )) {
      types.push({ type: 'tr', displayName: 'TR' });
    }

    // 检查EVI数据
    if (translationData.some(item =>
      item.evi !== null &&
      item.evi !== undefined &&
      typeof item.evi === 'number' &&
      !isNaN(item.evi)
    )) {
      types.push({ type: 'evi', displayName: 'EVI' });
    }

    return types;
  };

  // 图片下载功能 - 使用Canvas直接绘制避免oklch问题
  const downloadChartImage = (dataType: 'te' | 'tr' | 'evi', displayName: string) => {
    const { data } = prepareBoxPlotData(dataType);
    if (data.length === 0) return;

    // 获取categories和transcriptIds - 使用与数据相同的显示标签逻辑
    const categoryMap = new Map<string, string>();
    translationData.forEach(item => {
      if (selectedGeneIds.has(item.geneId)) {
        const displayLabel = item.geneSymbol && item.geneSymbol.trim() ? item.geneSymbol : item.geneId;
        categoryMap.set(item.geneId, displayLabel);
      }
    });
    const categories = Array.from(categoryMap.values()).sort();
    const transcriptIds = Array.from(new Set(data.map(d => `${d.transcriptId}_${d.projectId}`))).sort();

    // 计算图例所需的列数和宽度
    const maxItemsPerColumn = 20;
    const geneColumns = Math.ceil(categories.length / maxItemsPerColumn);
    const transcriptColumns = Math.ceil(Math.min(transcriptIds.length, 20) / maxItemsPerColumn);

    // 计算图例总宽度
    const columnWidth = 150;
    const geneIdWidth = geneColumns * columnWidth;
    const transcriptIdWidth = transcriptColumns * columnWidth;
    const legendSpacing = 80; // GENE ID 距离箱图的距离
    const betweenLegendSpacing = 40; // 第一列和第二列图例之间的距离
    const rightMargin = legendSpacing + geneIdWidth + betweenLegendSpacing + transcriptIdWidth + 20;

    // 计算图表尺寸
    const margin = { top: 30, right: rightMargin, bottom: 60, left: 50 };
    const fixedCategoryWidth = 70;
    const width = Math.max(1000, categories.length * fixedCategoryWidth + margin.left + margin.right);
    const height = 400;

    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // 创建Canvas
    const canvas = document.createElement('canvas');
    canvas.width = width * 2; // 高分辨率
    canvas.height = height * 2;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.scale(2, 2); // 高分辨率缩放
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);

    // 标题（与页面标题一致）
    const titleText = `${displayName} (log2 values) in ${selectedCellLines.length > 0 ? selectedCellLines[0] : selectedTissueCellTypes[0]}_${selectedDiseases[0]}`;
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(titleText, width / 2, 20);

    // 计算Y轴范围
    const allValues = data.map(d => d.value).filter(v => typeof v === 'number' && !isNaN(v));
    const yMin = Math.min(...allValues);
    const yMax = Math.max(...allValues);
    const yRange = yMax - yMin;
    const yPadding = yRange * 0.1;
    const yDomainMin = yMin - yPadding;
    const yDomainMax = yMax + yPadding;

    // Y轴比例尺函数
    const yScale = (value: number) => {
      return margin.top + chartHeight - ((value - yDomainMin) / (yDomainMax - yDomainMin)) * chartHeight;
    };

    // X轴比例尺函数
    const xScale = (index: number) => {
      return margin.left + index * fixedCategoryWidth + fixedCategoryWidth * 0.3 / 2;
    };

    const boxWidth = fixedCategoryWidth * 0.7;

    // 绘制坐标轴
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;

    // Y轴
    ctx.beginPath();
    ctx.moveTo(margin.left, margin.top);
    ctx.lineTo(margin.left, margin.top + chartHeight);
    ctx.stroke();

    // X轴
    ctx.beginPath();
    ctx.moveTo(margin.left, margin.top + chartHeight);
    ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight);
    ctx.stroke();

    // Y轴标签
    ctx.fillStyle = '#000000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.save();
    ctx.translate(20, margin.top + chartHeight / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText(`${displayName} - log2 values`, 0, 0);
    ctx.restore();

    // Y轴刻度
    const yTicks = 5;
    for (let i = 0; i <= yTicks; i++) {
      const value = yDomainMin + (yDomainMax - yDomainMin) * i / yTicks;
      const y = yScale(value);

      ctx.beginPath();
      ctx.moveTo(margin.left - 5, y);
      ctx.lineTo(margin.left, y);
      ctx.stroke();

      ctx.textAlign = 'right';
      ctx.font = '10px Arial';
      ctx.fillText(value.toFixed(1), margin.left - 8, y + 3);
    }

    // X轴标签
    ctx.textAlign = 'start';
    ctx.font = '9px Arial';
    categories.forEach((category, index) => {
      const x = xScale(index) + boxWidth / 2;
      const y = margin.top + chartHeight + 15;

      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(Math.PI / 6); // 30度旋转
      ctx.fillText(String(category), 0, 0);
      ctx.restore();
    });

    // 绘制箱线图
    categories.forEach((category, index) => {
      const categoryData = data.filter(d => d.category === category);
      if (categoryData.length === 0) return;

      const values = categoryData.map(d => d.value).filter(v => typeof v === 'number' && !isNaN(v)).sort((a, b) => a - b);
      if (values.length === 0) return;

      // 计算箱线图统计值 - 使用与D3.js相同的分位数计算方法
      const quantile = (arr: number[], p: number): number => {
        const n = arr.length;
        if (n === 0) return 0;
        const i = (n - 1) * p;
        const i0 = Math.floor(i);
        const i1 = Math.ceil(i);
        if (i0 === i1) return arr[i0];
        return arr[i0] * (i1 - i) + arr[i1] * (i - i0);
      };

      const q1 = quantile(values, 0.25);
      const median = quantile(values, 0.5);
      const q3 = quantile(values, 0.75);
      const iqr = q3 - q1;
      const min = Math.max(q1 - 1.5 * iqr, Math.min(...values));
      const max = Math.min(q3 + 1.5 * iqr, Math.max(...values));

      const color = macaronColors[index % macaronColors.length];
      const x = xScale(index);

      // 绘制箱体
      ctx.fillStyle = color;
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 1;

      const boxY = yScale(q3);
      const boxHeight = yScale(q1) - yScale(q3);

      ctx.fillRect(x, boxY, boxWidth, boxHeight);
      ctx.strokeRect(x, boxY, boxWidth, boxHeight);

      // 绘制中位数线
      ctx.beginPath();
      ctx.moveTo(x, yScale(median));
      ctx.lineTo(x + boxWidth, yScale(median));
      ctx.lineWidth = 2;
      ctx.stroke();

      // 绘制须线
      const whiskerX = x + boxWidth / 2;
      const whiskerWidth = boxWidth * 0.5;

      ctx.lineWidth = 1;

      // 上须线
      ctx.beginPath();
      ctx.moveTo(whiskerX, yScale(q3));
      ctx.lineTo(whiskerX, yScale(max));
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(whiskerX - whiskerWidth / 2, yScale(max));
      ctx.lineTo(whiskerX + whiskerWidth / 2, yScale(max));
      ctx.stroke();

      // 下须线
      ctx.beginPath();
      ctx.moveTo(whiskerX, yScale(q1));
      ctx.lineTo(whiskerX, yScale(min));
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(whiskerX - whiskerWidth / 2, yScale(min));
      ctx.lineTo(whiskerX + whiskerWidth / 2, yScale(min));
      ctx.stroke();

      // 绘制异常值
      values.forEach(value => {
        if (value < min || value > max) {
          ctx.fillStyle = color;
          ctx.strokeStyle = '#000000';
          ctx.lineWidth = 0.5;
          ctx.beginPath();
          ctx.arc(whiskerX, yScale(value), 2, 0, 2 * Math.PI);
          ctx.fill();
          ctx.stroke();
        }
      });

      // 绘制数据点
      categoryData.forEach((d, pointIndex) => {
        if (typeof d.value === 'number' && !isNaN(d.value)) {
          const pointIdentifier = `${d.transcriptId}_${d.projectId}`;
          const transcriptIndex = transcriptIds.indexOf(pointIdentifier);
          // 如果找不到transcript，使用默认颜色索引
          const colorIndex = transcriptIndex >= 0 ? transcriptIndex : pointIndex;
          const pointColor = macaronColors[colorIndex % macaronColors.length];

          ctx.fillStyle = pointColor;
          ctx.strokeStyle = '#000000';
          ctx.lineWidth = 0.5;
          ctx.globalAlpha = 0.7;

          // 使用固定的偏移量而不是随机值，确保下载结果一致
          const offsetSeed = (pointIndex * 7 + colorIndex * 13) % 100;
          const xOffset = ((offsetSeed / 100) - 0.5) * boxWidth * 0.6;

          ctx.beginPath();
          ctx.arc(whiskerX + xOffset, yScale(d.value), 3, 0, 2 * Math.PI);
          ctx.fill();
          ctx.stroke();

          ctx.globalAlpha = 1;
        }
      });
    });

    // 绘制图例 - 第一列 (多列布局)
    const legendStartX = margin.left + chartWidth + legendSpacing;

    ctx.fillStyle = '#333333';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Gene Symbol', legendStartX, 30);

    categories.forEach((category, index) => {
      const color = macaronColors[index % macaronColors.length];
      const columnIndex = Math.floor(index / maxItemsPerColumn);
      const rowIndex = index % maxItemsPerColumn;
      const legendX = legendStartX + columnIndex * columnWidth;
      const legendY = 50 + rowIndex * 15;

      // 绘制颜色方块
      ctx.fillStyle = color;
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 1;
      ctx.fillRect(legendX, legendY - 6, 10, 10);
      ctx.strokeRect(legendX, legendY - 6, 10, 10);

      // 绘制文本
      ctx.fillStyle = '#333333';
      ctx.font = '9px Arial';
      ctx.fillText(String(category), legendX + 15, legendY + 2);
    });

    // 绘制图例 - 第二列 (多列布局)
    const transcriptLegendX = legendStartX + geneIdWidth + betweenLegendSpacing;
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 12px Arial';
    ctx.fillText('Transcript ID', transcriptLegendX, 30);

    const displayTranscriptIds = transcriptIds.slice(0, 20); // 显示前20个
    displayTranscriptIds.forEach((transcriptId, index) => {
      const color = macaronColors[index % macaronColors.length];
      const columnIndex = Math.floor(index / maxItemsPerColumn);
      const rowIndex = index % maxItemsPerColumn;
      const legendX = transcriptLegendX + columnIndex * columnWidth;
      const legendY = 50 + rowIndex * 15;

      // 绘制颜色圆圈
      ctx.fillStyle = color;
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 0.5;
      ctx.beginPath();
      ctx.arc(legendX + 5, legendY - 1, 3, 0, 2 * Math.PI);
      ctx.fill();
      ctx.stroke();

      // 绘制文本
      ctx.fillStyle = '#333333';
      ctx.font = '9px Arial';
      ctx.fillText(transcriptId, legendX + 15, legendY + 2);
    });

    // 下载图片
    const link = document.createElement('a');
    const fileName = `transcript_${displayName}_${selectedCellLines.length > 0 ? selectedCellLines[0] : selectedTissueCellTypes[0]}_${selectedDiseases[0]}` + '.png';
    
    link.download = fileName;
    link.href = canvas.toDataURL();
    link.click();
  };

  // 渲染单个箱图
  const renderSingleBoxPlot = (dataType: 'te' | 'tr' | 'evi', displayName: string) => {
    const { data } = prepareBoxPlotData(dataType);
    
    if (data.length === 0) {
      return null;
    }

    // 使用与下载图片相同的逻辑生成categories
    const categoryMap = new Map<string, string>();
    translationData.forEach(item => {
      if (selectedGeneIds.has(item.geneId)) {
        const displayLabel = item.geneSymbol && item.geneSymbol.trim() ? item.geneSymbol : item.geneId;
        categoryMap.set(item.geneId, displayLabel);
      }
    });
    const categories = Array.from(categoryMap.values()).sort();

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex" style={{ height: '480px' }}>
          <div className="flex-1 min-w-0 flex flex-col">
            <div className="mb-3 flex items-center justify-between">
              <div></div>
              <h3 className="font-bold text-gray-900 text-center text-lg flex-1">
                {displayName} (log2 values) in {selectedCellLines.length > 0 ? selectedCellLines[0] : selectedTissueCellTypes[0]}_{selectedDiseases[0]}
              </h3>
              <button
                onClick={() => downloadChartImage(dataType, displayName)}
                className="bg-white hover:bg-gray-100 border border-gray-300 text-gray-700 p-2 rounded-md"
                style={{ backgroundColor: '#ffffff', borderColor: '#d1d5db', color: '#374151' }}
                title="Download chart as image"
              >
                <Download className="h-4 w-4" />
              </button>
            </div>
            
            <div className="flex-1">
              <div 
                className="h-full border border-gray-200 rounded scroll-container"
                style={{
                  maxHeight: '500px',
                  overflowX: 'scroll',
                  overflowY: 'hidden'
                }}
              >
                <div style={{ 
                  minWidth: `${Math.max(1400, categories.length * 80 + 500)}px`, // 增加额外空间给图例
                  width: `${Math.max(1400, categories.length * 80 + 500)}px`, // 显式设置宽度
                  height: '400px' 
                }}>
                  <svg
                    style={{ width: '100%', height: '100%' }}
                    ref={(node) => {
                    if (node) {
                      d3.select(node).selectAll("*").remove();

                      if (data.length > 0 && categories.length > 0) {
                        // SVG和D3渲染逻辑（与原代码相同，但简化为只处理Transcript模式）
                        const svg = d3.select(node);
                        const margin = { top: 60, right: 500, bottom: 100, left: 80 }; // 增加右边距
                        
                        // 计算动态宽度 - 确保有足够空间显示图例
                        const fixedCategoryWidth = Math.max(80, 600 / categories.length); // 确保最小宽度
                        const dynamicWidth = Math.max(1400, categories.length * fixedCategoryWidth + margin.left + margin.right);
                        const height = 400;
                        
                        svg.attr("width", dynamicWidth).attr("height", height);
                        
                        // const innerWidth = dynamicWidth - margin.left - margin.right;
                        const innerHeight = height - margin.top - margin.bottom;

                        const transcriptIds = Array.from(new Set(data.map(d => `${d.transcriptId}_${d.projectId}`))).sort();
                        
                        const boxWidth = Math.min(40, fixedCategoryWidth * 0.7);

                        const allValues = data.map(d => d.value).filter(v => typeof v === 'number' && !isNaN(v));
                        const yMin = Math.min(...allValues) - 0.5;
                        const yMax = Math.max(...allValues) + 0.5;

                        const xScale = d3.scaleBand()
                          .domain(categories.map(String))
                          .range([0, categories.length * fixedCategoryWidth])
                          .padding(0.1);

                        const yScale = d3.scaleLinear()
                          .domain([yMin, yMax])
                          .range([innerHeight, 0]);

                        const g = svg.append("g")
                          .attr("transform", `translate(${margin.left},${margin.top})`);

                        // Y轴
                        g.append("g")
                          .call(d3.axisLeft(yScale));

                        // Y轴标签
                        g.append("text")
                          .attr("transform", "rotate(-90)")
                          .attr("y", 0 - margin.left)
                          .attr("x", 0 - (innerHeight / 2))
                          .attr("dy", "1em")
                          .style("text-anchor", "middle")
                          .style("font-size", "12px")
                          .style("font-weight", "bold")
                          .text(`${displayName} - log2 values`);

                        // X轴
                        const xAxis = g.append("g")
                          .attr("transform", `translate(0,${innerHeight})`)
                          .call(d3.axisBottom(xScale));
                        
                        xAxis.selectAll("text")
                          .attr("transform", "rotate(30)")
                          .style("text-anchor", "start")
                          .style("font-size", "9px");

                        // 绘制箱线图
                        categories.forEach((category, index) => {
                          const categoryData = data.filter(d => d.category === category);
                          if (categoryData.length === 0) return;

                          const values = categoryData.map(d => d.value).filter(v => typeof v === 'number' && !isNaN(v)).sort(d3.ascending);
                          if (values.length === 0) return;

                          const q1 = d3.quantile(values, 0.25) || 0;
                          const median = d3.quantile(values, 0.5) || 0;
                          const q3 = d3.quantile(values, 0.75) || 0;
                          const iqr = q3 - q1;
                          const lowerWhisker = Math.max(d3.min(values) || 0, q1 - 1.5 * iqr);
                          const upperWhisker = Math.min(d3.max(values) || 0, q3 + 1.5 * iqr);

                          const whiskerX = index * fixedCategoryWidth + fixedCategoryWidth / 2;
                          const boxLeft = whiskerX - boxWidth / 2;

                          // 箱体 - 根据GENE ID分配颜色
                          const color = macaronColors[index % macaronColors.length];
                          g.append("rect")
                            .attr("x", boxLeft)
                            .attr("y", yScale(q3))
                            .attr("width", boxWidth)
                            .attr("height", yScale(q1) - yScale(q3))
                            .attr("fill", color)
                            .attr("stroke", "#000")
                            .attr("stroke-width", 1);

                          // 中位线
                          g.append("line")
                            .attr("x1", boxLeft)
                            .attr("x2", boxLeft + boxWidth)
                            .attr("y1", yScale(median))
                            .attr("y2", yScale(median))
                            .attr("stroke", "#000")
                            .attr("stroke-width", 2);

                          // 须线
                          g.append("line")
                            .attr("x1", whiskerX)
                            .attr("x2", whiskerX)
                            .attr("y1", yScale(q3))
                            .attr("y2", yScale(upperWhisker))
                            .attr("stroke", "#000");

                          g.append("line")
                            .attr("x1", whiskerX)
                            .attr("x2", whiskerX)
                            .attr("y1", yScale(q1))
                            .attr("y2", yScale(lowerWhisker))
                            .attr("stroke", "#000");

                          // 须线端点
                          const whiskerCapWidth = boxWidth * 0.3;
                          g.append("line")
                            .attr("x1", whiskerX - whiskerCapWidth)
                            .attr("x2", whiskerX + whiskerCapWidth)
                            .attr("y1", yScale(upperWhisker))
                            .attr("y2", yScale(upperWhisker))
                            .attr("stroke", "#000");

                          g.append("line")
                            .attr("x1", whiskerX - whiskerCapWidth)
                            .attr("x2", whiskerX + whiskerCapWidth)
                            .attr("y1", yScale(lowerWhisker))
                            .attr("y2", yScale(lowerWhisker))
                            .attr("stroke", "#000");

                          // 异常值
                          values.forEach(value => {
                            if (value < lowerWhisker || value > upperWhisker) {
                              const color = "#ff6b6b";
                              g.append("circle")
                                .attr("cx", whiskerX)
                                .attr("cy", yScale(value))
                                .attr("r", 2)
                                .attr("fill", color)
                                .attr("stroke", "#000")
                                .attr("stroke-width", 0.5);
                            }
                          });

                          // 数据点
                          categoryData.forEach((d, dataPointIndex) => {
                            if (typeof d.value === 'number' && !isNaN(d.value)) {
                              const pointIdentifier = `${d.transcriptId}_${d.projectId}`;
                              const pointIndex = transcriptIds.indexOf(pointIdentifier);
                              // 如果找不到transcript，使用默认颜色索引
                              const colorIndex = pointIndex >= 0 ? pointIndex : dataPointIndex;
                              const pointColor = macaronColors[colorIndex % macaronColors.length];

                              g.append("circle")
                                .attr("cx", whiskerX + (Math.random() - 0.5) * boxWidth * 0.6)
                                .attr("cy", yScale(d.value))
                                .attr("r", 3)
                                .attr("fill", pointColor)
                                .attr("stroke", "#000")
                                .attr("stroke-width", 0.5)
                                .attr("opacity", 0.7)
                                .append("title")
                                .text(`${pointIdentifier}: ${d.value.toFixed(3)} (Gene: ${d.geneId})`);
                            }
                          });
                        });

                        // 图例
                        const legendStartX = margin.left + (categories.length * fixedCategoryWidth) + 20;

                        // 第一列图例标题
                        svg.append("text")
                          .attr("x", legendStartX)
                          .attr("y", 30)
                          .attr("font-size", "12px")
                          .attr("font-weight", "bold")
                          .attr("fill", "#333")
                          .text('Gene Symbol');

                        categories.forEach((category, index) => {
                          const color = macaronColors[index % macaronColors.length];
                          const legendY = 50 + index * 15;

                          svg.append("rect")
                            .attr("x", legendStartX)
                            .attr("y", legendY - 5)
                            .attr("width", 10)
                            .attr("height", 10)
                            .attr("fill", color)
                            .attr("stroke", "#000")
                            .attr("stroke-width", 0.5);

                          svg.append("text")
                            .attr("x", legendStartX + 15)
                            .attr("y", legendY + 2)
                            .attr("font-size", "9px")
                            .attr("fill", "#333")
                            .text(category);
                        });

                        // 第二列图例
                        const secondLegendX = legendStartX + 150;
                        
                        svg.append("text")
                          .attr("x", secondLegendX)
                          .attr("y", 30)
                          .attr("font-size", "12px")
                          .attr("font-weight", "bold")
                          .attr("fill", "#333")
                          .text('Transcript ID');

                        const displayIds = transcriptIds.slice(0, 20); // 增加显示数量到20个
                        displayIds.forEach((transcriptId, index) => {
                          const color = macaronColors[index % macaronColors.length];
                          const legendY = 50 + index * 15;

                          svg.append("rect")
                            .attr("x", secondLegendX)
                            .attr("y", legendY - 5)
                            .attr("width", 10)
                            .attr("height", 10)
                            .attr("fill", color)
                            .attr("stroke", "#000")
                            .attr("stroke-width", 0.5);

                          svg.append("text")
                            .attr("x", secondLegendX + 15)
                            .attr("y", legendY + 2)
                            .attr("font-size", "9px")
                            .attr("fill", "#333")
                            .text(transcriptId);
                        });
                      }
                    }
                  }}
                />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const availableDataTypes = getAvailableDataTypes();
  
  // 调试信息

  if (availableDataTypes.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
        <p className="text-gray-500">No data available for the selected genes and conditions.</p>
      </div>
    );
  }

  return (
    <div>
      {/* 添加样式确保滚动条始终可见 */}
      <style jsx>{`
        .scroll-container {
          /* 强制显示滚动条 */
          overflow-x: scroll !important;
          overflow-y: hidden !important;
        }
        .scroll-container::-webkit-scrollbar {
          height: 12px !important;
          width: 12px !important;
        }
        .scroll-container::-webkit-scrollbar-track {
          background: #f1f1f1 !important;
          border-radius: 6px !important;
        }
        .scroll-container::-webkit-scrollbar-thumb {
          background: #CBD5E0 !important;
          border-radius: 6px !important;
        }
        .scroll-container::-webkit-scrollbar-thumb:hover {
          background: #A0AEC0 !important;
        }
        .scroll-container::-webkit-scrollbar-corner {
          background: #f1f1f1 !important;
        }
        /* Firefox */
        .scroll-container {
          scrollbar-width: thin !important;
          scrollbar-color: #CBD5E0 #f1f1f1 !important;
        }
      `}</style>
      
      <div className="bg-white border-l border-r border-gray-200 px-6 py-4">
        <p className="text-gray-700 text-sm leading-relaxed">
          The following figures show the box plot of TE, TR and EVI values (if available) for genes within the selected pathway/GO term across the chosen biological context, with distinct points representing different transcripts of different datasets. Users can select genes of interest to compare by clicking on the checkboxes in the data table above.
        </p>
      </div>

      <div className="bg-white border-l border-r border-gray-200 px-6 py-6">
        {availableDataTypes.map(({ type, displayName }) => 
          <div key={type}>
            {renderSingleBoxPlot(type, displayName)}
          </div>
        )}
      </div>
    </div>
  );
}