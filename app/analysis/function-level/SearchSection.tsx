import React, { useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Input } from "@/app/components/ui/input";
import { SearchSectionProps } from './types';

export default function SearchSection({
  analysisType,
  searchInput,
  onSearchInputChange,
  showDropdown,
  setShowDropdown,
  filteredOptions,
  onSearchSelect,
  isLoading,
  error
}: SearchSectionProps) {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setShowDropdown]);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Functional Analysis
        </h1>
        <p className="text-gray-600 mb-6">
          Select a {analysisType === 'KEGG analysis' ? 'KEGG pathway' : 'GO term'} to analyze gene functions and their translation efficiency
        </p>

        {/* 搜索输入框 */}
        <div className="relative max-w-2xl mx-auto">
          <div className="relative">
            <Input
              ref={searchInputRef}
              type="text"
              placeholder={`Search for ${analysisType === 'KEGG analysis' ? 'pathway descriptions' : 'GO terms'}...`}
              value={searchInput}
              onChange={(e) => onSearchInputChange(e.target.value)}
              onFocus={() => setShowDropdown(filteredOptions.length > 0)}
              className="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 pr-12"
              disabled={isLoading}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <ChevronDown className="w-5 h-5 text-gray-400" />
            </div>
          </div>

          {/* 下拉选项 */}
          {showDropdown && filteredOptions.length > 0 && (
            <div 
              ref={dropdownRef}
              className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
            >
              {filteredOptions.map((option, index) => (
                <div
                  key={index}
                  className="px-4 py-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => onSearchSelect(option)}
                >
                  <div className="text-sm text-gray-900">{option}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* 加载状态 */}
        {isLoading && (
          <div className="mt-4 text-gray-600">
            Loading...
          </div>
        )}
      </div>
    </div>
  );
}