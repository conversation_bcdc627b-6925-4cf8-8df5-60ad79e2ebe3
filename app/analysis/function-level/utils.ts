import { KEGGAnnotationData, GOAnnotationData, AnalysisType } from './types';
import { getProjectInfo } from './projectMapping';

// 解析组织细胞类型字符串为数组
export const parseTissueCellTypes = (str: string): string[] => {
  if (!str) return [];
  return str.replace(/[{}]/g, '').split(',').map(item => item.trim()).filter(item => item);
};

// 从projectId列表提取过滤选项
export const extractFilterOptionsFromProjectIds = (projectIdString: string) => {
  if (!projectIdString) {
    return {
      tissueTypes: [],
      cellLines: [],
      diseases: []
    };
  }

  const projectIds = projectIdString.split(';').map(id => id.trim()).filter(id => id);
  const tissueTypesSet = new Set<string>();
  const cellLinesSet = new Set<string>();
  const diseasesSet = new Set<string>();

  // 用来追踪是否有空值
  let hasEmptyTissueType = false;
  let hasEmptyCellLine = false;
  let hasEmptyDisease = false;

  projectIds.forEach(projectId => {
    const projectInfo = getProjectInfo(projectId);
    if (projectInfo) {
      // 添加组织类型（如果存在且非空）
      if (projectInfo.tissueOrCellType && projectInfo.tissueOrCellType.trim()) {
        tissueTypesSet.add(projectInfo.tissueOrCellType.trim());
      } else {
        hasEmptyTissueType = true;
      }
      
      // 添加细胞系（如果存在且非空）
      if (projectInfo.cellLine && projectInfo.cellLine.trim()) {
        cellLinesSet.add(projectInfo.cellLine.trim());
      } else {
        hasEmptyCellLine = true;
      }
      
      // 添加疾病信息（如果存在且非空）
      if (projectInfo.disease && projectInfo.disease.trim()) {
        // 处理可能的多个疾病，用分号分隔
        const diseases = projectInfo.disease.split(';').map(d => d.trim()).filter(d => d);
        diseases.forEach(disease => diseasesSet.add(disease));
      } else {
        hasEmptyDisease = true;
      }
    }
  });

  // 如果有空值，添加NA选项
  const tissueTypes = Array.from(tissueTypesSet).sort();
  if (hasEmptyTissueType) {
    tissueTypes.unshift('NA'); // 将NA放在最前面
  }

  const cellLines = Array.from(cellLinesSet).sort();
  if (hasEmptyCellLine) {
    cellLines.unshift('NA'); // 将NA放在最前面
  }

  const diseases = Array.from(diseasesSet).sort();
  if (hasEmptyDisease) {
    diseases.unshift('NA'); // 将NA放在最前面
  }

  return {
    tissueTypes,
    cellLines,
    diseases
  };
};

// 基于projectId列表动态生成过滤关系
export const generateDynamicFilterRelations = (projectIdString: string) => {
  if (!projectIdString) {
    return {
      tissueTypes: [],
      cellLines: [],
      diseases: []
    };
  }

  const projectIds = projectIdString.split(';').map(id => id.trim()).filter(id => id);
  
  // 获取所有项目信息
  const projectInfos = projectIds.map(id => getProjectInfo(id)).filter(info => info !== null);
  
  // 基于实际项目数据动态生成过滤选项
  const tissueTypesSet = new Set<string>();
  const cellLinesSet = new Set<string>();
  const diseasesSet = new Set<string>();

  // 用来追踪是否有空值
  let hasEmptyTissueType = false;
  let hasEmptyCellLine = false;
  let hasEmptyDisease = false;

  projectInfos.forEach(project => {
    if (project!.tissueOrCellType && project!.tissueOrCellType.trim()) {
      tissueTypesSet.add(project!.tissueOrCellType.trim());
    } else {
      hasEmptyTissueType = true;
    }
    
    if (project!.cellLine && project!.cellLine.trim()) {
      cellLinesSet.add(project!.cellLine.trim());
    } else {
      hasEmptyCellLine = true;
    }
    
    if (project!.disease && project!.disease.trim()) {
      const diseases = project!.disease.split(';').map(d => d.trim()).filter(d => d);
      diseases.forEach(disease => diseasesSet.add(disease));
    } else {
      hasEmptyDisease = true;
    }
  });

  // 如果有空值，添加NA选项
  const tissueTypes = Array.from(tissueTypesSet).sort();
  if (hasEmptyTissueType) {
    tissueTypes.unshift('NA');
  }

  const cellLines = Array.from(cellLinesSet).sort();
  if (hasEmptyCellLine) {
    cellLines.unshift('NA');
  }

  const diseases = Array.from(diseasesSet).sort();
  if (hasEmptyDisease) {
    diseases.unshift('NA');
  }

  return {
    tissueTypes,
    cellLines,
    diseases
  };
};

// 基于实际projectId数据的智能过滤逻辑
export const getFilteredOptionsBasedOnSelection = (
  projectIdString: string,
  selectedTissueTypes: string[],
  selectedCellLines: string[],
  selectedDiseases: string[]
) => {
  if (!projectIdString) {
    return {
      tissueTypes: [],
      cellLines: [],
      diseases: []
    };
  }

  const projectIds = projectIdString.split(';').map(id => id.trim()).filter(id => id);
  const projectInfos = projectIds.map(id => getProjectInfo(id)).filter(info => info !== null);

  // 如果没有选择任何条件，返回所有可用选项
  if (selectedTissueTypes.length === 0 && selectedCellLines.length === 0 && selectedDiseases.length === 0) {
    return generateDynamicFilterRelations(projectIdString);
  }

  // 根据已选择的条件过滤出符合条件的项目
  let matchingProjects = projectInfos;

  // 如果选择了组织类型，首先按组织类型过滤
  if (selectedTissueTypes.length > 0) {
    
    matchingProjects = matchingProjects.filter(project => 
      selectedTissueTypes.some(selected => {
        if (selected === 'NA') {
          // 如果选择了NA，匹配空值或空字符串
          return !project!.tissueOrCellType || !project!.tissueOrCellType.trim();
        }
        const match = project!.tissueOrCellType === selected;
        return match;
      })
    );
  }

  // 如果选择了细胞系，进一步按细胞系过滤
  if (selectedCellLines.length > 0) {
    matchingProjects = matchingProjects.filter(project => 
      selectedCellLines.some(selected => {
        if (selected === 'NA') {
          // 如果选择了NA，匹配空值或空字符串
          return !project!.cellLine || !project!.cellLine.trim();
        }
        return project!.cellLine === selected;
      })
    );
  }

  // 如果选择了疾病，进一步按疾病过滤
  if (selectedDiseases.length > 0) {
    matchingProjects = matchingProjects.filter(project => 
      selectedDiseases.some(selected => {
        if (selected === 'NA') {
          // 如果选择了NA，匹配空值或空字符串
          return !project!.disease || !project!.disease.trim();
        }
        if (project!.disease) {
          const diseases = project!.disease.split(';').map(d => d.trim());
          return diseases.includes(selected);
        }
        return false;
      })
    );
  }

  // 基于匹配的项目生成可用的过滤选项
  const availableTissueTypes = new Set<string>();
  const availableCellLines = new Set<string>();
  const availableDiseases = new Set<string>();

  // 用来追踪是否有空值
  let hasEmptyTissueType = false;
  let hasEmptyCellLine = false;
  let hasEmptyDisease = false;

  matchingProjects.forEach(project => {
    if (project!.tissueOrCellType && project!.tissueOrCellType.trim()) {
      availableTissueTypes.add(project!.tissueOrCellType.trim());
    } else {
      hasEmptyTissueType = true;
    }
    
    if (project!.cellLine && project!.cellLine.trim()) {
      availableCellLines.add(project!.cellLine.trim());
    } else {
      hasEmptyCellLine = true;
    }
    
    if (project!.disease && project!.disease.trim()) {
      const diseases = project!.disease.split(';').map(d => d.trim()).filter(d => d);
      diseases.forEach(disease => availableDiseases.add(disease));
    } else {
      hasEmptyDisease = true;
    }
  });

  // 如果有空值，添加NA选项
  const tissueTypes = Array.from(availableTissueTypes).sort();
  if (hasEmptyTissueType) {
    tissueTypes.unshift('NA');
  }

  const cellLines = Array.from(availableCellLines).sort();
  if (hasEmptyCellLine) {
    cellLines.unshift('NA');
  }

  const diseases = Array.from(availableDiseases).sort();
  if (hasEmptyDisease) {
    diseases.unshift('NA');
  }

  return {
    tissueTypes,
    cellLines,
    diseases
  };
};

// 调试函数：测试过滤联动逻辑
export const testFilterLinkage = (projectIdString: string, selectedTissue?: string) => {
  if (!projectIdString) return null;
  
  const projectIds = projectIdString.split(';').map(id => id.trim()).filter(id => id);
  
  if (selectedTissue) {
    const matchingProjects = projectIds
      .map(id => getProjectInfo(id))
      .filter(info => info !== null && info!.tissueOrCellType === selectedTissue);
    
    
    
    const cellLines = new Set<string>();
    const diseases = new Set<string>();
    
    matchingProjects.forEach(project => {
      if (project!.cellLine && project!.cellLine.trim()) {
        cellLines.add(project!.cellLine.trim());
      }
      if (project!.disease && project!.disease.trim()) {
        const diseaseList = project!.disease.split(';').map(d => d.trim()).filter(d => d);
        diseaseList.forEach(disease => diseases.add(disease));
      }
    });
    
    
    return {
      matchingProjectCount: matchingProjects.length,
      availableCellLines: Array.from(cellLines).sort(),
      availableDiseases: Array.from(diseases).sort()
    };
  }
  
  return null;
};

// CSV下载功能
export const downloadCSV = (data: (KEGGAnnotationData | GOAnnotationData)[], analysisType: AnalysisType) => {
  // CSV转义函数：处理包含特殊字符的值，确保符合RFC 4180标准
  const escapeCsvValue = (value: string | number | null | undefined): string => {
    if (value === null || value === undefined) return '';
    const str = String(value);

    // 如果包含逗号、双引号或换行符，需要用双引号包围，并转义内部的双引号
    if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
      return `"${str.replace(/"/g, '""')}"`;
    }

    return str;
  };

  let headers: string[];
  let rows: string[][];

  if (analysisType === 'KEGG analysis') {
    headers = ['GENE SYMBOL', 'GENE ID', 'PATHWAY DESCRIPTION', 'PATHWAY ID'];
    rows = data.map(item => {
      const keggItem = item as KEGGAnnotationData;
      return [
        escapeCsvValue(keggItem.geneSymbol || 'NA'),
        escapeCsvValue(keggItem.geneId || 'NA'),
        escapeCsvValue(keggItem.pathwayDescription || 'NA'),
        escapeCsvValue(keggItem.pathwayId || 'NA')
      ];
    });
  } else {
    headers = ['GENE SYMBOL', 'GENE ID', 'GO TERM', 'GO DOMAIN', 'GO ID'];
    rows = data.map(item => {
      const goItem = item as GOAnnotationData;
      return [
        escapeCsvValue(goItem.geneSymbol || 'NA'),
        escapeCsvValue(goItem.geneId || 'NA'),
        escapeCsvValue(goItem.goTerm || 'NA'),
        escapeCsvValue(goItem.goDomain || 'NA'),
        escapeCsvValue(goItem.goId || 'NA')
      ];
    });
  }

  const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.setAttribute('href', url);
  link.setAttribute('download', `${analysisType.replace(' ', '_')}_data.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// 排序功能
export const sortData = <T extends KEGGAnnotationData | GOAnnotationData>(
  data: T[], 
  field: string, 
  direction: 'asc' | 'desc'
): T[] => {
  return [...data].sort((a, b) => {
    let aValue: string | number | null | undefined, bValue: string | number | null | undefined;
    
    if (field in a) {
      // 使用未知类型过渡到可索引记录，避免联合类型直接断言引起的兼容性告警
      aValue = (a as unknown as Record<string, string | number | null | undefined>)[String(field)];
      bValue = (b as unknown as Record<string, string | number | null | undefined>)[String(field)];
    } else {
      return 0;
    }

    // 处理null和undefined值
    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return direction === 'asc' ? 1 : -1;
    if (bValue == null) return direction === 'asc' ? -1 : 1;

    // 字符串比较
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return direction === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // 数值比较
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return direction === 'asc' ? aValue - bValue : bValue - aValue;
    }

    // 转换为字符串进行比较
    const aStr = String(aValue);
    const bStr = String(bValue);
    return direction === 'asc' 
      ? aStr.localeCompare(bStr)
      : bStr.localeCompare(aStr);
  });
};

// 分页功能
export const paginateData = <T>(data: T[], page: number, pageSize: number): T[] => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return data.slice(startIndex, endIndex);
};

// 计算总页数
export const getTotalPages = (totalItems: number, pageSize: number): number => {
  return Math.ceil(totalItems / pageSize);
};

// 过滤数据功能
export const filterDataByKeyword = <T extends KEGGAnnotationData | GOAnnotationData>(
  data: T[], 
  keyword: string
): T[] => {
  if (!keyword.trim()) return data;
  
  const lowerKeyword = keyword.toLowerCase();
  return data.filter(item => {
    return Object.values(item).some(value => 
      value != null && String(value).toLowerCase().includes(lowerKeyword)
    );
  });
};

// 获取可用的数据类型
export const getAvailableDataTypes = (translationData: Array<{te?: number | null, tr?: number | null, evi?: number | null}>): Array<{type: string, available: boolean}> => {
  if (!translationData || translationData.length === 0) {
    return [
      { type: 'TE', available: false },
      { type: 'TR', available: false },
      { type: 'EVI', available: false }
    ];
  }

  const hasTE = translationData.some(item => item.te !== null && item.te !== undefined);
  const hasTR = translationData.some(item => item.tr !== null && item.tr !== undefined);
  const hasEVI = translationData.some(item => item.evi !== null && item.evi !== undefined);

  return [
    { type: 'TE', available: hasTE },
    { type: 'TR', available: hasTR },
    { type: 'EVI', available: hasEVI }
  ];
};

// API调用工具函数
export const fetchFilterData = async (searchTerm: string, analysisType: AnalysisType) => {
  if (!searchTerm) return null;

  try {
    let endpoint = '';
    if (analysisType === 'KEGG analysis') {
      endpoint = `${process.env.NEXT_PUBLIC_API_URL}/keggpathwaysummarylast/search/pathway/${encodeURIComponent(searchTerm)}`;
    } else if (analysisType === 'GO analysis') {
      endpoint = `${process.env.NEXT_PUBLIC_API_URL}/gotermsummarylast/search/goterm/${encodeURIComponent(searchTerm)}`;
    }

    const response = await fetch(endpoint);
    if (!response.ok) {
      throw new Error(`Failed to fetch filter data: ${response.status}`);
    }

    const data = await response.json();
    const firstItem = Array.isArray(data) && data.length > 0 ? data[0] : data;

    // 使用新的函数从projectId列表中提取过滤选项
    const filterOptions = extractFilterOptionsFromProjectIds(firstItem.translationProjectIds);

    return { 
      tissueTypes: filterOptions.tissueTypes, 
      cellLines: filterOptions.cellLines, 
      diseases: filterOptions.diseases,
      projectIds: firstItem.translationProjectIds || ''
    };
  } catch (error) {
    console.error('Error loading filter data:', error);
    throw error;
  }
};

// 获取选项数据
export const fetchOptions = async (analysisType: AnalysisType) => {
  try {
    if (analysisType === 'KEGG analysis') {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/keggpathwaysummarylast/all-pathway-descriptions`);
      if (!response.ok) throw new Error('Failed to fetch pathway descriptions');
      const data = await response.json();
      return data; // 返回所有数据
    } else if (analysisType === 'GO analysis') {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/gotermsummarylast/all-goterm`);
      if (!response.ok) throw new Error('Failed to fetch GO terms');
      const data = await response.json();
      return data; // 返回所有数据
    }
    return [];
  } catch (error) {
    console.error('Error loading options:', error);
    throw error;
  }
};

// 检查projectId列表是否匹配过滤条件
export const checkProjectIdsMatchFilters = (
  projectIdString: string,
  selectedTissueTypes: string[],
  selectedCellLines: string[],
  selectedDiseases: string[]
): boolean => {
  if (!projectIdString) return false;
  
  const projectIds = projectIdString.split(';').map(id => id.trim()).filter(id => id);
  
  // 如果没有选择任何过滤条件，显示所有数据
  if (selectedTissueTypes.length === 0 && selectedCellLines.length === 0 && selectedDiseases.length === 0) {
    return true;
  }
  
  // 检查是否有任何projectId完全匹配所有选择的过滤条件
  return projectIds.some(projectId => {
    const projectInfo = getProjectInfo(projectId);
    if (!projectInfo) return false;
    
    // 检查组织类型匹配（如果选择了组织类型）
    if (selectedTissueTypes.length > 0) {
      const matchesTissue = selectedTissueTypes.some(selected => {
        if (selected === 'NA') {
          // 如果选择了NA，匹配空值或空字符串
          return !projectInfo.tissueOrCellType || !projectInfo.tissueOrCellType.trim();
        }
        return projectInfo.tissueOrCellType === selected;
      });
      if (!matchesTissue) return false;
    }
    
    // 检查细胞系匹配（如果选择了细胞系）
    if (selectedCellLines.length > 0) {
      const matchesCellLine = selectedCellLines.some(selected => {
        if (selected === 'NA') {
          // 如果选择了NA，匹配空值或空字符串
          return !projectInfo.cellLine || !projectInfo.cellLine.trim();
        }
        return projectInfo.cellLine === selected;
      });
      if (!matchesCellLine) return false;
    }
    
    // 检查疾病匹配（如果选择了疾病）
    if (selectedDiseases.length > 0) {
      const matchesDisease = selectedDiseases.some(selected => {
        if (selected === 'NA') {
          // 如果选择了NA，匹配空值或空字符串
          return !projectInfo.disease || !projectInfo.disease.trim();
        }
        // 处理疾病字段可能包含多个疾病的情况，用分号分隔
        if (projectInfo.disease) {
          const diseases = projectInfo.disease.split(';').map(d => d.trim());
          return diseases.includes(selected);
        }
        return false;
      });
      if (!matchesDisease) return false;
    }
    
    // 所有选择的条件都匹配
    return true;
  });
};

// 获取注解数据
export const fetchAnnotationData = async (searchTerm: string, analysisType: AnalysisType) => {
  try {
    let endpoint = '';
    if (analysisType === 'KEGG analysis') {
      endpoint = `${process.env.NEXT_PUBLIC_API_URL}/keggannotationlast/pathway/${encodeURIComponent(searchTerm)}`;
    } else {
      endpoint = `${process.env.NEXT_PUBLIC_API_URL}/goannotationlast/goterm/${encodeURIComponent(searchTerm)}`;
    }

    const response = await fetch(endpoint);
    if (!response.ok) throw new Error('Failed to fetch annotation data');
    return await response.json();
  } catch (error) {
    console.error('Error loading annotation data:', error);
    throw error;
  }
};