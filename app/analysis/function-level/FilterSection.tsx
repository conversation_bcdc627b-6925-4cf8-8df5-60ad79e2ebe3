import React from 'react';
import { FilterSectionProps } from './types';

export default function FilterSection({
  availableTissueCellTypes,
  availableCellLines,
  availableDiseases,
  selectedTissueCellTypes,
  selectedCellLines,
  selectedDiseases,
  onTissueCellTypeChange,
  onCellLineChange,
  onDiseaseChange,
  showMoreTissues,
  showMoreCellLines,
  showMoreDiseases,
  setShowMoreTissues,
  setShowMoreCellLines,
  setShowMoreDiseases
}: FilterSectionProps) {
  
  // 简化：直接使用传入的可用选项，不再进行内部过滤
  // 过滤逻辑已移至主页面的getFilteredOptions函数中，基于实际projectId数据
  const filteredOptions = {
    availableTissueTypes: availableTissueCellTypes,
    availableCellLines: availableCellLines,
    availableDiseases: availableDiseases
  };

  // 处理组织类型选择
  const handleTissueChange = (tissue: string, checked: boolean) => {
    if (checked) {
      onTissueCellTypeChange([tissue]); // 单选
      // 选择组织类型时，清空其他选择以触发联动更新
      onCellLineChange([]); 
      onDiseaseChange([]);
    } else {
      onTissueCellTypeChange([]);
    }
  };

  // 处理细胞系选择
  const handleCellLineChange = (cellLine: string, checked: boolean) => {
    if (checked) {
      onCellLineChange([cellLine]); // 单选
      // 选择细胞系时，清空组织类型和疾病选择以触发联动更新
      onTissueCellTypeChange([]);
      onDiseaseChange([]);
    } else {
      onCellLineChange([]);
    }
  };

  // 处理疾病选择 - 支持多选，不清空其他选择
  const handleDiseaseChange = (disease: string, checked: boolean) => {
    if (checked) {
      if (!selectedDiseases.includes(disease)) {
        const newSelectedDiseases = [...selectedDiseases, disease];
        onDiseaseChange(newSelectedDiseases);
      }
    } else {
      onDiseaseChange(selectedDiseases.filter(d => d !== disease));
    }
  };

  // 渲染过滤器组
  const renderFilterGroup = (
    title: string,
    options: string[],
    selectedOptions: string[],
    onOptionChange: (option: string, checked: boolean) => void,
    showMore: boolean,
    setShowMore: (show: boolean) => void,
    maxVisible: number = 5
  ) => {
    const visibleOptions = showMore ? options : options.slice(0, maxVisible);
    
    return (
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-3">{title}</h3>
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {visibleOptions.map((option) => (
            <label key={option} className="flex items-center">
              <input
                type="checkbox"
                checked={selectedOptions.includes(option)}
                onChange={(e) => onOptionChange(option, e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">{option}</span>
            </label>
          ))}
        </div>
        {options.length > maxVisible && (
          <button
            onClick={() => setShowMore(!showMore)}
            className="mt-2 text-sm text-blue-600 hover:text-blue-800"
          >
            {showMore ? 'Show Less' : `Show More (${options.length - maxVisible} more)`}
          </button>
        )}
      </div>
    );
  };

  if (availableTissueCellTypes.length === 0 && availableCellLines.length === 0 && availableDiseases.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Filter Options</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 组织/细胞类型过滤器 */}
        {filteredOptions.availableTissueTypes.length > 0 && renderFilterGroup(
          'Tissue/Cell Type',
          filteredOptions.availableTissueTypes,
          selectedTissueCellTypes,
          handleTissueChange,
          showMoreTissues,
          setShowMoreTissues
        )}

        {/* 细胞系过滤器 */}
        {filteredOptions.availableCellLines.length > 0 && renderFilterGroup(
          'Cell Line',
          filteredOptions.availableCellLines,
          selectedCellLines,
          handleCellLineChange,
          showMoreCellLines,
          setShowMoreCellLines
        )}

        {/* 疾病过滤器 */}
        {filteredOptions.availableDiseases.length > 0 && renderFilterGroup(
          'Disease/Condition',
          filteredOptions.availableDiseases,
          selectedDiseases,
          handleDiseaseChange,
          showMoreDiseases,
          setShowMoreDiseases
        )}
      </div>

      {/* 清除所有过滤器 */}
      {(selectedTissueCellTypes.length > 0 || selectedCellLines.length > 0 || selectedDiseases.length > 0) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={() => {
              onTissueCellTypeChange([]);
              onCellLineChange([]);
              onDiseaseChange([]);
            }}
            className="text-sm text-red-600 hover:text-red-800"
          >
            Clear All Filters
          </button>
        </div>
      )}
    </div>
  );
}