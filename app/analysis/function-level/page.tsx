'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Download, ChevronDown } from 'lucide-react';

import { Input } from "@/app/components/ui/input";
import { 
  KEGGAnnotationData, 
  GOAnnotationData, 
  TranslationIndicesData, 
  GeneData,
  AnalysisType 
} from './types';
import { 
  downloadCSV, 
  fetchFilterData,
  fetchOptions,
  fetchAnnotationData,
  checkProjectIdsMatchFilters,
  getFilteredOptionsBasedOnSelection,
  testFilterLinkage
} from './utils';
import TranscriptLevelDisplay from './components/TranscriptLevelDisplay';
import GeneLevelDisplay from './components/GeneLevelDisplay';

export default function CompareGeneKEGGPage() {
  const [analysisType, setAnalysisType] = useState<AnalysisType>('KEGG analysis');
  const [resultType, setResultType] = useState<'Transcript' | 'Gene'>('Gene');
  const [searchInput, setSearchInput] = useState<string>('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [showAnalysisDropdown, setShowAnalysisDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  
  // 移除强制设置的useEffect
  
  // Error message state
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Data state
  const [pathwayDescriptions, setPathwayDescriptions] = useState<string[]>([]);
  const [goTerms, setGoTerms] = useState<string[]>([]);
  const [filteredOptions, setFilteredOptions] = useState<string[]>([]);
  const [selectedSearchTerm, setSelectedSearchTerm] = useState<string>('');

  // Filter data
  // 注意：这些变量被移除了，因为现在使用动态计算的过滤选项

  // Filter selection state - changed to array format, default empty
  const [selectedTissueCellTypes, setSelectedTissueCellTypes] = useState<string[]>([]);
  const [selectedCellLines, setSelectedCellLines] = useState<string[]>([]);
  const [selectedDiseases, setSelectedDiseases] = useState<string[]>([]);

  // Table data
  const [keggTableData, setKeggTableData] = useState<KEGGAnnotationData[]>([]);
  const [goTableData, setGoTableData] = useState<GOAnnotationData[]>([]);
  const [selectedGeneIds, setSelectedGeneIds] = useState<Set<string>>(new Set());

  // Translation data and chart state
  const [translationData, setTranslationData] = useState<TranslationIndicesData[]>([]);
  const [geneData, setGeneData] = useState<GeneData[]>([]);
  const [showResults, setShowResults] = useState(false);

  // 存储当前pathway/goterm的projectId信息
  const [currentProjectIds, setCurrentProjectIds] = useState<string>('');

  // Sort state
  const [sortField, setSortField] = useState<string>('geneSymbol');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Search and download state
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [showDownloadOptions, setShowDownloadOptions] = useState<boolean>(false);
  const [showDisplaySelect, setShowDisplaySelect] = useState<boolean>(true);

  // Pagination state
  const [keggCurrentPage, setKeggCurrentPage] = useState<number>(1);
  const [keggPageSize, setKeggPageSize] = useState<number>(10);
  const [goCurrentPage, setGoCurrentPage] = useState<number>(1);
  const [goPageSize, setGoPageSize] = useState<number>(10);

  // References
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const analysisDropdownRef = useRef<HTMLDivElement>(null);


// Load filter data
const loadFilterData = useCallback(async (searchTerm: string) => {
  if (!searchTerm) {
    return;
  }

  try {
    const result = await fetchFilterData(searchTerm, analysisType);
    if (result) {
      // 设置currentProjectIds以便过滤器选项能够显示
      setCurrentProjectIds(result.projectIds || '');
    }
  } catch (error) {
    console.error('Error loading filter data:', error);
    setError('Failed to load filter data');
  }
}, [analysisType]);

// Load options data
useEffect(() => {
  const loadOptions = async () => {
    try {
      const data = await fetchOptions(analysisType);
      
      if (analysisType === 'KEGG analysis') {
        setPathwayDescriptions(data);
        setFilteredOptions(data);

        // 自动选择第一个KEGG pathway并加载数据
        if (data.length > 0 && !selectedSearchTerm) {
          const firstPathway = data[0];
          setSearchInput(firstPathway);
          setSelectedSearchTerm(firstPathway);
          // 自动提交第一个选项
          setTimeout(async () => {
            try {
              setIsLoading(true);
              setError('');

              const annotationData = await fetchAnnotationData(firstPathway, analysisType);
              setKeggTableData(annotationData);
              setGoTableData([]);

              // 使用loadFilterData函数加载筛选器数据
              await loadFilterData(firstPathway);
            } catch (error) {
              console.error('Error loading initial data:', error);
              setError('Failed to load initial data');
            } finally {
              setIsLoading(false);
            }
          }, 100);
        }
      } else if (analysisType === 'GO analysis') {
        setGoTerms(data);
        setFilteredOptions(data);

        // 自动选择第一个GO term并加载数据
        if (data.length > 0 && !selectedSearchTerm) {
          const firstGoTerm = data[0];
          setSearchInput(firstGoTerm);
          setSelectedSearchTerm(firstGoTerm);
          // 自动提交第一个选项
          setTimeout(async () => {
            try {
              setIsLoading(true);
              setError('');

              const annotationData = await fetchAnnotationData(firstGoTerm, analysisType);
              setGoTableData(annotationData);
              setKeggTableData([]);

              // 使用loadFilterData函数加载筛选器数据
              await loadFilterData(firstGoTerm);
            } catch (error) {
              console.error('Error loading initial GO data:', error);
              setError('Failed to load initial data');
            } finally {
              setIsLoading(false);
            }
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error loading options:', error);
      setError('Failed to load options');
    }
  };

  loadOptions();
}, [analysisType, selectedSearchTerm, loadFilterData]);


  // 处理搜索输入变化 - 改进搜索建议
  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
    setError(''); // 清除之前的错误
    
    const options = analysisType === 'KEGG analysis' ? pathwayDescriptions : goTerms;
    
    if (value.length === 0) {
      setFilteredOptions([]);
      setShowDropdown(false);
      return;
    }
    
    // 多种匹配策略
    const exactMatch = options.filter(option => 
      option.toLowerCase() === value.toLowerCase()
    );
    
    const startsWithMatch = options.filter(option => 
      option.toLowerCase().startsWith(value.toLowerCase()) && 
      option.toLowerCase() !== value.toLowerCase()
    );
    
    const containsMatch = options.filter(option => 
      option.toLowerCase().includes(value.toLowerCase()) && 
      !option.toLowerCase().startsWith(value.toLowerCase()) &&
      option.toLowerCase() !== value.toLowerCase()
    );
    
    // 按优先级组合结果：精确匹配 > 开头匹配 > 包含匹配
    const combinedResults = [
      ...exactMatch,
      ...startsWithMatch.slice(0, 15), // 限制开头匹配的数量
      ...containsMatch.slice(0, 20)    // 限制包含匹配的数量
    ].slice(0, 20); // 总共最多显示20个结果用于下拉框
    
    setFilteredOptions(combinedResults);
    setShowDropdown(combinedResults.length > 0);
    
  };

  // 处理搜索项选择
  const handleSearchSelect = (term: string) => {
    setSearchInput(term);
    setSelectedSearchTerm(term);
    setShowDropdown(false);
    // 清除之前的错误
    setError('');
    
    // 直接加载filter data而不依赖loadFilterData函数
    const loadFilterDataForTerm = async () => {
      try {
        let endpoint = '';
        if (analysisType === 'KEGG analysis') {
          endpoint = `${process.env.NEXT_PUBLIC_API_URL}/keggpathwaysummarylast/search/pathway/${encodeURIComponent(term)}`;
        } else {
          endpoint = `${process.env.NEXT_PUBLIC_API_URL}/gotermsummarylast/search/goterm/${encodeURIComponent(term)}`;
        }

        const response = await fetch(endpoint);
        if (response.ok) {
          const data = await response.json();

          // API 返回的是数组，取第一个元素
          const firstItem = Array.isArray(data) && data.length > 0 ? data[0] : data;

          // 过滤选项现在通过useMemo动态计算，不需要设置状态
          
          // 保存当前的projectIds供后续过滤使用
          setCurrentProjectIds(firstItem.translationProjectIds || '');
        }
      } catch (error) {
        console.error('Error loading filter data:', error);
      }
    };
    
    loadFilterDataForTerm();
  };

  // 处理带参数的Submit - 改进支持搜索建议
  const handleSubmitWithTerm = useCallback(async (term: string) => {
    if (!term) return;

    setIsLoading(true);
    setError('');

    try {
      // 先尝试精确匹配
      let exactMatch = null;
      const options = analysisType === 'KEGG analysis' ? pathwayDescriptions : goTerms;
      exactMatch = options.find(option => 
        option.toLowerCase() === term.toLowerCase()
      );

      // 如果没有精确匹配，尝试查找部分匹配
      let searchTerm = exactMatch || term;
      
      // 如果输入的不是完整的选项，尝试找最佳匹配
      if (!exactMatch && term.length >= 2) {
        // 多种匹配策略：精确匹配 > 开头匹配 > 包含匹配
        const exactPartialMatch = options.filter(option => 
          option.toLowerCase() === term.toLowerCase()
        );
        
        const startsWithMatch = options.filter(option => 
          option.toLowerCase().startsWith(term.toLowerCase()) && 
          option.toLowerCase() !== term.toLowerCase()
        );
        
        const containsMatch = options.filter(option => 
          option.toLowerCase().includes(term.toLowerCase()) && 
          !option.toLowerCase().startsWith(term.toLowerCase()) &&
          option.toLowerCase() !== term.toLowerCase()
        );
        
        // 按优先级组合结果
        const allMatches = [
          ...exactPartialMatch,
          ...startsWithMatch.slice(0, 15),
          ...containsMatch.slice(0, 25)
        ].slice(0, 20); // 限制用于选择的匹配项数量
        
        if (allMatches.length === 1) {
          // 如果只有一个匹配，使用它
          searchTerm = allMatches[0];
          setSearchInput(searchTerm); // 更新输入框显示完整匹配
        } else if (allMatches.length > 1) {
          // If multiple matches, show dropdown for selection
          setFilteredOptions(allMatches);
          setShowDropdown(true);
          setError(`Found ${allMatches.length} matching ${analysisType === 'KEGG analysis' ? 'pathway descriptions' : 'GO terms'}. Please select one from the dropdown.`);
          setIsLoading(false);
          return;
        } else {
          // No match found
          setError(`No matching ${analysisType === 'KEGG analysis' ? 'pathway description' : 'GO term'} found for "${term}". Please check your input.`);
          setIsLoading(false);
          return;
        }
      }

      let endpoint = '';
      if (analysisType === 'KEGG analysis') {
        endpoint = `${process.env.NEXT_PUBLIC_API_URL}/keggannotationlast/pathway/${encodeURIComponent(searchTerm)}`;
      } else {
        endpoint = `${process.env.NEXT_PUBLIC_API_URL}/goannotationlast/goterm/${encodeURIComponent(searchTerm)}`;
      }

      const response = await fetch(endpoint);
      if (!response.ok) {
        throw new Error(`Failed to fetch annotation data for "${searchTerm}"`);
      }
      const data = await response.json();

      if (!data || data.length === 0) {
        setError(`No data found for "${searchTerm}". Please try another ${analysisType === 'KEGG analysis' ? 'pathway description' : 'GO term'}.`);
        setIsLoading(false);
        return;
      }

      if (analysisType === 'KEGG analysis') {
        setKeggTableData(data);
        setGoTableData([]);
      } else {
        setGoTableData(data);
        setKeggTableData([]);
      }

      setSelectedSearchTerm(searchTerm);
      setShowDropdown(false); // 关闭下拉框
      
      // 成功获取注释数据后加载filter data
      try {
        let filterEndpoint = '';
        if (analysisType === 'KEGG analysis') {
          filterEndpoint = `${process.env.NEXT_PUBLIC_API_URL}/keggpathwaysummarylast/search/pathway/${encodeURIComponent(searchTerm)}`;
        } else {
          filterEndpoint = `${process.env.NEXT_PUBLIC_API_URL}/gotermsummarylast/search/goterm/${encodeURIComponent(searchTerm)}`;
        }

        const filterResponse = await fetch(filterEndpoint);
        if (filterResponse.ok) {
          const filterData = await filterResponse.json();

          // API 返回的是数组，取第一个元素
          const firstItem = Array.isArray(filterData) && filterData.length > 0 ? filterData[0] : filterData;

          // 保存当前的projectIds供后续过滤使用
          setCurrentProjectIds(firstItem.translationProjectIds || '');
        }
      } catch (filterError) {
        console.error('Error loading filter data:', filterError);
        // filter data错误不影响主要功能，只记录错误
      }
    } catch (error) {
      console.error('Error submitting:', error);
      setError(`Failed to load data for "${term}". ${error instanceof Error ? error.message : 'Please try again later.'}`);
    } finally {
      setIsLoading(false);
    }
  }, [analysisType, pathwayDescriptions, goTerms]);

  // 处理Submit
  const handleSubmit = async () => {
    // 只有在搜索输入完全匹配可用选项时才提交
    const options = analysisType === 'KEGG analysis' ? pathwayDescriptions : goTerms;

    const exactMatch = options.find(option =>
      option.toLowerCase() === searchInput.toLowerCase()
    );

    const termToUse = exactMatch || selectedSearchTerm;

    if (!termToUse) {
      setError('Please select a valid option from the dropdown.');
      return;
    }

    await handleSubmitWithTerm(termToUse);
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          searchInputRef.current && !searchInputRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
      if (analysisDropdownRef.current && !analysisDropdownRef.current.contains(event.target as Node)) {
        setShowAnalysisDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);


  // 处理过滤器选择 - 参考基因页面的逻辑
  const handleFilterSelect = (
    type: 'tissue' | 'cellLine' | 'disease', 
    option: string, 
    checked: boolean
  ) => {
    // 清除错误信息
    setErrorMessage('');
    
    switch (type) {
      case 'tissue':
        if (checked) {
          // 清空细胞系选择（互斥）
          setSelectedCellLines([]);
          setSelectedTissueCellTypes([option]);
        } else {
          setSelectedTissueCellTypes([]);
        }
        break;
      case 'cellLine':
        if (checked) {
          // 清空组织类型选择（互斥）
          setSelectedTissueCellTypes([]);
          setSelectedCellLines([option]);
        } else {
          setSelectedCellLines([]);
        }
        break;
      case 'disease':
        // 疾病支持多选
        if (checked) {
          if (!selectedDiseases.includes(option)) {
            setSelectedDiseases([...selectedDiseases, option]);
          }
        } else {
          setSelectedDiseases(selectedDiseases.filter(d => d !== option));
        }
        break;
    }
  };



  // 处理基因选择
  const handleGeneSelect = (geneId: string, checked: boolean) => {
    // 清除错误信息
    setErrorMessage('');

    const newSelected = new Set(selectedGeneIds);
    if (checked) {
      newSelected.add(geneId);
    } else {
      newSelected.delete(geneId);
    }
    setSelectedGeneIds(newSelected);
  };

  // 排序功能
  const sortTableData = (data: (KEGGAnnotationData | GOAnnotationData)[]) => {
    return [...data].sort((a, b) => {
      const aValue = a[sortField as keyof (KEGGAnnotationData | GOAnnotationData)] || '';
      const bValue = b[sortField as keyof (KEGGAnnotationData | GOAnnotationData)] || '';

      // 字符串排序
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });
  };

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <div className="flex flex-col ml-1 text-xs leading-none">
          <span className="text-gray-300 mb-0.5">▲</span>
          <span className="text-gray-300">▼</span>
        </div>
      );
    }

    return (
      <div className="flex flex-col ml-1 text-xs leading-none">
        <span className={`${sortDirection === 'asc' ? 'text-[#337ab7]' : 'text-gray-300'} mb-0.5`}>▲</span>
        <span className={`${sortDirection === 'desc' ? 'text-[#337ab7]' : 'text-gray-300'}`}>▼</span>
      </div>
    );
  };

  // 当排序字段或方向改变时，强制重新渲染
  useEffect(() => {
    // 这个effect确保排序状态变化时组件重新渲染
  }, [sortField, sortDirection]);

  // 下载选项引用
  const downloadOptionsRef = useRef<HTMLDivElement>(null);

  // 监控showDownloadOptions状态变化
  useEffect(() => {
    // State change monitoring
  }, [showDownloadOptions]);

  // 点击外部关闭下载选项
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (downloadOptionsRef.current && !downloadOptionsRef.current.contains(event.target as Node)) {
        if (showDownloadOptions) {
          setShowDownloadOptions(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDownloadOptions]);

  // 分页相关函数
  const getKeggPaginatedData = () => {
    const filteredData = getFilteredTableData();
    const currentData = analysisType === 'KEGG analysis' ? filteredData : keggTableData;
    const searchedData = filterTableData(currentData, searchKeyword);
    const sortedData = sortTableData(searchedData);
    const startIndex = (keggCurrentPage - 1) * keggPageSize;
    const endIndex = startIndex + keggPageSize;
    return {
      data: sortedData.slice(startIndex, endIndex),
      total: sortedData.length,
      totalPages: Math.ceil(sortedData.length / keggPageSize)
    };
  };

  const getGoPaginatedData = () => {
    const filteredData = getFilteredTableData();
    const currentData = analysisType === 'GO analysis' ? filteredData : goTableData;
    const searchedData = filterTableData(currentData, searchKeyword);
    const sortedData = sortTableData(searchedData);
    const startIndex = (goCurrentPage - 1) * goPageSize;
    const endIndex = startIndex + goPageSize;
    return {
      data: sortedData.slice(startIndex, endIndex),
      total: sortedData.length,
      totalPages: Math.ceil(sortedData.length / goPageSize)
    };
  };

  // 分页按钮渲染函数
  const renderKeggPagination = () => {
    const { totalPages } = getKeggPaginatedData();
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, keggCurrentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (keggCurrentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setKeggCurrentPage(keggCurrentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous
        </button>
      );
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setKeggCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            keggCurrentPage === i
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]'
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
        </button>
      );
    }

    // Next button
    if (keggCurrentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setKeggCurrentPage(keggCurrentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center">{pages}</div>;
  };

  const renderGoPagination = () => {
    const { totalPages } = getGoPaginatedData();
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, goCurrentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (goCurrentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setGoCurrentPage(goCurrentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous
        </button>
      );
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setGoCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            goCurrentPage === i
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]'
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
        </button>
      );
    }

    // Next button
    if (goCurrentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setGoCurrentPage(goCurrentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center">{pages}</div>;
  };

  // 搜索功能
  const filterTableData = (data: (KEGGAnnotationData | GOAnnotationData)[], keyword: string) => {
    if (!keyword.trim()) return data;

    const lowerKeyword = keyword.toLowerCase();
    return data.filter(item =>
      item.geneSymbol?.toLowerCase().includes(lowerKeyword) ||
      item.geneId?.toLowerCase().includes(lowerKeyword) ||
      (analysisType === 'KEGG analysis' &&
        ((item as KEGGAnnotationData).pathwayDescription?.toLowerCase().includes(lowerKeyword) ||
         (item as KEGGAnnotationData).pathwayId?.toLowerCase().includes(lowerKeyword))) ||
      (analysisType === 'GO analysis' &&
        ((item as GOAnnotationData).goTerm?.toLowerCase().includes(lowerKeyword) ||
         (item as GOAnnotationData).goDomain?.toLowerCase().includes(lowerKeyword) ||
         (item as GOAnnotationData).goId?.toLowerCase().includes(lowerKeyword)))
    );
  };

  // 下载功能
  const downloadData = (format: 'csv' | 'json') => {
    const filteredData = getFilteredTableData();

    const currentData = analysisType === 'KEGG analysis' ?
      (filteredData.length > 0 ? filteredData : keggTableData) :
      (filteredData.length > 0 ? filteredData : goTableData);

    const searchedData = filterTableData(currentData, searchKeyword);

    const sortedData = sortTableData(searchedData);

    if (sortedData.length === 0) {
      console.warn('No data to download');
      alert('No data to download. Please ensure there is data in the table.');
      setShowDownloadOptions(false);
      return;
    }

    if (format === 'csv') {
      downloadCSV(sortedData, analysisType);
    } else {
      downloadJSON(sortedData);
    }
    setShowDownloadOptions(false);
  };

  const downloadJSON = (data: (KEGGAnnotationData | GOAnnotationData)[]) => {
    const jsonContent = JSON.stringify(data, null, 2);
    
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    const fileName = analysisType === 'KEGG analysis' ? 'KEGG_Pathway_Analysis' : 'GO_Annotation_Analysis';

    link.setAttribute('href', url);
    link.setAttribute('download', `${fileName}_data.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);

    try {
      link.click();
    } catch (error) {
      console.error('JSON download click failed:', error);
      // 备选方案：直接使用window.open
      window.open(url, '_blank');
    }

    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 1000);
  };

  // 清空选中的基因
  const handleClearGenes = () => {
    setSelectedGeneIds(new Set());
  };

  // 验证选择条件并获取数据
  const handleCalculate = async () => {
    // 清除之前的错误信息
    setErrorMessage('');
    
    // 验证选择条件
    const tissueCellSelected = selectedTissueCellTypes.length > 0;
    const cellLineSelected = selectedCellLines.length > 0;
    const diseaseSelected = selectedDiseases.length > 0;
    const geneSelected = selectedGeneIds.size > 0;

    // 验证NA值 - 新增逻辑
    const hasNATissueCell = selectedTissueCellTypes.some(item => item === 'NA');
    const hasNACellLine = selectedCellLines.some(item => item === 'NA');
    const hasNADisease = selectedDiseases.some(item => item === 'NA');

    // 计算选择的非NA条件总数
    const nonNASelections = [
      tissueCellSelected && !hasNATissueCell,
      cellLineSelected && !hasNACellLine,
      diseaseSelected && !hasNADisease
    ].filter(Boolean).length;

    // 检查是否符合条件
    const validSelection = 
      (tissueCellSelected || cellLineSelected) && // Tissue/Cell Type和Cell Line必须2选1
      !(tissueCellSelected && cellLineSelected) && // 不能同时选择
      diseaseSelected && // Disease必须选一个
      selectedDiseases.length === 1 && // Disease只能选一个
      geneSelected && // 必须选择基因
      nonNASelections >= 2; // 至少选择两种非NA条件

    if (!validSelection) {
      if (nonNASelections < 2) {
        setErrorMessage("Compare requires at least two types of non-NA conditions to be selected (Tissue/Cell Type, Cell Line, and Disease/Condition). NA values are not allowed for comparison.");
      } else {
        setErrorMessage("Tissue/Cell Type and Cell Line must be selected (only one of them), Disease must be selected (only one), and at least one gene must be selected.");
      }
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // 构建API请求参数
      const geneIdsParam = Array.from(selectedGeneIds).join(',');
      const tissueCellType = selectedTissueCellTypes.length > 0 ? selectedTissueCellTypes[0] : '';
      const cellLine = selectedCellLines.length > 0 ? selectedCellLines[0] : '';
      const disease = selectedDiseases[0];

      // 同时获取两种类型的数据
      const transcriptUrl = `${process.env.NEXT_PUBLIC_API_URL}/translation-indices/filter?geneIds=${encodeURIComponent(geneIdsParam)}&tissueCellType=${encodeURIComponent(tissueCellType)}&cellLine=${encodeURIComponent(cellLine)}&disease=${encodeURIComponent(disease)}`;
      const geneUrl = `${process.env.NEXT_PUBLIC_API_URL}/genes/filter?geneIds=${encodeURIComponent(geneIdsParam)}&tissueCellType=${encodeURIComponent(tissueCellType)}&cellLine=${encodeURIComponent(cellLine)}&disease=${encodeURIComponent(disease)}`;

      // 并行请求两种数据
      const [transcriptResponse, geneResponse] = await Promise.all([
        fetch(transcriptUrl),
        fetch(geneUrl)
      ]);

      // 检查响应状态
      if (!transcriptResponse.ok) {
        console.warn('Failed to fetch transcript data, but continuing with gene data');
      }
      if (!geneResponse.ok) {
        console.warn('Failed to fetch gene data, but continuing with transcript data');
      }

      // 解析数据
      let transcriptData = [];
      let geneData = [];

      try {
        if (transcriptResponse.ok) {
          transcriptData = await transcriptResponse.json();
        }
      } catch (error) {
        console.warn('Error parsing transcript data:', error);
      }

      try {
        if (geneResponse.ok) {
          geneData = await geneResponse.json();
        }
      } catch (error) {
        console.warn('Error parsing gene data:', error);
      }

      // 如果两种数据都获取失败，抛出错误
      if (!transcriptResponse.ok && !geneResponse.ok) {
        throw new Error('Failed to fetch both transcript and gene data');
      }

      // 设置数据
      setTranslationData(transcriptData);
      setGeneData(geneData);
      setShowResults(true);

    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  };




  // 展开状态管理
  const [showMoreTissueTypes, setShowMoreTissueTypes] = useState(false);
  const [showMoreCellLines, setShowMoreCellLines] = useState(false);
  const [showMoreDiseases, setShowMoreDiseases] = useState(false);

  // 使用基于实际projectId数据的动态过滤逻辑 - 使用useMemo优化
  const { tissueTypes, cellLines, diseases } = useMemo(() => {
    if (!currentProjectIds) {
      // 没有选择pathway/goterm时，返回空选项
      return {
        tissueTypes: [],
        cellLines: [],
        diseases: []
      };
    }

    // 使用新的智能过滤逻辑，基于已选择的条件动态生成可用选项
    const result = getFilteredOptionsBasedOnSelection(
      currentProjectIds,
      selectedTissueCellTypes,
      selectedCellLines,
      selectedDiseases
    );


    // 如果选择了组织类型，使用测试函数验证
    if (selectedTissueCellTypes.length > 0) {
      testFilterLinkage(currentProjectIds, selectedTissueCellTypes[0]);
    }

    return {
      tissueTypes: result.tissueTypes,
      cellLines: result.cellLines,
      diseases: result.diseases
    };
  }, [currentProjectIds, selectedTissueCellTypes, selectedCellLines, selectedDiseases]);

  // 自动选择Disease的逻辑
  useEffect(() => {
    // 如果Disease只有一个选项，自动选择
    if (diseases.length === 1 && selectedDiseases.length === 0) {
      setSelectedDiseases([diseases[0]]);
    }
    // 如果Disease选项变化，检查当前选择是否仍然有效
    else if (selectedDiseases.length > 0) {
      const validDiseases = selectedDiseases.filter(disease => diseases.includes(disease));
      if (validDiseases.length !== selectedDiseases.length) {
        setSelectedDiseases(validDiseases);
      }
    }
  }, [diseases, selectedDiseases]);

  // 当筛选选项发生变化时，自动清除不再有效的选择
  useEffect(() => {
    // 检查当前选择的疾病是否还在可用的疾病列表中
    if (selectedDiseases.length > 0) {
      const validDiseases = selectedDiseases.filter(disease => diseases.includes(disease));
      if (validDiseases.length !== selectedDiseases.length) {
        setSelectedDiseases(validDiseases);
      }
    }
    
    // 检查当前选择的细胞系是否还在可用列表中
    if (selectedCellLines.length > 0) {
      const validCellLines = selectedCellLines.filter(cellLine => cellLines.includes(cellLine));
      if (validCellLines.length !== selectedCellLines.length) {
        setSelectedCellLines(validCellLines);
      }
    }
    
    // 检查当前选择的组织类型是否还在可用列表中
    if (selectedTissueCellTypes.length > 0) {
      const validTissueTypes = selectedTissueCellTypes.filter(tissue => tissueTypes.includes(tissue));
      if (validTissueTypes.length !== selectedTissueCellTypes.length) {
        setSelectedTissueCellTypes(validTissueTypes);
      }
    }
  }, [diseases, cellLines, tissueTypes, selectedDiseases, selectedCellLines, selectedTissueCellTypes]); // 依赖于筛选条件的变化

  // 监听分析类型变化，重新获取数据
  useEffect(() => {
    if (selectedGeneIds.size > 0 && (searchInput || selectedSearchTerm)) {
      // 当分析类型改变时，重新获取数据
      const termToUse = searchInput || selectedSearchTerm;
      handleSubmitWithTerm(termToUse);
    }
  }, [analysisType, handleSubmitWithTerm, selectedGeneIds.size, searchInput, selectedSearchTerm]);

  // 监听搜索关键词和分析类型变化，重置分页
  useEffect(() => {
    setKeggCurrentPage(1);
    setGoCurrentPage(1);
  }, [searchKeyword, analysisType]);

  // 监听过滤器变化，自动清除已选基因中不在过滤结果中的基因
  useEffect(() => {
    if (selectedGeneIds.size > 0) {
      const filteredData = getFilteredTableData();
      const validGeneIds = new Set(filteredData.map(item => item.geneId));
      
      // 检查当前选择的基因是否还在过滤结果中
      const newSelectedGeneIds = new Set<string>();
      selectedGeneIds.forEach(geneId => {
        if (validGeneIds.has(geneId)) {
          newSelectedGeneIds.add(geneId);
        }
      });
      
      // 如果有基因被过滤掉，更新选择状态
      if (newSelectedGeneIds.size !== selectedGeneIds.size) {
        setSelectedGeneIds(newSelectedGeneIds);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTissueCellTypes, selectedCellLines, selectedDiseases, keggTableData, goTableData, analysisType]);

  // 过滤表格数据 - 根据选择的筛选条件过滤数据
  const getFilteredTableData = () => {
    const currentData = analysisType === 'KEGG analysis' ? keggTableData : goTableData;
    
    return currentData.filter(item => {
      // 使用新的projectId匹配逻辑
      return checkProjectIdsMatchFilters(
        item.translationProjectIds || '',
        selectedTissueCellTypes,
        selectedCellLines,
        selectedDiseases
      );
    });
  };

  // 获取过滤后的表格数据
  const filteredTableData = getFilteredTableData();

  const renderFilterSection = (
    title: string,
    options: string[],
    selectedOptions: string[],
    type: 'tissue' | 'cellLine' | 'disease'
  ) => {
    // 确定是否显示更多选项
    let showMore = false;
    let setShowMore = () => {};
    
    if (type === 'tissue') {
      showMore = showMoreTissueTypes;
      setShowMore = () => setShowMoreTissueTypes(!showMoreTissueTypes);
    } else if (type === 'cellLine') {
      showMore = showMoreCellLines;
      setShowMore = () => setShowMoreCellLines(!showMoreCellLines);
    } else {
      showMore = showMoreDiseases;
      setShowMore = () => setShowMoreDiseases(!showMoreDiseases);
    }

    // 显示的选项数量
    const displayOptions = showMore ? options : options.slice(0, 4);

    return (
      <div className="mb-4 border-b border-gray-200 pb-4">
        <div className="flex">
          <div className="w-32 font-medium text-blue-800">{title}:</div>
          <div className="flex-1">
            <div className="grid grid-cols-4 gap-4">
              {displayOptions.map(option => (
                <label key={option} className="flex items-start group cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedOptions.includes(option)}
                    onChange={(e) => handleFilterSelect(type, option, e.target.checked)}
                    className="mr-2 h-4 w-4 mt-0.5 flex-shrink-0 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span 
                    className="text-sm text-gray-700 leading-relaxed break-words"
                    title={option}
                  >
                    {option}
                  </span>
                </label>
              ))}
            </div>
          </div>
          <div className="w-20 flex justify-end">
            {options.length > 4 && (
              <button 
                onClick={setShowMore}
                className="text-[#337ab7] hover:text-[#1a3d5c] text-sm"
              >
                {showMore ? "- less" : "+ more"}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* 强制覆盖任何可能的oklch颜色 */}
      <style jsx global>{`
        * {
          color-scheme: light !important;
        }
        .bg-blue-500 { background-color: #23527C !important; }
        .bg-blue-600 { background-color: #1a3d5c !important; }
        .bg-green-500 { background-color: #10b981 !important; }
        .text-blue-600 { color: #23527C !important; }
        .text-blue-800 { color: #1a3d5c !important; }
        .border-blue-300 { border-color: #7ba3c7 !important; }
        .border-blue-200 { border-color: #a8c4d9 !important; }
        .bg-blue-100 { background-color: #e6f2ff !important; }
        .bg-blue-50 { background-color: #f0f4f8 !important; }
        .hover\\:bg-blue-100:hover { background-color: #e6f2ff !important; }
        .hover\\:bg-blue-600:hover { background-color: #1a3d5c !important; }
        .focus\\:ring-blue-500:focus { --tw-ring-color: #23527C !important; }
      `}</style>
      <div className="min-h-screen bg-gray-50" style={{ colorScheme: 'light' }}>
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span style={{ color: '#337ab7' }}>TE/TR/EVI of Genes across KEGG pathways/GO terms</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">


          {/* Display Select 区域 - 参考gene页面的搜索控制区域 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3
              className="text-lg font-medium text-gray-900 mb-4 cursor-pointer hover:text-blue-600 transition-colors duration-200 flex items-center"
              onClick={() => setShowDisplaySelect(!showDisplaySelect)}
            >
              <span className={`mr-2 transition-transform duration-200 ${showDisplaySelect ? 'rotate-90' : ''}`}>▶</span>
              Display Select
            </h3>

            {showDisplaySelect && (
              <>
              <div className="flex items-center justify-center space-x-6 mb-6">
              {/* 分析功能选择器 */}
              <div className="relative" ref={analysisDropdownRef}>
                <div
                  className="bg-white border border-gray-300 rounded-lg shadow-sm px-4 py-2 cursor-pointer hover:border-gray-400 transition-colors duration-200 min-w-[180px]"
                  onClick={() => setShowAnalysisDropdown(!showAnalysisDropdown)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-sm font-medium text-gray-700">{analysisType}</span>
                    </div>
                    <svg
                      className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${showAnalysisDropdown ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>

                {/* 下拉选项 */}
                {showAnalysisDropdown && (
                  <div className="absolute z-20 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                    {(['KEGG analysis', 'GO analysis'] as AnalysisType[]).map((type) => (
                      <div
                        key={type}
                        className="px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center space-x-2 first:rounded-t-lg last:rounded-b-lg"
                        onClick={() => {
                          setAnalysisType(type);
                          setShowAnalysisDropdown(false);
                        }}
                      >
                        <div className={`w-4 h-4 rounded-sm flex items-center justify-center ${analysisType === type ? 'bg-green-500' : 'bg-gray-200'}`}>
                          {analysisType === type && (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <span className="text-sm text-gray-700">{type}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 搜索框 */}
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                  {analysisType === 'KEGG analysis' ? 'Pathway Description:' : 'GO Term:'}
                </label>
                <div className="relative">
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder={analysisType === 'KEGG analysis' ? 'Input Pathway_Description' : 'Input GO_Term'}
                    value={searchInput}
                    onChange={(e) => handleSearchInputChange(e.target.value)}
                    onFocus={() => {
                      if (filteredOptions.length > 0) {
                        setShowDropdown(true);
                      }
                    }}
                    className="w-64 h-10 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
                  />
                  <div 
                    className="absolute inset-y-0 right-0 flex items-center pr-2 cursor-pointer"
                    onClick={() => {
                      if (searchInputRef.current) {
                        searchInputRef.current.focus();
                        if (filteredOptions.length > 0) {
                          setShowDropdown(true);
                        }
                      }
                    }}
                  >
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>

                  {/* 下拉选项 */}
                  {showDropdown && filteredOptions.length > 0 && (
                    <div 
                      ref={dropdownRef}
                      className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
                    >
                      {filteredOptions.slice(0, 20).map((option) => (
                        <div
                          key={option}
                          className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                          onClick={() => handleSearchSelect(option)}
                        >
                          {option}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Submit和Reset按钮 - 参考gene页面 */}
              <button
                onClick={handleSubmit}
                disabled={isLoading}
                className={`btn-submit ${isLoading ? 'loading' : ''}`}
              >
                {isLoading ? 'Loading...' : 'Submit'}
              </button>
              
              <button
                onClick={() => {
                  // 重置所有状态到初始值，相当于重新加载页面
                  setSearchInput('');
                  setSelectedSearchTerm('');
                  setKeggTableData([]);
                  setGoTableData([]);
                  // 过滤选项现在通过useMemo动态计算，不需要重置状态
                  setSelectedTissueCellTypes([]);
                  setSelectedCellLines([]);
                  setSelectedDiseases([]);
                  setSelectedGeneIds(new Set());
                  setAnalysisType('KEGG analysis');
                  setTranslationData([]);
                  setShowResults(false);
                  setErrorMessage('');
                  setSearchKeyword('');
                  setSortField('geneSymbol');
                  setSortDirection('asc');
                  setShowDownloadOptions(false);

                  // 重置搜索输入框
                  if (searchInputRef.current) {
                    searchInputRef.current.value = '';
                  }
                }}
                className="btn-reset"
              >
                Reset
              </button>
            </div>

            {/* 过滤器 - 参考gene页面的布局 */}
            {(tissueTypes.length > 0 || cellLines.length > 0 || diseases.length > 0) && (
              <div className="border-t border-gray-200 pt-4">
                {/* 联动过滤提示 */}
                {tissueTypes.length > 0 &&
                  renderFilterSection('Tissue/Cell Type', tissueTypes, selectedTissueCellTypes, 'tissue')
                }
                {cellLines.length > 0 &&
                  renderFilterSection('Cell Line', cellLines, selectedCellLines, 'cellLine')
                }
                {diseases.length > 0 &&
                  renderFilterSection('Condition', diseases, selectedDiseases, 'disease')
                }
              </div>
            )}
            </>
            )}
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}

          {/* 数据表格和右侧面板的布局 */}
          {(keggTableData.length > 0 || goTableData.length > 0) && (
            <div className="grid grid-cols-4 gap-6">
              {/* 左侧表格区域 - 占3列 */}
              <div className="col-span-3">
                {/* 顶部搜索和下载区域 */}
                <div className="flex justify-between items-center mb-6">
                  <div className="flex items-center">
                    <h1 className="text-xl font-semibold text-gray-800">
                      {(() => {
                        const currentData = analysisType === 'KEGG analysis' ?
                          (filteredTableData.length > 0 ? filteredTableData : keggTableData) :
                          (filteredTableData.length > 0 ? filteredTableData : goTableData);
                        const searchedData = filterTableData(currentData, searchKeyword);
                        const analysisName = analysisType === 'KEGG analysis' ? 'KEGG Pathway' : 'GO Annotation';
                        return `${searchedData.length} ${analysisName} Gene Records Found`;
                      })()}
                    </h1>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex">
                      <Input
                        placeholder="Search"
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        className="rounded-r-none h-10 w-80 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
                      />
                    </div>

                    <div className="relative" ref={downloadOptionsRef}>
                    <button
                      onClick={() => {
                        const newState = !showDownloadOptions;
                        setShowDownloadOptions(newState);
                      }}
                      className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </button>

                      {showDownloadOptions && (
                        <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                             style={{ display: showDownloadOptions ? 'block' : 'none' }}>
                          <div className="py-1">
                            <button
                              onClick={() => {
                                downloadData('csv');
                              }}
                              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                            >
                              Download as CSV
                            </button>
                            <button
                              onClick={() => {
                                downloadData('json');
                              }}
                              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                            >
                              Download as JSON
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {/* KEGG 表格 */}
                {analysisType === 'KEGG analysis' && keggTableData.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Compare
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('geneSymbol')}
                            >
                              <div className="flex items-center justify-center">
                                <span>GENE symbol</span>
                                {renderSortIcon('geneSymbol')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('geneId')}
                            >
                              <div className="flex items-center justify-center">
                                <span>GENE ID</span>
                                {renderSortIcon('geneId')}
                              </div>
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Pathway_Description
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Pathway ID
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {(() => {
                            const { data } = getKeggPaginatedData();
                            return data.map((item, index) => (
                            <tr key={item.id} className={index === 0 ? 'bg-blue-50' : ''}>
                              <td className="px-4 py-3 whitespace-nowrap text-center">
                                <input
                                  type="checkbox"
                                  checked={selectedGeneIds.has(item.geneId)}
                                  onChange={(e) => handleGeneSelect(item.geneId, e.target.checked)}
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                                {item.geneSymbol && item.geneSymbol !== 'nan' && item.geneSymbol !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/gene/?term=${item.geneSymbol}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="link-research"
                                  >
                                    {item.geneSymbol}
                                  </a>
                                ) : (
                                  <span className="text-gray-900">NA</span>
                                )}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                                {item.geneId && item.geneId !== 'nan' && item.geneId !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/gene/?term=${item.geneId}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="link-research"
                                  >
                                    {item.geneId}
                                  </a>
                                ) : (
                                  <span className="text-gray-900">NA</span>
                                )}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                                {(item as KEGGAnnotationData).pathwayDescription && (item as KEGGAnnotationData).pathwayDescription !== 'nan' && (item as KEGGAnnotationData).pathwayDescription !== 'null' ? (item as KEGGAnnotationData).pathwayDescription : 'NA'}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                                {(item as KEGGAnnotationData).pathwayId && (item as KEGGAnnotationData).pathwayId !== 'nan' && (item as KEGGAnnotationData).pathwayId !== 'null' ? (item as KEGGAnnotationData).pathwayId : 'NA'}
                              </td>
                            </tr>
                          ));
                          })()}
                        </tbody>
                      </table>
                    </div>

                    {/* 分页和控制 */}
                    <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-6">
                          <span className="text-sm font-medium text-gray-700">
                            Showing {(keggCurrentPage - 1) * keggPageSize + 1} to {Math.min(keggCurrentPage * keggPageSize, getKeggPaginatedData().total)} of {getKeggPaginatedData().total} rows
                          </span>
                          <div className="flex items-center space-x-2">
                            <label className="text-sm font-medium text-gray-700">
                              <select
                                value={keggPageSize}
                                onChange={(e) => {
                                  setKeggPageSize(Number(e.target.value));
                                  setKeggCurrentPage(1);
                                }}
                                className="border border-gray-300 rounded px-2 py-1 text-sm ml-2 mr-2"
                              >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                              </select>
                              rows per page
                            </label>
                          </div>
                        </div>

                        {/* 分页按钮 */}
                        {renderKeggPagination()}
                      </div>
                    </div>
                  </div>
                )}

                {/* GO 表格 */}
                {analysisType === 'GO analysis' && goTableData.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Compare
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('geneSymbol')}
                            >
                              <div className="flex items-center justify-center">
                                <span>GENE symbol</span>
                                {renderSortIcon('geneSymbol')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('geneId')}
                            >
                              <div className="flex items-center justify-center">
                                <span>GENE ID</span>
                                {renderSortIcon('geneId')}
                              </div>
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              GO Term 
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              GO_Domain
                            </th>
                            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              GO_ID
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {(() => {
                            const { data } = getGoPaginatedData();
                            return data.map((item, index) => (
                            <tr key={item.id} className={index === 0 ? 'bg-[#f0f4f8]' : ''}>
                              <td className="px-4 py-3 whitespace-nowrap text-center">
                                <input
                                  type="checkbox"
                                  checked={selectedGeneIds.has(item.geneId)}
                                  onChange={(e) => handleGeneSelect(item.geneId, e.target.checked)}
                                  className="rounded border-gray-300 text-[#337ab7] focus:ring-[#337ab7]"
                                />
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                                {item.geneSymbol && item.geneSymbol !== 'nan' && item.geneSymbol !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/gene/?term=${item.geneSymbol}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="link-research"
                                  >
                                    {item.geneSymbol}
                                  </a>
                                ) : (
                                  <span className="text-gray-900">NA</span>
                                )}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                                {item.geneId && item.geneId !== 'nan' && item.geneId !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/gene/?term=${item.geneId}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="link-research"
                                  >
                                    {item.geneId}
                                  </a>
                                ) : (
                                  <span className="text-gray-900">NA</span>
                                )}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                                {(item as GOAnnotationData).goTerm && (item as GOAnnotationData).goTerm !== 'nan' && (item as GOAnnotationData).goTerm !== 'null' ? (item as GOAnnotationData).goTerm : 'NA'}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                                {(item as GOAnnotationData).goDomain && (item as GOAnnotationData).goDomain !== 'nan' && (item as GOAnnotationData).goDomain !== 'null' ? (item as GOAnnotationData).goDomain : 'NA'}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                                {(item as GOAnnotationData).goId && (item as GOAnnotationData).goId !== 'nan' && (item as GOAnnotationData).goId !== 'null' ? (item as GOAnnotationData).goId : 'NA'}
                              </td>
                            </tr>
                          ));
                          })()}
                        </tbody>
                      </table>
                    </div>

                    {/* 分页和控制 */}
                    <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-6">
                          <span className="text-sm font-medium text-gray-700">
                            Showing {(goCurrentPage - 1) * goPageSize + 1} to {Math.min(goCurrentPage * goPageSize, getGoPaginatedData().total)} of {getGoPaginatedData().total} rows
                          </span>
                          <div className="flex items-center space-x-2">
                            <label className="text-sm font-medium text-gray-700">
                              <select
                                value={goPageSize}
                                onChange={(e) => {
                                  setGoPageSize(Number(e.target.value));
                                  setGoCurrentPage(1);
                                }}
                                className="border border-gray-300 rounded px-2 py-1 text-sm ml-2 mr-2"
                              >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                              </select>
                              rows per page
                            </label>
                          </div>
                        </div>

                        {/* 分页按钮 */}
                        {renderGoPagination()}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 右侧GENE ID面板 - 占1列 */}
              <div className="col-span-1">
                <div className="bg-blue-100 rounded-lg p-4 border-2 border-blue-300 sticky top-6">
                  <h4 className="font-medium text-blue-800 mb-3">GENE ID ({selectedGeneIds.size})</h4>
                  <div className="bg-white rounded p-3 mb-3 min-h-[200px] max-h-[300px] border border-blue-200 overflow-y-auto">
                    {selectedGeneIds.size > 0 ? (
                      <div className="space-y-2">
                        {Array.from(selectedGeneIds).map((geneId) => (
                          <div key={geneId} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                            <span className="text-sm text-gray-700">{geneId}</span>
                            <button 
                              onClick={() => handleGeneSelect(geneId, false)}
                              className="text-gray-400 hover:text-gray-600 ml-2"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-gray-500 text-sm text-center py-8">
                        No genes selected
                      </div>
                    )}
                  </div>
                  {/* 错误提示区域 */}
                  {errorMessage && (
                    <div className="mb-3">
                      <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="ml-2 flex-1">
                            <p className="text-xs text-red-800">{errorMessage}</p>
                          </div>
                          <div className="ml-auto">
                            <button
                              type="button"
                              onClick={() => setErrorMessage('')}
                              className="inline-flex rounded-md bg-red-50 p-1 text-red-500 hover:bg-red-100"
                            >
                              <span className="sr-only">Close</span>
                              <svg className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    <button 
                      onClick={handleCalculate}
                      className="bg-[#0071BC] hover:bg-[#2B7FFF] text-white px-4 py-2 rounded font-medium flex-1"
                    >
                      Compare
                    </button>
                    <button 
                      onClick={handleClearGenes}
                      className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded font-medium flex-1"
                    >
                      Clear
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

                    {/* 结果展示区域 */}
          {showResults && (
            <div className="mt-6">
              {/* 蓝色背景标题条 */}
              <div className="bg-[#267DD4] text-white text-center py-3 rounded-t-lg">
                <h2 className="text-xl font-semibold">Compare Result</h2>
              </div>
              
              {/* 美化的切换选项区域 */}
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 border-l border-r border-gray-200 px-6 py-6">
                <div className="flex justify-center">
                  <div className="bg-white rounded-lg p-1 shadow-sm border border-gray-300">
                    <div className="flex space-x-1">
                      <button
                        onClick={() => setResultType('Transcript')}
                        className={`px-6 py-3 rounded-md font-medium text-sm transition-all duration-200 ease-in-out ${
                          resultType === 'Transcript'
                            ? 'bg-[#267DD4] text-white shadow-md transform scale-105'
                            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                        }`}
                      >
                        <span className="flex items-center space-x-2">
                          <span className="w-2 h-2 rounded-full bg-current opacity-60"></span>
                          <span>Transcript Level</span>
                        </span>
                      </button>
                      <button
                        onClick={() => setResultType('Gene')}
                        className={`px-6 py-3 rounded-md font-medium text-sm transition-all duration-200 ease-in-out ${
                          resultType === 'Gene'
                            ? 'bg-[#267DD4] text-white shadow-md transform scale-105'
                            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                        }`}
                      >
                        <span className="flex items-center space-x-2">
                          <span className="w-2 h-2 rounded-full bg-current opacity-60"></span>
                          <span>Gene Level</span>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Data content or message */}
              {((resultType === 'Transcript' && translationData.length > 0) || (resultType === 'Gene' && geneData.length > 0)) ? (
                <>
                  {resultType === 'Transcript' ? (
                    <TranscriptLevelDisplay
                      translationData={translationData}
                      selectedGeneIds={selectedGeneIds}
                      selectedTissueCellTypes={selectedTissueCellTypes}
                      selectedCellLines={selectedCellLines}
                      selectedDiseases={selectedDiseases}
                    />
                  ) : (
                    <GeneLevelDisplay
                      geneData={geneData}
                      selectedGeneIds={selectedGeneIds}
                      selectedTissueCellTypes={selectedTissueCellTypes}
                      selectedCellLines={selectedCellLines}
                      selectedDiseases={selectedDiseases}
                    />
                  )}
                </>
              ) : (
                <div className="bg-white border border-gray-200 rounded-b-lg p-8">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No {resultType} Data Available
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Please click &ldquo;Compare&rdquo; button above to generate {resultType.toLowerCase()} analysis results.
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
    </>
  );
}