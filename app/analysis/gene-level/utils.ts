import { UTRComponents } from './types';

// 解析UTR组件
export const parseUTRComponents = (componentsString: string): UTRComponents => {
  try {
    return JSON.parse(componentsString);
  } catch {
    return {
      IRES: 'no',
      miRNAs: 'no',
      'PolyA Sites': 'no',
      Repeats: 'no',
      'Rfam motifs': 'no',
      uORFs: 'no'
    };
  }
};

// 自定义颜色插值器 - 使用渐变友好的颜色序列
export const customColorInterpolator = (t: number) => {
  // 重新设计的颜色配置，避免蓝橙直接过渡产生灰色
  // 使用单一色调渐变：深蓝 → 浅蓝 → 白色 → 浅橙 → 深红
  const colors = ["#08519C", "#3182BD", "#9ECAE1", "#FDAE6B", "#E6550D", "#A63603"];
  const scaledT = Math.max(0, Math.min(1, t));
  const index = scaledT * (colors.length - 1);
  const lowerIndex = Math.floor(index);
  const upperIndex = Math.ceil(index);
  const ratio = index - lowerIndex;

  if (lowerIndex === upperIndex) {
    return colors[lowerIndex];
  }

  // 解析颜色
  const parseColor = (hex: string) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return { r, g, b };
  };

  const color1 = parseColor(colors[lowerIndex]);
  const color2 = parseColor(colors[upperIndex]);

  // 线性插值
  const r = Math.round(color1.r + (color2.r - color1.r) * ratio);
  const g = Math.round(color1.g + (color2.g - color1.g) * ratio);
  const b = Math.round(color1.b + (color2.b - color1.b) * ratio);

  return `rgb(${r}, ${g}, ${b})`;
};

// 获取UTR标签的样式
export const getUTRTagStyle = (key: string) => {
  let bgColor = '#6b7280';
  let textColor = '#f9fafb';
  let displayName = key;

  switch (key) {
    case 'IRES':
      bgColor = '#fef3c7';
      textColor = '#d97706';
      break;
    case 'miRNAs':
      bgColor = '#dbeafe';
      textColor = '#1e40af';
      break;
    case 'PolyA Sites':
      bgColor = '#f3f4f6';
      textColor = '#374151';
      displayName = 'PolyA';
      break;
    case 'Repeats':
      bgColor = '#fed7aa';
      textColor = '#c2410c';
      break;
    case 'Rfam motifs':
      bgColor = '#dcfce7';
      textColor = '#166534';
      displayName = 'Rfam';
      break;
    case 'uORFs':
      bgColor = '#e9d5ff';
      textColor = '#7c3aed';
      break;
  }

  return { bgColor, textColor, displayName };
};

// 温莎化函数 - 将极值替换为1%和99%分位数
export const winsorizeData = (values: number[]): number[] => {
  if (values.length === 0) return values;

  // 过滤掉0值，只对正值进行温莎化
  const positiveValues = values.filter(v => v > 0);
  if (positiveValues.length === 0) return values;

  // 排序以计算分位数
  const sorted = [...positiveValues].sort((a, b) => a - b);

  // 计算1%和99%分位数的索引
  const p1Index = Math.floor(sorted.length * 0.01);
  const p99Index = Math.floor(sorted.length * 0.99);

  const p1Value = sorted[p1Index];
  const p99Value = sorted[p99Index];

  // 应用温莎化：将极值替换为分位数值
  return values.map(value => {
    if (value <= 0) return value; // 保持0值不变
    if (value < p1Value) return p1Value;
    if (value > p99Value) return p99Value;
    return value;
  });
};
