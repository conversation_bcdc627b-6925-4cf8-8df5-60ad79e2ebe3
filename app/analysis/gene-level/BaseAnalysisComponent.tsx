import React, { useMemo } from 'react';
import { Download } from 'lucide-react';
import * as d3 from 'd3';
import { TranslationData, MetricType, MetricCardData, AnalysisType } from './types';
import { parseUTRComponents, customColorInterpolator, getUTRTagStyle } from './utils';
import { downloadHeatmapImage as downloadHeatmap } from './downloadUtils';

interface BaseAnalysisComponentProps {
  translationData: TranslationData[];
  selectedOptions: {[key in MetricType]: Set<string>};
  setSelectedOptions: React.Dispatch<React.SetStateAction<{[key in MetricType]: Set<string>}>>;
  selectedGeneSymbol: string;
  selectedGeneId: string;
  customGeneSymbol: string;
  customGeneId: string;
  analysisType: AnalysisType;
  getConditionValue: (item: TranslationData) => string;
}

export default function BaseAnalysisComponent({
  translationData,
  selectedOptions,
  setSelectedOptions,
  selectedGeneSymbol,
  selectedGeneId,
  customGeneSymbol,
  customGeneId,
  analysisType,
  getConditionValue
}: BaseAnalysisComponentProps) {



  // 使用useMemo优化性能，生成每个指标的数据
  const metricCards = useMemo(() => {
    if (translationData.length === 0) return [];

    const cards: MetricCardData[] = [];
    const metrics: MetricType[] = ['TE', 'TR', 'EVI'];

    metrics.forEach(metric => {
      // 首先找出该指标有数据的所有条件
      const metricAvailableConditions = (() => {
        const conditionsWithData = new Set<string>();

        translationData.forEach(item => {
          const value = metric === 'TE' ? item.te : metric === 'TR' ? item.tr : item.evi;
          if (value !== null && value !== undefined && !isNaN(Number(value))) {
            const conditionValue = getConditionValue(item);
            if (conditionValue) {
              conditionsWithData.add(conditionValue);
            }
          }
        });

        return Array.from(conditionsWithData).sort();
      })();

      // 如果该指标没有任何可用数据，跳过这个指标
      if (metricAvailableConditions.length === 0) {
        return;
      }

      const metricSelectedOptions = selectedOptions[metric];

      // 如果该指标没有选中任何条件，生成空卡片以显示过滤器
      if (metricSelectedOptions.size === 0) {
        cards.push({
          metric,
          transcriptIds: [],
          conditions: [],
          heatmapData: {},
          min: 0,
          max: 1,
          availableConditions: metricAvailableConditions
        });
        return;
      }

      // 过滤数据 - 使用该指标的选项，并且只包含该指标有数据的条件
      const filteredData = translationData.filter(item => {
        const conditionValue = getConditionValue(item);

        // 检查该条件是否被选中，并且该指标在该条件下有数据
        if (!metricSelectedOptions.has(conditionValue)) return false;

        const value = metric === 'TE' ? item.te : metric === 'TR' ? item.tr : item.evi;
        return value !== null && value !== undefined && !isNaN(Number(value));
      });

      if (filteredData.length === 0) {
        // 即使没有数据，也创建一个空卡片以显示过滤器
        cards.push({
          metric,
          transcriptIds: [],
          conditions: [],
          heatmapData: {},
          min: 0,
          max: 1,
          availableConditions: metricAvailableConditions
        });
        return;
      }

      const heatmap: {[key: string]: {[key: string]: number}} = {};
      const transcripts = new Set<string>();
      const conditionsSet = new Set<string>();

      // 构建热力图数据
      filteredData.forEach(item => {
        const conditionValue = getConditionValue(item);
        const condition = `${conditionValue}_${item.projectId}`;
        const transcript = item.transcriptId;
        
        let rawValue: number | null | undefined;
        switch (metric) {
          case 'TE':
            rawValue = item.te;
            break;
          case 'TR':
            rawValue = item.tr;
            break;
          case 'EVI':
            rawValue = item.evi;
            break;
        }

        const numericValue = Number(rawValue);
        if (!isNaN(numericValue)) {
          if (!heatmap[transcript]) {
            heatmap[transcript] = {};
          }
          heatmap[transcript][condition] = numericValue;
          transcripts.add(transcript);
          conditionsSet.add(condition);
        }
      });

      // 按列计算z-score标准化用于颜色映射
      const columnZScores: {[key: string]: {[key: string]: number}} = {};
      const allZScores: number[] = [];

      // 为每个条件（列）计算z-score
      Array.from(conditionsSet).forEach(condition => {
        const columnValues: number[] = [];

        // 收集该列的所有有效值
        transcripts.forEach(transcript => {
          const value = heatmap[transcript]?.[condition];
          if (value !== undefined && !isNaN(Number(value))) {
            columnValues.push(Number(value));
          }
        });

        if (columnValues.length > 1) {
          // 计算该列的均值和标准差
          const mean = columnValues.reduce((sum, val) => sum + val, 0) / columnValues.length;
          const variance = columnValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (columnValues.length - 1);
          const stdDev = Math.sqrt(variance);

          // 为该列的每个值计算z-score
          transcripts.forEach(transcript => {
            const value = heatmap[transcript]?.[condition];
            if (value !== undefined && !isNaN(Number(value)) && stdDev > 0) {
              if (!columnZScores[transcript]) {
                columnZScores[transcript] = {};
              }
              const zscore = (Number(value) - mean) / stdDev;
              columnZScores[transcript][condition] = zscore;
              allZScores.push(zscore);
            }
          });
        }
      });

      // 计算所有z-score的范围用于颜色映射
      const min = allZScores.length > 0 ? Math.min(...allZScores) : -2;
      const max = allZScores.length > 0 ? Math.max(...allZScores) : 2;

      // 用于颜色条标签的z-score范围
      const zScoreRange = { min, max };

      cards.push({
        metric,
        transcriptIds: Array.from(transcripts).sort(),
        conditions: Array.from(conditionsSet).sort(),
        heatmapData: heatmap, // 使用原始数据显示数值
        columnZScores, // 添加按列计算的z-score用于颜色映射
        min, // z-score范围用于颜色映射
        max,
        zScoreMin: zScoreRange.min,
        zScoreMax: zScoreRange.max,
        availableConditions: metricAvailableConditions
      });
    });

    return cards;
  }, [translationData, selectedOptions, getConditionValue]);

  // 处理单个选项选择
  const handleOptionSelect = (metric: MetricType, option: string, checked: boolean) => {
    setSelectedOptions(prev => {
      const newSelected = { ...prev };
      newSelected[metric] = new Set(prev[metric]);
      if (checked) {
        newSelected[metric].add(option);
      } else {
        newSelected[metric].delete(option);
      }
      return newSelected;
    });
  };

  // 渲染指标卡片
  const renderMetricCard = (cardData: MetricCardData) => {
    const { metric, transcriptIds, conditions, heatmapData, columnZScores, min, max, zScoreMin, zScoreMax, availableConditions } = cardData;
    
    return (
      <div key={metric} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 relative" id={`heatmap-container-${metric.toLowerCase()}`}>
        <div className="flex h-[600px]">
          {/* 标题和下载按钮 */}
          <div className="absolute top-4 right-4 z-10">
            <button
              onClick={() => downloadHeatmap({
                metric,
                transcriptIds,
                conditions,
                heatmapData,
                columnZScores,
                min,
                max,
                translationData,
                selectedGeneSymbol,
                selectedGeneId,
                customGeneSymbol,
                customGeneId,
                analysisType
              })}
              className="bg-white hover:bg-gray-100 border border-gray-300 text-gray-700 p-2 rounded-md transition-colors shadow-sm"
              title="Download heatmap as image"
            >
              <Download className="h-4 w-4" />
            </button>
          </div>
          {/* 左侧过滤器 - 自适应宽度，最小宽度确保内容显示 */}
          <div className="min-w-[280px] max-w-[400px] flex-shrink-0 pr-4 border-r border-gray-200 flex flex-col">
            {/* 全选选项 - 固定高度 */}
            <div className="flex-shrink-0 pb-3 border-b border-gray-100">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={availableConditions.every(option => selectedOptions[metric].has(option))}
                  onChange={(e) => {
                    setSelectedOptions(prev => {
                      const newSelected = { ...prev };
                      if (e.target.checked) {
                        newSelected[metric] = new Set([...prev[metric], ...availableConditions]);
                      } else {
                        newSelected[metric] = new Set();
                      }
                      return newSelected;
                    });
                  }}
                  className="rounded border-gray-300 text-[#337ab7] focus:ring-[#337ab7]"
                />
                <span className="ml-2 text-sm font-medium text-gray-900">ALL</span>
              </label>
            </div>

            {/* 条件选项 - 占满剩余高度 */}
            <div className="flex-1 pt-3 overflow-y-auto">
              <div className="space-y-3">
                {availableConditions.map(option => (
                  <label key={option} className="flex items-start ml-4 text-sm">
                    <input
                      type="checkbox"
                      checked={selectedOptions[metric].has(option)}
                      onChange={(e) => handleOptionSelect(metric, option, e.target.checked)}
                      className="rounded border-gray-300 text-[#337ab7] focus:ring-[#337ab7] mt-0.5 flex-shrink-0"
                    />
                    <span className="ml-2 text-gray-700 leading-snug break-words">{option}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* 热力图区域 - 占满剩余空间 */}
          <div className="flex-1 pl-4 min-w-0 flex flex-col">
            <div className="flex-shrink-0 mb-4">
              <h3 className="font-bold text-gray-900 text-lg text-center">
                {metric} of {selectedGeneId || selectedGeneSymbol || customGeneId || customGeneSymbol} transcripts in different {(() => {
                  switch (analysisType) {
                    case 'Tissue/Cell Type':
                      return 'tissues/cell types';
                    case 'Cell Line':
                      return 'cell lines';
                    case 'Condition':
                      return 'conditions';
                    default:
                      return 'conditions';
                  }
                })()}
              </h3>
            </div>
            
            <div className="flex-1 border border-gray-300 rounded-lg bg-white">
              <div className="h-full p-3">
                <div
                  className="heatmap-scroll-container overflow-auto border border-gray-200 rounded relative h-full"
                  style={{
                    width: '100%',
                    maxHeight: '500px',
                    overflowX: 'auto',
                    overflowY: 'auto',
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#CBD5E0 #F7FAFC'
                  }}
                >
                  <div
                    ref={(node) => {
                      if (node) {
                        d3.select(node).selectAll("*").remove();

                        if (transcriptIds.length > 0 && conditions.length > 0) {
                          
                          const cellSize = 40;
                          // 重新设计布局：UTR(500) + transcriptID(200) + 热图 + 颜色条(150)
                          const utrAreaWidth = 500; // UTR标签区域宽度
                          const transcriptLabelWidth = 200; // transcript ID标签宽度
                          const colorBarAreaWidth = 150; // 颜色条区域宽度，增加以容纳标签
                          const margin = {
                            top: 30,
                            right: colorBarAreaWidth,
                            bottom: 150,
                            left: utrAreaWidth + transcriptLabelWidth
                          };
                          
                          const heatmapCoreWidth = conditions.length * cellSize;
                          const totalRequiredWidth = heatmapCoreWidth + margin.left + margin.right;
                          // 增加额外宽度以确保所有内容都能显示
                          const width = totalRequiredWidth + 100;
                          const height = Math.max(transcriptIds.length * cellSize + margin.top + margin.bottom, 400);
                          
                          const svg = d3.select(node)
                            .append("svg")
                            .attr("width", width)
                            .attr("height", height);
                          
                          const g = svg.append("g")
                            .attr("transform", `translate(${margin.left},${margin.top})`);
                          
                          const colorScale = d3.scaleSequential()
                            .interpolator(customColorInterpolator)
                            .domain([min, max]);
                          
                          // 计算每列有值的单元格数量
                          const columnValueCounts: {[condition: string]: number} = {};
                          conditions.forEach(condition => {
                            let count = 0;
                            transcriptIds.forEach(transcriptId => {
                              const value = heatmapData[transcriptId]?.[condition];
                              if (value !== undefined && !isNaN(Number(value))) {
                                count++;
                              }
                            });
                            columnValueCounts[condition] = count;
                          });

                          // 预收集需要显示的原始数值，最后统一绘制在最上层
                          const cellTexts: Array<{ i: number; j: number; value: number }> = [];

                          // 绘制热力图单元格
                          transcriptIds.forEach((transcriptId, i) => {
                            conditions.forEach((condition, j) => {
                              const value = heatmapData[transcriptId]?.[condition];
                              // 获取该单元格的z-score用于颜色映射
                              const zscore = columnZScores?.[transcriptId]?.[condition];
                              const hasNumericValue = value !== undefined && !isNaN(Number(value));
                              
                              // 判断该列是否只有一个有值的单元格，如果是且当前单元格有值，则使用红色背景
                              const isOnlyValueInColumn = columnValueCounts[condition] === 1 && hasNumericValue;
                              
                              let fillColor = "#f5f5f5"; // 默认灰色
                              if (hasNumericValue) {
                                if (isOnlyValueInColumn) {
                                  fillColor = "#A93703"; // 红色背景
                                } else if (zscore !== undefined) {
                                  fillColor = colorScale(zscore); // 正常色阶
                                }
                              }

                              g.append("rect")
                                .attr("x", j * cellSize)
                                .attr("y", i * cellSize)
                                .attr("width", cellSize - 1)
                                .attr("height", cellSize - 1)
                                .attr("fill", fillColor)
                                .attr("stroke", "white")
                                .attr("stroke-width", 1)
                                .append("title")
                                .text(`${transcriptId} - ${condition}: ${hasNumericValue ? Number(value).toFixed(3) : 'N/A'}${zscore !== undefined ? ` (Z-score: ${zscore.toFixed(3)})` : ''}${isOnlyValueInColumn ? ' (Only value in column)' : ''}`);

                              if (hasNumericValue) {
                                cellTexts.push({ i, j, value: Number(value) });
                              }
                            });
                          });
                          
                          // Y轴标签（转录本ID）到左侧
                          g.selectAll(".y-label")
                            .data(transcriptIds)
                            .enter()
                            .append("text")
                            .attr("class", "y-label")
                            .attr("x", -15) // 放到热图左边
                            .attr("y", (_, i) => i * cellSize + cellSize / 2)
                            .attr("text-anchor", "end") // 右对齐
                            .attr("dominant-baseline", "central")
                            .attr("font-size", "11px")
                            .attr("font-family", "Arial, sans-serif")
                            .attr("fill", "#333")
                            .text(d => d);

                          // X轴标签
                          g.selectAll(".x-label")
                            .data(conditions)
                            .enter()
                            .append("text")
                            .attr("class", "x-label")
                            .attr("x", (_, i) => i * cellSize + cellSize / 2)
                            .attr("y", transcriptIds.length * cellSize + 30)
                            .attr("text-anchor", "start")
                            .attr("dominant-baseline", "hanging")
                            .attr("font-size", "11px")
                            .attr("font-family", "Arial, sans-serif")
                            .attr("fill", "#333")
                            .attr("transform", (_, i) => `rotate(30, ${i * cellSize + cellSize / 2}, ${transcriptIds.length * cellSize + 30})`)
                            .text(d => d);
                          
                          // 右侧颜色条 - 放到热图右边
                          const colorBarGroup = svg.append("g")
                            .attr("transform", `translate(${margin.left + conditions.length * cellSize + 20}, ${margin.top})`);
                          
                          const gradientId = `colorbar-gradient-${metric}`;
                          const defs = svg.append("defs");
                          const gradient = defs.append("linearGradient")
                            .attr("id", gradientId)
                            .attr("x1", "0%")
                            .attr("x2", "0%")
                            .attr("y1", "0%")
                            .attr("y2", "100%");
                          
                          const steps = 10;
                          for (let i = 0; i <= steps; i++) {
                            const ratio = i / steps;
                            const value = min + (max - min) * (1 - ratio);
                            gradient.append("stop")
                              .attr("offset", `${ratio * 100}%`)
                              .attr("stop-color", colorScale(value));
                          }
                          
                          const colorBarHeight = transcriptIds.length * cellSize;
                          colorBarGroup.append("rect")
                            .attr("x", 0)
                            .attr("y", 0)
                            .attr("width", 25)
                            .attr("height", colorBarHeight)
                            .attr("fill", `url(#${gradientId})`)
                            .attr("stroke", "#666")
                            .attr("stroke-width", 1);
                          
                          colorBarGroup.append("text")
                            .attr("x", 30)
                            .attr("y", -8)
                            .attr("font-size", "12px")
                            .attr("font-family", "Arial, sans-serif")
                            .attr("fill", "#333")
                            .attr("font-weight", "bold")
                            .text(`${zScoreMax?.toFixed(2) || '0.00'} (z-score)`);

                          colorBarGroup.append("text")
                            .attr("x", 30)
                            .attr("y", colorBarHeight + 15)
                            .attr("font-size", "12px")
                            .attr("font-family", "Arial, sans-serif")
                            .attr("fill", "#333")
                            .attr("font-weight", "bold")
                            .text(`${zScoreMin?.toFixed(2) || '0.00'} (z-score)`);

                          // 添加指向热力图每一行的刻度线
                          transcriptIds.forEach((_, i) => {
                            const y = i * cellSize + cellSize / 2;
                            colorBarGroup.append("line")
                              .attr("x1", -20) // 从颜色条左边开始
                              .attr("y1", y)
                              .attr("x2", -10) // 指向热力图
                              .attr("y2", y)
                              .attr("stroke", "#333")
                              .attr("stroke-width", 1);
                          });

                          // 添加连接所有刻度线的竖线
                          if (transcriptIds.length > 1) {
                            colorBarGroup.append("line")
                              .attr("x1", -10) // 所有横线的最右边
                              .attr("y1", cellSize / 2) // 第一行的中心
                              .attr("x2", -10) // 同一个x位置
                              .attr("y2", (transcriptIds.length - 1) * cellSize + cellSize / 2) // 最后一行的中心
                              .attr("stroke", "#333")
                              .attr("stroke-width", 1);
                          }

                          // 左侧UTR组件 - 放到最左边
                          const utrGroup = svg.append("g")
                            .attr("transform", `translate(20, ${margin.top})`);
                          
                          utrGroup.append("text")
                            .attr("x", 40)
                            .attr("y", -10)
                            .attr("font-size", "13px")
                            .attr("font-family", "Arial, sans-serif")
                            .attr("fill", "#333")
                            .attr("font-weight", "bold")
                            .text("5' UTR");
                          
                          utrGroup.append("text")
                            .attr("x", 280)
                            .attr("y", -10)
                            .attr("font-size", "13px")
                            .attr("font-family", "Arial, sans-serif")
                            .attr("fill", "#333")
                            .attr("font-weight", "bold")
                            .text("3' UTR");

                          transcriptIds.forEach((transcriptId, i) => {
                            const transcript = translationData.find(item => item.transcriptId === transcriptId);
                            if (!transcript) return;

                            const y = i * cellSize + cellSize / 2;
                            
                            const fiveUTRComponents = parseUTRComponents(transcript.fiveUtrComp);
                            const threeUTRComponents = parseUTRComponents(transcript.threeUtrComp);
                            
                            // 5' UTR 标签 - 使用browse页面的颜色，从x=40开始避免遮挡横坐标
                            let xOffset = 40;
                            Object.entries(fiveUTRComponents).forEach(([key, value]) => {
                              if (value === 'yes') {
                                const { bgColor, textColor, displayName } = getUTRTagStyle(key);

                                // 更精确的标签宽度计算
                                const tagWidth = Math.max(displayName.length * 7 + 12, 40);
                                
                                utrGroup.append("rect")
                                  .attr("x", xOffset)
                                  .attr("y", y - 8)
                                  .attr("width", tagWidth)
                                  .attr("height", 16)
                                  .attr("fill", bgColor)
                                  .attr("rx", 3);
                                
                                utrGroup.append("text")
                                  .attr("x", xOffset + 6)
                                  .attr("y", y + 3)
                                  .attr("font-size", "10px")
                                  .attr("font-family", "Arial, sans-serif")
                                  .attr("fill", textColor)
                                  .attr("font-weight", "500")
                                  .text(displayName);
                                
                                xOffset += tagWidth + 6;
                              }
                            });

                            // 3' UTR 标签 - 固定位置确保不与5' UTR重叠
                            let threeUtrOffset = 280;
                            Object.entries(threeUTRComponents).forEach(([key, value]) => {
                              if (value === 'yes') {
                                const { bgColor, textColor, displayName } = getUTRTagStyle(key);
                                
                                const tagWidth = Math.max(displayName.length * 7 + 12, 40);
                                
                                utrGroup.append("rect")
                                  .attr("x", threeUtrOffset)
                                  .attr("y", y - 8)
                                  .attr("width", tagWidth)
                                  .attr("height", 16)
                                  .attr("fill", bgColor)
                                  .attr("rx", 3);
                                
                                utrGroup.append("text")
                                  .attr("x", threeUtrOffset + 6)
                                  .attr("y", y + 3)
                                  .attr("font-size", "10px")
                                  .attr("font-family", "Arial, sans-serif")
                                  .attr("fill", textColor)
                                  .attr("font-weight", "500")
                                  .text(displayName);
                                
                                threeUtrOffset += tagWidth + 6;
                              }
                            });
                          });

                          // 最后统一绘制单元格内原始数值，放在最顶层，避免被覆盖
                          if (cellTexts.length > 0) {
                            const textTopGroup = svg.append('g')
                              .attr('transform', `translate(${margin.left},${margin.top})`)
                              .attr('pointer-events', 'none');
                            cellTexts.forEach(({ i, j, value }) => {
                              const x = j * cellSize + cellSize / 2;
                              const y = i * cellSize + cellSize / 2;
                              const text = value.toFixed(2);
                              // 与 ConditionAnalysis 保持一致：纯黑色文字，不加描边
                              textTopGroup.append('text')
                                .attr('x', x)
                                .attr('y', y)
                                .attr('text-anchor', 'middle')
                                .attr('dominant-baseline', 'central')
                                .attr('font-size', '10px')
                                .attr('font-weight', 'bold')
                                .attr('fill', '#000')
                                .text(text);
                            });
                          }
                        } else {
                          // 当没有数据时，显示提示信息
                          const width = 800; // 固定宽度
                          const height = 400;

                          const svg = d3.select(node)
                            .append("svg")
                            .attr("width", width)
                            .attr("height", height);

                          // 显示提示信息
                          svg.append("text")
                            .attr("x", width / 2)
                            .attr("y", height / 2)
                            .attr("text-anchor", "middle")
                            .attr("dominant-baseline", "central")
                            .attr("font-size", "16px")
                            .attr("font-family", "Arial, sans-serif")
                            .attr("fill", "#6b7280")
                            .text(selectedOptions[metric].size === 0
                              ? "Please select at least one condition from the left panel to view the heatmap"
                              : "No data available for the current selection");
                        }
                      }
                    }}
                    className="heatmap-container"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {metricCards.length > 0 ? (
        metricCards.map(cardData => renderMetricCard(cardData))
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <div className="text-gray-500">
            {Object.values(selectedOptions).every(set => set.size === 0)
              ? "Please select at least one condition from the left panel to view the data"
              : "No data available for the current selection"
            }
          </div>
        </div>
      )}
    </div>
  );
}
