import * as d3 from 'd3';
import { TranslationData, MetricType, AnalysisType } from './types';
import { parseUTRComponents, customColorInterpolator, getUTRTagStyle } from './utils';

// 疾病大类与condition的映射关系
const CONDITION_GROUPS = {
  "Infections": [
    "Adult Hepatocellular Carcinoma; Dengue Virus Infection",
    "Adult Hepatocellular Carcinoma; HCV Transfection",
    "B95-8 Epstein-Barr Virus Infection",
    "Childhood T Lymphoblastic Lymphoma; HIV-1 Infection",
    "Human Cytomegalovirus Infection",
    "Human Papillomavirus-related Endocervical Adenocarcinoma; NDV Infection",
    "IAV Transfection",
    "Lung Adenocarcinoma; IAV Infection",
    "Lung Adenocarcinoma; SARS-CoV-2 Infection",
    "M81 Epstein-Barr Virus Infection",
    "Toxoplasma Infection"
  ],
  "Gastrointestinal System Cancer": [
    "Adult Hepatocellular Carcinoma",
    "Childhood Hepatocellular Carcinoma",
    "Colon Adenocarcinoma",
    "Colon Carcinoma",
    "Esophageal Squamous Cell Carcinoma",
    "Hepatoblastoma",
    "Hepatocellular Carcinoma",
    "Intrahepatic Cholangiocarcinoma"
  ],
  "Musculoskeletal System Cancer": [
    "Osteosarcoma"
  ],
  "Reproductive Organ Cancer": [
    "Human Papillomavirus-related Endocervical Adenocarcinoma",
    "Ovarian Cancer",
    "Ovarian Endometrioid Carcinoma",
    "Prostate Cancer",
    "Prostate Carcinoma"
  ],
  "Respiratory System Cancer": [
    "Lung Adenocarcinoma",
    "Lung Large Cell Carcinoma"
  ],
  "Urinary System Cancer": [
    "Kidney Rhabdoid Cancer",
    "Kidney Tumor",
    "Renal Cell Carcinoma"
  ],
  "Genetic Disease": [
    "Duchenne Muscular Dystrophy",
    "Hbs1L Deficiency",
    "Roberts Syndrome",
    "Tuberous Sclerosis Complex"
  ],
  "Other": [
    "Neoplastic Transformation",
    "Patient-derived",
    "Treacher Collins Syndrome"
  ],
  "Nervous System Cancer": [
    "Astrocytoma",
    "Brain Glioma",
    "Neuroblastoma"
  ],
  "Hematologic Cancer": [
    "Acute Myeloid Leukemia",
    "Adult Acute Myeloid Leukemia",
    "Childhood T Acute Lymphoblastic Leukemia",
    "Childhood T Lymphoblastic Lymphoma",
    "Chronic Myeloid Leukemia",
    "Multiple Myeloma"
  ],
  "Breast Cancer": [
    "Breast Adenocarcinoma",
    "Breast Carcinoma"
  ],
  "Head And Neck Cancer": [
    "Head And Neck Squamous Cell Carcinoma",
    "Tongue Squamous Cell Carcinoma"
  ],
  "Endocrine Gland Cancer": [
    "Pancreatic Adenocarcinoma"
  ]
};

// 获取条件的分组
const getConditionGroup = (condition: string): string => {
  for (const [groupName, conditions] of Object.entries(CONDITION_GROUPS)) {
    if (conditions.includes(condition)) {
      return groupName;
    }
  }
  return 'Normal';
};

// 获取分组颜色
const getGroupColor = (groupName: string): string => {
  const colors = {
    "Infections": "#C06A6A", 
    "Gastrointestinal System Cancer": "#3AAFA9",
    "Musculoskeletal System Cancer": "#3E92CC",
    "Reproductive Organ Cancer": "#A8C686",
    "Respiratory System Cancer": "#F5D88C",
    "Urinary System Cancer": "#DDA0DD",
    "Genetic Disease": "#78C2AD",
    "Other": "#EACD76",
    "Nervous System Cancer": "#9B89B3",
    "Hematologic Cancer": "#6497B1",
    "Breast Cancer": "#E6A96B",
    "Head And Neck Cancer": "#82E0AA", 
    "Endocrine Gland Cancer": "#F1948A",
    "Normal": "#A8B0B3"
  };
  return colors[groupName as keyof typeof colors] || colors["Normal"];
};

interface DownloadHeatmapParams {
  metric: MetricType;
  transcriptIds: string[];
  conditions: string[];
  heatmapData: {[key: string]: {[key: string]: number}};
  columnZScores?: {[key: string]: {[key: string]: number}}; // 添加按列计算的z-score
  min: number;
  max: number;
  translationData: TranslationData[];
  selectedGeneSymbol: string;
  selectedGeneId: string;
  customGeneSymbol: string;
  customGeneId: string;
  analysisType: AnalysisType;
}



export const downloadHeatmapImage = ({
  metric,
  transcriptIds,
  conditions,
  heatmapData,
  columnZScores,
  min,
  max,
  translationData,
  selectedGeneSymbol,
  selectedGeneId,
  customGeneSymbol,
  customGeneId,
  analysisType
}: DownloadHeatmapParams) => {
  if (transcriptIds.length === 0 || conditions.length === 0) return;

  // 计算热力图核心区域尺寸
  const cellSize = 40;
  const heatmapCoreWidth = conditions.length * cellSize;
  const heatmapCoreHeight = transcriptIds.length * cellSize;

  // 计算各个组件的尺寸 - 新布局：UTR + transcriptID + 热图 + 颜色条 + 图例（仅Condition分析）
  const titleHeight = 80;
  const colorBarWidth = 150; // 颜色条区域宽度，增加以容纳标签
  const legendWidth = analysisType === 'Condition' ? 300 : 0; // 只有Condition分析时才显示图例
  const utrAreaWidth = 500; // UTR标签区域宽度
  const transcriptLabelWidth = 200; // transcript ID标签宽度
  const axisLabelHeight = analysisType === 'Condition' ? 180 : 150; // Condition分析时增加高度以容纳分组横条

  // 计算总的内容宽度和高度
  const contentWidth = utrAreaWidth + transcriptLabelWidth + heatmapCoreWidth + colorBarWidth + legendWidth;
  const contentHeight = titleHeight + heatmapCoreHeight + axisLabelHeight;

  // 添加边距使内容居中
  const padding = 50;
  const width = contentWidth + padding * 2;
  const height = contentHeight + padding * 2;

  // 计算居中的起始位置
  const startX = (width - contentWidth) / 2;
  const startY = (height - contentHeight) / 2;

  // 重新定义margin，基于居中位置
  const margin = {
    top: startY + titleHeight,
    left: startX + utrAreaWidth + transcriptLabelWidth, // UTR区域 + transcript标签区域
    right: colorBarWidth + legendWidth, // 颜色条 + 图例区域
    bottom: axisLabelHeight
  };

  // 创建Canvas
  const canvas = document.createElement('canvas');
  canvas.width = width * 2; // 高分辨率
  canvas.height = height * 2;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  ctx.scale(2, 2); // 高分辨率缩放
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, width, height);

  // 使用传入的z-score范围进行颜色映射
  const colorScale = d3.scaleSequential()
    .interpolator(customColorInterpolator)
    .domain([min, max]);

  // 计算每列有值的单元格数量
  const columnValueCounts: {[condition: string]: number} = {};
  conditions.forEach(condition => {
    let count = 0;
    transcriptIds.forEach(transcriptId => {
      const value = heatmapData[transcriptId]?.[condition];
      if (value !== undefined && !isNaN(Number(value))) {
        count++;
      }
    });
    columnValueCounts[condition] = count;
  });

  // 绘制热力图单元格
  transcriptIds.forEach((transcriptId, i) => {
    conditions.forEach((condition, j) => {
      const value = heatmapData[transcriptId]?.[condition];
      // 获取该单元格的z-score用于颜色映射
      const zscore = columnZScores?.[transcriptId]?.[condition];

      const hasNumeric = value !== undefined && !isNaN(Number(value));
      
      // 判断该列是否只有一个有值的单元格，如果是且当前单元格有值，则使用红色背景
      const isOnlyValueInColumn = columnValueCounts[condition] === 1 && hasNumeric;
      
      let fillColor = '#f5f5f5'; // 默认灰色
      if (hasNumeric) {
        if (isOnlyValueInColumn) {
          fillColor = '#A93703'; // 红色背景
        } else if (zscore !== undefined) {
          fillColor = colorScale(zscore); // 正常色阶
        }
      }
      
      ctx.fillStyle = fillColor;
      ctx.fillRect(
        margin.left + j * cellSize,
        margin.top + i * cellSize,
        cellSize - 1,
        cellSize - 1
      );

      if (hasNumeric) {
        const textX = margin.left + j * cellSize + cellSize / 2;
        const textY = margin.top + i * cellSize + cellSize / 2 + 2;
        const text = Number(value).toFixed(2);
        ctx.font = 'bold 10px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        // // 白色描边以提升对比度
        // ctx.lineWidth = 3;
        // ctx.strokeStyle = '#ffffff';
        // ctx.strokeText(text, textX, textY);
        // 黑色实心文字
        ctx.fillStyle = '#111827';
        ctx.fillText(text, textX, textY);
      }

      // 绘制边框
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 1;
      ctx.strokeRect(
        margin.left + j * cellSize,
        margin.top + i * cellSize,
        cellSize - 1,
        cellSize - 1
      );
    });
  });

  // 绘制标题 - 使用与页面相同的标题格式
  const geneIdentifier = selectedGeneId || selectedGeneSymbol || customGeneId || customGeneSymbol;
  const analysisTypeText = (() => {
    switch (analysisType) {
      case 'Tissue/Cell Type':
        return 'tissues/cell types';
      case 'Cell Line':
        return 'cell lines';
      case 'Condition':
        return 'conditions';
      default:
        return 'conditions';
    }
  })();

  const title = `${metric} of ${geneIdentifier} transcripts in different ${analysisTypeText}`;

  ctx.fillStyle = '#111827';
  ctx.font = 'bold 16px Arial';
  ctx.textAlign = 'center';
  ctx.fillText(title, width / 2, startY + 40);

  // 绘制Y轴标签（转录本ID）- 放在热图左边
  ctx.font = '11px Arial';
  ctx.textAlign = 'end'; // 右对齐
  ctx.fillStyle = '#111827';
  transcriptIds.forEach((transcriptId, i) => {
    ctx.fillText(
      transcriptId,
      margin.left - 15, // 热图左边
      margin.top + i * cellSize + cellSize / 2 + 4
    );
  });

  // 绘制分组颜色横条 - 仅在Condition分析时显示
  if (analysisType === 'Condition') {
    const groupBarHeight = 15;
    const groupBarY = margin.top + transcriptIds.length * cellSize + 5;

    // 计算每个分组的范围
    const groupRanges: {[key: string]: {start: number, end: number, color: string}} = {};
    let currentGroup = '';
    let groupStart = 0;

    conditions.forEach((condition, i) => {
      const conditionName = condition.split('_')[0];
      const group = getConditionGroup(conditionName);

      if (group !== currentGroup) {
        // 如果不是第一个分组，保存前一个分组的范围
        if (currentGroup && i > 0) {
          groupRanges[currentGroup] = {
            start: groupStart * cellSize,
            end: i * cellSize,
            color: getGroupColor(currentGroup)
          };
        }
        currentGroup = group;
        groupStart = i;
      }
    });

    // 保存最后一个分组的范围
    if (currentGroup) {
      groupRanges[currentGroup] = {
        start: groupStart * cellSize,
        end: conditions.length * cellSize,
        color: getGroupColor(currentGroup)
      };
    }

    // 绘制分组颜色横条
    Object.entries(groupRanges).forEach(([, range]) => {
      ctx.fillStyle = range.color;
      ctx.globalAlpha = 0.7;
      ctx.fillRect(
        margin.left + range.start,
        groupBarY,
        range.end - range.start,
        groupBarHeight
      );
      ctx.globalAlpha = 1.0;

      // 绘制边框
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 1;
      ctx.strokeRect(
        margin.left + range.start,
        groupBarY,
        range.end - range.start,
        groupBarHeight
      );
    });
  }

  // 绘制X轴标签（条件）- 根据分析类型调整位置
  ctx.fillStyle = '#111827'; // 确保X轴标签使用黑色
  ctx.font = '11px Arial';
  ctx.textAlign = 'start';
  const xLabelY = analysisType === 'Condition'
    ? margin.top + transcriptIds.length * cellSize + 15 + 15 // 有分组横条时的位置
    : margin.top + transcriptIds.length * cellSize + 30; // 无分组横条时的位置

  conditions.forEach((condition, i) => {
    ctx.save();
    ctx.translate(
      margin.left + i * cellSize + cellSize / 2,
      xLabelY
    );
    ctx.rotate(Math.PI / 6); // 30度旋转
    ctx.fillText(condition, 0, 0);
    ctx.restore();
  });

  // 绘制颜色条 - 放在右侧
  const colorBarHeight = transcriptIds.length * cellSize;
  const colorBarX = margin.left + conditions.length * cellSize + 20; // 热图右边
  const colorBarY = margin.top;
  const actualColorBarWidth = 25; // 实际颜色条的宽度，留出空间给标签

  // 绘制颜色条渐变 - 从上到下：高值到低值
  const gradient = ctx.createLinearGradient(0, colorBarY, 0, colorBarY + colorBarHeight);
  const steps = 10;
  for (let i = 0; i <= steps; i++) {
    const ratio = i / steps;
    const value = min + (max - min) * (1 - ratio); // 反转：顶部是最大值
    const color = colorScale(value);
    gradient.addColorStop(ratio, color);
  }
  ctx.fillStyle = gradient;
  ctx.fillRect(colorBarX, colorBarY, actualColorBarWidth, colorBarHeight);

  // 绘制颜色条边框
  ctx.strokeStyle = '#666';
  ctx.lineWidth = 1;
  ctx.strokeRect(colorBarX, colorBarY, actualColorBarWidth, colorBarHeight);

  // 绘制颜色条标签
  ctx.fillStyle = '#111827';
  ctx.font = 'bold 12px Arial';
  ctx.textAlign = 'start';

  // 最大值标签（顶部）
  ctx.fillText(
    `${max.toFixed(2)} (z-score)`,
    colorBarX + actualColorBarWidth + 5,
    colorBarY - 8
  );

  // 最小值标签（底部）
  ctx.fillText(
    `${min.toFixed(2)} (z-score)`,
    colorBarX + actualColorBarWidth + 5,
    colorBarY + colorBarHeight + 15
  );

  // 绘制指向热力图每一行的刻度线
  ctx.strokeStyle = '#333';
  ctx.lineWidth = 1;
  transcriptIds.forEach((_, i) => {
    const y = margin.top + i * cellSize + cellSize / 2;
    ctx.beginPath();
    ctx.moveTo(colorBarX - 20, y); // 从颜色条左边开始
    ctx.lineTo(colorBarX - 10, y); // 指向热力图
    ctx.stroke();
  });

  // 绘制连接所有刻度线的竖线
  if (transcriptIds.length > 1) {
    ctx.beginPath();
    ctx.moveTo(colorBarX - 10, margin.top + cellSize / 2); // 第一行的中心
    ctx.lineTo(colorBarX - 10, margin.top + (transcriptIds.length - 1) * cellSize + cellSize / 2); // 最后一行的中心
    ctx.stroke();
  }

  // 绘制分组图例 - 仅在Condition分析时显示
  if (analysisType === 'Condition') {
    const legendStartX = colorBarX + actualColorBarWidth + 160; // 在颜色条标签后面
    const legendStartY = margin.top;

    // 获取当前热力图中实际出现的分组
    const presentGroups = new Set<string>();
    conditions.forEach(condition => {
      const conditionName = condition.split('_')[0];
      const group = getConditionGroup(conditionName);
      presentGroups.add(group);
    });

    // 绘制图例标题
    ctx.fillStyle = '#111827';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'start';
    ctx.fillText("Category", legendStartX, legendStartY - 10);

    // 绘制图例项
    let legendY = legendStartY + 20;
    Array.from(presentGroups).sort().forEach((groupName) => {
      const color = getGroupColor(groupName);

      // 绘制颜色方块
      ctx.fillStyle = color;
      ctx.globalAlpha = 0.7;
      ctx.fillRect(legendStartX, legendY, 15, 15);
      ctx.globalAlpha = 1.0;

      // 绘制边框
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1;
      ctx.strokeRect(legendStartX, legendY, 15, 15);

      // 绘制分组名称
      ctx.fillStyle = '#111827';
      ctx.font = '11px Arial';
      ctx.textAlign = 'start';
      ctx.fillText(groupName, legendStartX + 20, legendY + 12);

      legendY += 25;
    });
  }

  // 绘制UTR标签区域 - 放在最左边
  const utrStartX = startX;

  // 绘制5' UTR和3' UTR标题
  ctx.fillStyle = '#333333'; // 使用与页面相同的颜色
  ctx.font = 'bold 13px Arial';
  ctx.textAlign = 'start'; // 使用与页面SVG相同的对齐方式
  ctx.textBaseline = 'alphabetic'; // 使用默认的底部对齐，与SVG默认行为一致
  
  // 完全按照页面显示的位置设置（页面中 x=40 和 x=280, y=-10）
  ctx.fillText("5' UTR", utrStartX + 40, margin.top - 10);
  ctx.fillText("3' UTR", utrStartX + 280, margin.top - 10);

  // 绘制每个转录本的UTR标签
  transcriptIds.forEach((transcriptId, i) => {
    const transcript = translationData.find(item => item.transcriptId === transcriptId);
    if (!transcript) return;

    const y = margin.top + i * cellSize + cellSize / 2;

    const fiveUTRComponents = parseUTRComponents(transcript.fiveUtrComp);
    const threeUTRComponents = parseUTRComponents(transcript.threeUtrComp);

    // 5' UTR 标签
    let xOffset = utrStartX + 40;
    Object.entries(fiveUTRComponents).forEach(([key, value]) => {
      if (value === 'yes') {
        const { bgColor, textColor, displayName } = getUTRTagStyle(key);
        const tagWidth = Math.max(displayName.length * 7 + 12, 40);

        // 绘制标签背景
        ctx.fillStyle = bgColor;
        ctx.fillRect(xOffset, y - 8, tagWidth, 16);

        // 绘制标签文字
        ctx.fillStyle = textColor;
        ctx.font = '500 10px Arial';
        ctx.textAlign = 'start';
        ctx.fillText(displayName, xOffset + 6, y + 3);

        xOffset += tagWidth + 6;
      }
    });

    // 3' UTR 标签
    let threeUtrOffset = utrStartX + 280;
    Object.entries(threeUTRComponents).forEach(([key, value]) => {
      if (value === 'yes') {
        const { bgColor, textColor, displayName } = getUTRTagStyle(key);
        const tagWidth = Math.max(displayName.length * 7 + 12, 40);

        // 绘制标签背景
        ctx.fillStyle = bgColor;
        ctx.fillRect(threeUtrOffset, y - 8, tagWidth, 16);

        // 绘制标签文字
        ctx.fillStyle = textColor;
        ctx.font = '500 10px Arial';
        ctx.textAlign = 'start';
        ctx.fillText(displayName, threeUtrOffset + 6, y + 3);

        threeUtrOffset += tagWidth + 6;
      }
    });
  });

  // 下载图片
  const link = document.createElement('a');
  const searchValue = customGeneSymbol || selectedGeneSymbol || customGeneId || selectedGeneId;
  // 生成包含基因信息、分析类型和指标的文件名
  const geneIdentifierForFile = selectedGeneId || selectedGeneSymbol || customGeneId || customGeneSymbol || searchValue;
  const analysisTypeForFile = (() => {
    switch (analysisType) {
      case 'Tissue/Cell Type':
        return 'tissue_cell_types';
      case 'Cell Line':
        return 'cell_lines';
      case 'Condition':
        return 'conditions';
      default:
        return 'conditions';
    }
  })();

  const fileName = `${metric}_${geneIdentifierForFile}_${analysisTypeForFile}_heatmap`.replace(/[^a-zA-Z0-9_]/g, '_') + '.png';

  link.download = fileName;
  link.href = canvas.toDataURL('image/png');
  link.click();
};
