export interface TranslationData {
  id: number;
  transcriptId: string;
  projectId: string;
  bioprojectId: string;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  tr: number | null;
  evi: number | null;
  te: number;
  geneId: string;
  geneSymbol: string;
  threeUtrComp: string;
  fiveUtrComp: string;
}

export interface UTRComponents {
  IRES: string;
  miRNAs: string;
  'PolyA Sites': string;
  Repeats: string;
  'Rfam motifs': string;
  uORFs: string;
}

export type AnalysisType = 'Tissue/Cell Type' | 'Cell Line' | 'Condition';
export type MetricType = 'TE' | 'TR' | 'EVI';
export type SearchType = 'geneSymbol' | 'geneId';

export interface MetricCardData {
  metric: MetricType;
  transcriptIds: string[];
  conditions: string[];
  heatmapData: {[key: string]: {[key: string]: number}};
  columnZScores?: {[key: string]: {[key: string]: number}}; // 按列计算的z-score用于颜色映射
  min: number;
  max: number;
  zScoreMin?: number;
  zScoreMax?: number;
  availableConditions: string[];
}

export interface AnalysisComponentProps {
  translationData: TranslationData[];
  selectedOptions: {[key in MetricType]: Set<string>};
  setSelectedOptions: React.Dispatch<React.SetStateAction<{[key in MetricType]: Set<string>}>>;
  selectedGeneSymbol: string;
  selectedGeneId: string;
  customGeneSymbol: string;
  customGeneId: string;
}
