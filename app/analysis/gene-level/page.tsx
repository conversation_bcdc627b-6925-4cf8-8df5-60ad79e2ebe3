'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { But<PERSON> } from "@/app/components/ui/button";
import Link from 'next/link';
import Image from 'next/image';
import Footer from '../../components/Footer/Footer';
import { Download, ChevronDown } from 'lucide-react';
import TissueCellTypeAnalysis from './TissueCellTypeAnalysis';
import CellLineAnalysis from './CellLineAnalysis';
import ConditionAnalysis from './ConditionAnalysis';
import { TranslationData, AnalysisType, MetricType, SearchType } from './types';

// 静态选项作为后备
const geneSymbolOptions = ["TP53", "PIK3CA", "ARID1A", "PTEN", "APC", "KRAS", "EGFR", "VHL", "FGFR3", "IDH1", "BRAF", "CDH1", "TBX3", "ATRX", "<PERSON><PERSON>", "PBRM1", "STK11", "NFE2L2", "KMT2D", "MUC16"];
const geneIdOptions = [
  "ENSG00000141510",
  "ENSG00000121879",
  "ENSG00000117713",
  "ENSG00000171862",
  "ENSG00000134982",
  "ENSG00000133703",
  "ENSG00000146648",
  "ENSG00000134086",
  "ENSG00000068078",
  "ENSG00000138413",
  "ENSG00000157764",
  "ENSG00000039068",
  "ENSG00000135111",
  "ENSG00000085224",
  "ENSG00000079432",
  "ENSG00000163939",
  "ENSG00000118046",
  "ENSG00000116044",
  "ENSG00000167548",
  "ENSG00000181143"
];

export default function TranslationIndicesPage() {
  const [geneSymbols, setGeneSymbols] = useState<string[]>([]);
  const [geneIds, setGeneIds] = useState<string[]>([]);
  const [selectedGeneSymbol, setSelectedGeneSymbol] = useState<string>('');
  const [selectedGeneId, setSelectedGeneId] = useState<string>('');
  const [customGeneSymbol, setCustomGeneSymbol] = useState<string>('');
  const [customGeneId, setCustomGeneId] = useState<string>('');
  const [analysisType, setAnalysisType] = useState<AnalysisType>('Tissue/Cell Type');
  const [translationData, setTranslationData] = useState<TranslationData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 过滤选项状态 - 每个指标独立
  const [selectedOptions, setSelectedOptions] = useState<{[key in MetricType]: Set<string>}>({
    'TE': new Set(),
    'TR': new Set(),
    'EVI': new Set()
  });
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);

  // 搜索优化状态
  const [geneSymbolQuery, setGeneSymbolQuery] = useState('');
  const [geneIdQuery, setGeneIdQuery] = useState('');
  const [showGeneSymbolDropdown, setShowGeneSymbolDropdown] = useState(false);
  const [showGeneIdDropdown, setShowGeneIdDropdown] = useState(false);
  const geneSymbolInputRef = useRef<HTMLInputElement>(null);
  const geneIdInputRef = useRef<HTMLInputElement>(null);
  const geneSymbolDropdownRef = useRef<HTMLDivElement>(null);
  const geneIdDropdownRef = useRef<HTMLDivElement>(null);

  // 防抖搜索函数
  const debounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const downloadDropdownRef = useRef<HTMLDivElement>(null);
  
  const debouncedSearch = useCallback((query: string, type: 'symbol' | 'id') => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    
    debounceTimeout.current = setTimeout(() => {
      if (type === 'symbol') {
        setGeneSymbolQuery(query);
      } else {
        setGeneIdQuery(query);
      }
    }, 300);
  }, []);

  // 过滤后的选项（只显示匹配的前20个）
  const filteredGeneSymbols = useMemo(() => {
    if (!geneSymbolQuery) return geneSymbolOptions;
    
    const options = geneSymbols.length > 0 ? geneSymbols : geneSymbolOptions;
    return options
      .filter(symbol => symbol.toLowerCase().includes(geneSymbolQuery.toLowerCase()))
      .slice(0, 20);
  }, [geneSymbols, geneSymbolQuery]);

  const filteredGeneIds = useMemo(() => {
    if (!geneIdQuery) return geneIdOptions;
    
    const options = geneIds.length > 0 ? geneIds : geneIdOptions;
    return options
      .filter(id => id.toLowerCase().includes(geneIdQuery.toLowerCase()))
      .slice(0, 20);
  }, [geneIds, geneIdQuery]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (geneSymbolDropdownRef.current && !geneSymbolDropdownRef.current.contains(event.target as Node) &&
          geneSymbolInputRef.current && !geneSymbolInputRef.current.contains(event.target as Node)) {
        setShowGeneSymbolDropdown(false);
      }
      if (geneIdDropdownRef.current && !geneIdDropdownRef.current.contains(event.target as Node) &&
          geneIdInputRef.current && !geneIdInputRef.current.contains(event.target as Node)) {
        setShowGeneIdDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 初始化加载数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 获取基因符号和ID列表
        const [symbolsResponse, idsResponse] = await Promise.all([
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/gene-info/all-gene-symbols`),
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/gene-info/all-gene-ids`)
        ]);

        if (symbolsResponse.ok && idsResponse.ok) {
          const symbols = await symbolsResponse.json();
          const ids = await idsResponse.json();
          
          // 排序
          const sortedSymbols = symbols.sort();
          const sortedIds = ids.sort();
          
          setGeneSymbols(sortedSymbols);
          setGeneIds(sortedIds);
          
          // 默认选择TP53基因符号并加载数据
          const defaultGene = 'TP53'; 
          setSelectedGeneSymbol(defaultGene);
          // 加载默认数据
          await loadTranslationData('geneSymbol', defaultGene);
        }
      } catch (err) {
        console.error('Error initializing data:', err);
        setError('Failed to load initial data');
      }
    };

    initializeData();
  }, []);

  // 加载翻译数据
  const loadTranslationData = async (type: SearchType, value: string) => {
    if (!value) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const apiUrl = type === 'geneSymbol' 
        ? `${process.env.NEXT_PUBLIC_API_URL}/translation-indices/geneSymbol/${encodeURIComponent(value)}`
        : `${process.env.NEXT_PUBLIC_API_URL}/translation-indices/geneId/${encodeURIComponent(value)}`;
      
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(`API returned error: ${response.status}`);
      }
      
      const data = await response.json();
      setTranslationData(data);
      
      // 更新可用选项
      updateAvailableOptions(data);
      
    } catch (err) {
      console.error('Error loading translation data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // 更新可用选项 - 为每个指标分别计算
  const updateAvailableOptions = (data: TranslationData[]) => {
    const metrics: MetricType[] = ['TE', 'TR', 'EVI'];
    const newSelectedOptions: {[key in MetricType]: Set<string>} = {
      'TE': new Set(),
      'TR': new Set(),
      'EVI': new Set()
    };

    metrics.forEach(metric => {
      const metricOptions = new Set<string>();

      data.forEach(item => {
        const value = metric === 'TE' ? item.te : metric === 'TR' ? item.tr : item.evi;
        if (value !== null && value !== undefined && value > 0) {
          let conditionValue = '';
          switch (analysisType) {
            case 'Tissue/Cell Type':
              conditionValue = item.tissueCellType;
              break;
            case 'Cell Line':
              conditionValue = item.cellLine;
              break;
            case 'Condition':
              conditionValue = item.disease;
              break;
          }
          if (conditionValue) {
            metricOptions.add(conditionValue);
          }
        }
      });

      // 默认全选该指标的所有可用选项
      newSelectedOptions[metric] = new Set(metricOptions);
    });

    setSelectedOptions(newSelectedOptions);
  };



  // 处理提交
  const handleSubmit = () => {
    let value = '';
    let type: SearchType = 'geneSymbol';

    // 确定使用哪种搜索方式和值
    if (customGeneSymbol || selectedGeneSymbol) {
      value = customGeneSymbol || selectedGeneSymbol;
      type = 'geneSymbol';
    } else if (customGeneId || selectedGeneId) {
      value = customGeneId || selectedGeneId;
      type = 'geneId';
    }

    if (value) {
      loadTranslationData(type, value);
    }
  };

  // 处理Reset
  const handleReset = () => {
    // 重置所有状态到初始值
    setCustomGeneSymbol('');
    setSelectedGeneSymbol('');
    setCustomGeneId('');
    setSelectedGeneId('');
    setTranslationData([]);
    setSelectedOptions({
      'TE': new Set(),
      'TR': new Set(),
      'EVI': new Set()
    });
    setError('');
    setLoading(false);
    setShowDownloadOptions(false);
    setShowGeneSymbolDropdown(false);
    setShowGeneIdDropdown(false);
    setGeneSymbolQuery('');
    setGeneIdQuery('');

    // 如果有默认基因符号，重新设置
    if (geneSymbols.length > 0) {
      setSelectedGeneSymbol(geneSymbols[0]);
    }
  };



  // 重新生成可用选项（当分析类型改变时）
  useEffect(() => {
    if (translationData.length > 0) {
      updateAvailableOptions(translationData);
    }
  }, [analysisType]);

  // 点击外部关闭下载选项
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (downloadDropdownRef.current && !downloadDropdownRef.current.contains(event.target as Node)) {
        setShowDownloadOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);




  // 数据下载功能
  const downloadData = (format: 'csv' | 'json') => {
    const dataToDownload = translationData;

    if (format === 'json') {
      const jsonData = JSON.stringify(dataToDownload, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const searchValue = customGeneSymbol || selectedGeneSymbol || customGeneId || selectedGeneId;
      a.download = `gene_translation_data_${searchValue.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } else if (format === 'csv') {
      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const headers = ['TRANSCRIPT ID', 'DATASET ID', 'BIOPROJECT ID', 'TISSUE/CELL TYPE', 'CELL LINE', 'CONDITION', 'TE', 'TR', 'EVI', 'GENE ID', 'GENE SYMBOL', '5\' UTR ELEMENTS', '3\' UTR ELEMENTS'];
      const csvContent = [
        headers.join(','),
        ...dataToDownload.map(item => [
          escapeCsvValue(item.transcriptId),
          escapeCsvValue(item.projectId),
          escapeCsvValue(item.bioprojectId),
          escapeCsvValue(item.tissueCellType),
          escapeCsvValue(item.cellLine),
          escapeCsvValue(item.disease),
          escapeCsvValue(item.te),
          escapeCsvValue(item.tr),
          escapeCsvValue(item.evi),
          escapeCsvValue(item.geneId),
          escapeCsvValue(item.geneSymbol),
          escapeCsvValue(item.fiveUtrComp),
          escapeCsvValue(item.threeUtrComp)
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const searchValue = customGeneSymbol || selectedGeneSymbol || customGeneId || selectedGeneId;
      a.download = `gene_translation_data_${searchValue.replace(/[^a-zA-Z0-9]/g, '_')}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    }

    setShowDownloadOptions(false);
  };





  return (
    <div className="min-h-screen bg-gray-50">
      {/* 添加自定义滚动条样式 */}
      <style jsx>{`
        .heatmap-scroll-container::-webkit-scrollbar {
          height: 16px;
        }
        .heatmap-scroll-container::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 4px;
        }
        .heatmap-scroll-container::-webkit-scrollbar-thumb {
          background: #cbd5e0;
          border-radius: 4px;
        }
        .heatmap-scroll-container::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }
        .heatmap-scroll-container::-webkit-scrollbar-corner {
          background: #f1f5f9;
        }
      `}</style>
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span style={{ color: '#337ab7' }}>TE/TR/EVI of Genes across Biological Contexts</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
          {/* 标签页 */}
          <div className="mb-6">
            <div className="flex border-b border-gray-200 bg-white rounded-t-lg">
              {(['Tissue/Cell Type', 'Cell Line', 'Condition'] as AnalysisType[]).map((type) => (
                <button
                  key={type}
                  onClick={() => setAnalysisType(type)}
                  className={`flex-1 py-3 font-medium text-sm border-r border-gray-200 last:border-r-0 text-center ${
                    analysisType === type
                      ? 'bg-[#267DD4] text-white'
                      : 'text-gray-700 hover:bg-[#F9FAFB]'
                  }`}
                >
                  TE/TR/EVI in {type}
                </button>
              ))}
            </div>
          </div>

          {/* 说明文字 */}
          <div className="mb-6 text-gray-700 text-sm leading-relaxed">
            {(() => {
              switch (analysisType) {
                case 'Tissue/Cell Type':
                  return 'The following figures show the heatmap of TE, TR and EVI values (if available) for all transcripts of the input gene across different tissues/cell types. On the left side, the corresponding 5\' UTR and 3\' UTR elements for each transcript are shown. The left panel provides checkbox filters for selecting tissues/cell types of interest to compare. Please note that the same tissue/cell type may also comes from multiple datasets.';
                case 'Cell Line':
                  return 'The following figures show the heatmap of TE, TR and EVI values (if available) for all transcripts of the input gene across different cell lines. On the left side, the corresponding 5\' UTR and 3\' UTR elements for each transcript are shown. The left panel provides checkbox filters for selecting cell lines of interest to compare. Please note that the same cell line may also comes from multiple datasets.';
                case 'Condition':
                  return 'The following figures shows the heatmap of TE, TR and EVI values (if available) for all transcripts of the input gene across different conditions. On the left side, the corresponding 5\' UTR and 3\' UTR elements for each transcript are shown. The left panel provides checkbox filters for selecting conditions of interest to compare. Different colors beneath the image denote the categories of the selected conditions. Please note that the same condition may also comes from multiple datasets.';
                default:
                  return '';
              }
            })()}
            {/* <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-blue-800 text-sm">
                <strong>Note:</strong> The values displayed in the heatmap cells are log₂ transformed values. Darker colors indicate higher original values.
              </p>
            </div> */}
          </div>

{/* 搜索控制区域 */}
<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
  {/* 外层：相对定位 + 主内容居中 */}
  <div className="relative flex flex-wrap items-center justify-center">

    {/* ================= 中   间   区   块 ================= */}
    <div className="flex flex-wrap items-center space-x-6 pr-24">
      {/* ---------- GENE SYMBOL ---------- */}
      <div className="flex items-center space-x-2">
        <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
          Gene Symbol:
        </label>
        <div className="relative">
          <input
            ref={geneSymbolInputRef}
            type="text"
            placeholder="Gene Symbol"
            value={customGeneSymbol || selectedGeneSymbol}
            onChange={(e) => {
              const value = e.target.value;
              setCustomGeneSymbol(value);
              setSelectedGeneSymbol('');
              if (value) {
                setSelectedGeneId('');
                setCustomGeneId('');
                debouncedSearch(value, 'symbol');
                setShowGeneSymbolDropdown(true);
              } else {
                setShowGeneSymbolDropdown(false);
              }
            }}
            onFocus={() =>
              (customGeneSymbol || selectedGeneSymbol) &&
              setShowGeneSymbolDropdown(true)
            }
            disabled={!!selectedGeneId || !!customGeneId}
            className="w-64 h-10 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
          />
          {/* 下拉触发图标 */}
          <button
            type="button"
            className="absolute inset-y-0 right-0 flex items-center pr-2 cursor-pointer"
            onClick={() => {
              geneSymbolInputRef.current?.focus();
              setShowGeneSymbolDropdown(true);
            }}
          >
            <svg
              className="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          {showGeneSymbolDropdown && filteredGeneSymbols.length > 0 && (
            <div
              ref={geneSymbolDropdownRef}
              className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
            >
              {filteredGeneSymbols.map((symbol) => (
                <div
                  key={symbol}
                  className="px-3 py-2 hover:bg-[#f0f4f8] cursor-pointer text-sm"
                  onClick={() => {
                    setCustomGeneSymbol(symbol);
                    setSelectedGeneSymbol('');
                    setShowGeneSymbolDropdown(false);
                    setSelectedGeneId('');
                    setCustomGeneId('');
                  }}
                >
                  {symbol}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* ---------- GENE ID ---------- */}
      <div className="flex items-center space-x-2">
        <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
          Gene ID:
        </label>
        <div className="relative">
          <input
            ref={geneIdInputRef}
            type="text"
            placeholder="Gene ID"
            value={customGeneId || selectedGeneId}
            onChange={(e) => {
              const value = e.target.value;
              setCustomGeneId(value);
              setSelectedGeneId('');
              if (value) {
                setSelectedGeneSymbol('');
                setCustomGeneSymbol('');
                debouncedSearch(value, 'id');
                setShowGeneIdDropdown(true);
              } else {
                setShowGeneIdDropdown(false);
              }
            }}
            onFocus={() =>
              (customGeneId || selectedGeneId) && setShowGeneIdDropdown(true)
            }
            disabled={!!selectedGeneSymbol || !!customGeneSymbol}
            className="w-64 h-10 w-64 h-10 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
          />
          {/* 下拉触发图标 */}
          <button
            type="button"
            className="absolute inset-y-0 right-0 flex items-center pr-2 cursor-pointer"
            onClick={() => {
              geneIdInputRef.current?.focus();
              setShowGeneIdDropdown(true);
            }}
          >
            <svg
              className="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          {showGeneIdDropdown && filteredGeneIds.length > 0 && (
            <div
              ref={geneIdDropdownRef}
              className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
            >
              {filteredGeneIds.map((id) => (
                <div
                  key={id}
                  className="px-3 py-2 hover:bg-[#f0f4f8] cursor-pointer text-sm"
                  onClick={() => {
                    setCustomGeneId(id);
                    setSelectedGeneId('');
                    setShowGeneIdDropdown(false);
                    setSelectedGeneSymbol('');
                    setCustomGeneSymbol('');
                  }}
                >
                  {id}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* ---------- Submit / Reset ---------- */}
      <div className="flex gap-3">
        <Button
          onClick={handleSubmit}
          disabled={loading}
          className={`btn-submit ${loading ? 'loading' : ''}`}
        >
          {loading ? 'Loading…' : 'Submit'}
        </Button>
        <Button
          onClick={handleReset}
          variant="outline"
          className="btn-reset"
        >
          Reset
        </Button>
      </div>
    </div>
    {/* =================  中  间  区  块  结束  ================= */}

    {/* ================= 下载按钮（固定最右） ================= */}
    {translationData.length > 0 && (
      <div
        className="absolute right-0 top-0"
        ref={downloadDropdownRef}
      >
        <button
          onClick={() => setShowDownloadOptions(!showDownloadOptions)}
          className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
        >
          <Download className="h-4 w-4 mr-2" />
          <ChevronDown className="h-4 w-4 ml-2" />
        </button>

        {showDownloadOptions && (
          <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-300 rounded-lg shadow-lg z-50">
            <button
              onClick={() => downloadData('csv')}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
            >
              Download as CSV
            </button>
            <button
              onClick={() => downloadData('json')}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
            >
              Download as JSON
            </button>
          </div>
        )}
      </div>
    )}
    {/* ================= 下载按钮结束 ================= */}

  </div>
</div>

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="text-red-800">{error}</div>
            </div>
          )}

          {/* 分析组件 */}
          {translationData.length > 0 && (
            <>
              {analysisType === 'Tissue/Cell Type' && (
                <TissueCellTypeAnalysis
                  translationData={translationData}
                  selectedOptions={selectedOptions}
                  setSelectedOptions={setSelectedOptions}
                  selectedGeneSymbol={selectedGeneSymbol}
                  selectedGeneId={selectedGeneId}
                  customGeneSymbol={customGeneSymbol}
                  customGeneId={customGeneId}
                />
              )}
              {analysisType === 'Cell Line' && (
                <CellLineAnalysis
                  translationData={translationData}
                  selectedOptions={selectedOptions}
                  setSelectedOptions={setSelectedOptions}
                  selectedGeneSymbol={selectedGeneSymbol}
                  selectedGeneId={selectedGeneId}
                  customGeneSymbol={customGeneSymbol}
                  customGeneId={customGeneId}
                />
              )}
              {analysisType === 'Condition' && (
                <ConditionAnalysis
                  translationData={translationData}
                  selectedOptions={selectedOptions}
                  setSelectedOptions={setSelectedOptions}
                  selectedGeneSymbol={selectedGeneSymbol}
                  selectedGeneId={selectedGeneId}
                  customGeneSymbol={customGeneSymbol}
                  customGeneId={customGeneId}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8">
        <Footer />
      </div>
    </div>
  );
} 