import React from 'react';
import BaseAnalysisComponent from './BaseAnalysisComponent';
import { AnalysisComponentProps, TranslationData } from './types';

export default function CellLineAnalysis(props: AnalysisComponentProps) {
  const getConditionValue = (item: TranslationData) => item.cellLine;

  return (
    <BaseAnalysisComponent
      {...props}
      analysisType="Cell Line"
      getConditionValue={getConditionValue}
    />
  );
}
