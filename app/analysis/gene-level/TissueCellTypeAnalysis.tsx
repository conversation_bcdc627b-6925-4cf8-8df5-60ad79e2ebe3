import React from 'react';
import BaseAnalysisComponent from './BaseAnalysisComponent';
import { AnalysisComponentProps, TranslationData } from './types';

export default function TissueCellTypeAnalysis(props: AnalysisComponentProps) {
  const getConditionValue = (item: TranslationData) => item.tissueCellType;

  return (
    <BaseAnalysisComponent
      {...props}
      analysisType="Tissue/Cell Type"
      getConditionValue={getConditionValue}
    />
  );
}
