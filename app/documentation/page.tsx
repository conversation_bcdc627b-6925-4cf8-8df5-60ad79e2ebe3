'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface NavigationItem {
  id: string;
  title: string;
  children?: NavigationItem[];
}

export default function DocumentationPage() {
  const [activeSection, setActiveSection] = useState('database-overview');

  const navigationItems: NavigationItem[] = [
    {
      id: 'database-overview',
      title: 'Database Overview',
      children: [
        { id: 'about-ted-db', title: '1. About TEDD' },
        { id: 'definition', title: '2. Definition' }
      ]
    },
    {
      id: 'data-source',
      title: 'Data Source'
    },
    {
      id: 'data-workflow',
      title: 'Data Process Workflow'
    },
    {
      id: 'database-usage',
      title: 'Database Usage',
      children: [
        { id: 'browse', title: '1. Browse', children: [
          { id: 'project', title: '1.1. Dataset' },
          { id: 'sample', title: '1.2. Sample' },
          { id: 'gene', title: '1.3. Gene' }
        ]},
        { id: 'search', title: '2. Search' },
        { id: 'analysis', title: '3. Analysis', children: [
          { id: 'gene-level', title: '3.1. TE/TR/EVI of Genes across Biological Contexts' },
          { id: 'transcript-level', title: '3.2. TE/TR/EVI of Transcripts across Biological Contexts' },
          { id: 'functional-level', title: '3.3. TE/TR/EVI of Genes across KEGG pathways/GO terms' }
        ]},
        { id: 'download', title: '4. Download' }
      ]
    },
    {
      id: 'contact',
      title: 'Contact us'
    }
  ];

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Monitor scrolling to update active section
  useEffect(() => {
    const handleScroll = () => {
      const sections = navigationItems.flatMap(item => [
        item,
        ...(item.children || []).flatMap(child => [
          child,
          ...(child.children || [])
        ])
      ]);

      const currentSection = sections.find(section => {
        const element = document.getElementById(section.id);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom > 100;
        }
        return false;
      });

      if (currentSection) {
        setActiveSection(currentSection.id);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const renderNavigationItems = (items: NavigationItem[], level = 0) => {
    return items.map((item) => (
      <div key={item.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <button
          onClick={() => scrollToSection(item.id)}
          className={`block w-full text-left py-2 px-3 text-sm rounded-md transition-all duration-200 ${
            activeSection === item.id 
              ? 'bg-blue-100 text-blue-700 font-semibold border-l-4 border-blue-500' 
              : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
          }`}
        >
          {item.title}
        </button>
        {item.children && (
          <div className="mt-1 mb-2">
            {renderNavigationItems(item.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb navigation */}
      <div className="bg-slate-100 border-b border-slate-200 py-3 px-6">
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="link-breadcrumb">
            Home
          </Link>
          <span className="mx-2 text-black">&gt;</span>
          <span className="link-breadcrumb-current">Documentation</span>
        </div>
      </div>

      <div className="flex">
        {/* Left sidebar navigation - 固定在初始位置 */}
        <div className="w-80 bg-white border-r border-gray-200 sticky top-0 h-screen overflow-y-auto shadow-lg">
          <div className="p-6">
            <nav className="space-y-1">
              {renderNavigationItems(navigationItems)}
            </nav>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 max-w-4xl mx-auto">
          {/* Content area */}
          <div className="p-8">
            
            {/* Database Overview */}
            <section id="database-overview" className="mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                Database Overview
              </h2>
              
              <div className="space-y-8">
                <div id="about-ted-db">
                  <h3 className="text-xl font-semibold text-gray-700 mb-4">
                    1. About TEDD
                  </h3>
                  <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400">
                    <p className="text-gray-700 leading-relaxed">
                    Translation Efficiency Dynamic Database (TEDD) is a user-friendly database that integrates extensive translatomics and transcriptomics data, exploring translation efficiency (TE),translation initiation efficiency (translation ratio, TR), and translation elongation speed (elongation velocity index, EVI) of genes and transcripts and the coresponding UTR features across diverse biological contexts. This database offers browsing, searching, analysis and download functionalities, aiming to offer researchers the opportunity to comprehensively analyze translomics data, thereby promoting the development of both basic research and translational applications in translatomics in human biology and disease. 
                    </p>
                  </div>
                </div>

                <div id="definition">
                  <h3 className="text-xl font-semibold text-gray-700 mb-4">
                    2. Definition
                  </h3>
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <h4 className="font-semibold text-gray-800 mb-2">RNA-seq:</h4>
                      <p className="text-gray-600 text-sm">
                      By using high-throughput sequencing technology to quantitatively and sequentially analyze all RNAs in the sample, it can be applied for alignment, quantification, differential expression, etc.
                      </p>
                    </div>
                  <div className="space-y-4">
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <h4 className="font-semibold text-gray-800 mb-2">Ribo-seq:</h4>
                      <p className="text-gray-600 text-sm">
                      Ribosome Profiling technology, which detects small RNA fragments protected by ribosomes (approximately 22-30 bp), can locate the distribution information of ribosomes on mRNA to infer information such as the position of start codons and uORF.  
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <h4 className="font-semibold text-gray-800 mb-2">RNC-seq:</h4>
                      <p className="text-gray-600 text-sm">
                      Ribosome-Nascent Chain complex sequencing, to detect mRNA fragments in the process of translation and obtain full-length information of mRNA in the process of translation at a certain state.  
                      </p>
                    </div> 
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <h4 className="font-semibold text-gray-800 mb-2">TE:</h4>
                      <p className="text-gray-600 text-sm">
                      Translation efficiency, which characterizes the efficiency of mRNA being translated into proteins.  
                        <span className="inline-block ml-2 font-mono text-gray-800">
                          TE =
                          <span className="inline-block mx-1 relative" style={{verticalAlign: 'middle', fontSize: '0.8em'}}>
                            <span className="block leading-none">Ribo(TPM)</span>
                            <span className="block border-t border-gray-800 leading-none">RNA(TPM)</span>
                          </span>
                        </span>
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <h4 className="font-semibold text-gray-800 mb-2">TR:</h4>
                      <p className="text-gray-600 text-sm">
                      Translation Ratio, representing the translation initiation efficiency of a gene.
                        <span className="inline-block ml-2 font-mono text-gray-800">
                          TR =
                          <span className="inline-block mx-1 relative" style={{verticalAlign: 'middle', fontSize: '0.8em'}}>
                            <span className="block leading-none">RNC(TPM)</span>
                            <span className="block border-t border-gray-800 leading-none">RNA(TPM)</span>
                          </span>
                        </span>
                      </p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <h4 className="font-semibold text-gray-800 mb-2">EVI:</h4>
                      <p className="text-gray-600 text-sm">
                        Elongation Velocity Index, which characterizes the translation elongation speed of a gene. 
                        <span className="inline-block ml-2 font-mono text-gray-800">
                          EVI =
                          <span className="inline-block mx-1 relative" style={{verticalAlign: 'middle', fontSize: '0.8em'}}>
                            <span className="block leading-none text-center">RNC(TPM)²</span>
                            <span className="block border-t border-gray-800 leading-none">RNA(TPM) × Ribo(TPM)</span>
                          </span>
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Data Source */}
            <section id="data-source" className="mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                Data Source
              </h2>
              <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                <p className="text-gray-700 leading-relaxed">
                TEDD collects all publicly available RNA-seq, Ribo-seq and RNC-seq control group data from the Gene Expression Omnibus (GEO) and Short Read Archive (SRA). The current version includes 726 RNA-seq, 738 Ribo-seq and 54 RNC-seq data from 143 projects involving humans, a total of 1,518 samples. These samples were classified into 279 datasets based on project and biological contexts, including 24 tissue/cell types, 74 cell lines, and 52 conditions grouped into 14 categories.
                </p>
              </div>
            </section>

            {/* Data Process Workflow */}
            <section id="data-workflow" className="mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                Data Process Workflow
              </h2>
              
              <div className="mb-6">
                <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400 mb-6">
                  <p className="text-gray-700 leading-relaxed">
                    TEDD has been dedicated to the extensive collection of various translatomics and transcriptomics data, including RNA-seq, Ribo-seq and RNC-seq, and performs multi-level analyses on each data. Custom workflows are established for each data to achieve optimal analysis results. Figure 1 shows an overview of the data processing and integration in TEDD.
                  </p>
                </div>
                
                <div className="text-center my-8">
                  <Image
                    src={`${process.env.basePath || ''}/logo/flow chart.png`}
                    alt="Data processing workflow"
                    width={400}
                    height={500}
                    className="rounded-lg shadow-md mx-auto"
                  />
                  <p className="text-sm text-gray-500 mt-3">
                    <strong>Figure 1.</strong> Overview of the data processing
                  </p>
                </div>

                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Data Processing Pipeline</h4>
                      <p className="text-gray-700 leading-relaxed text-sm">
                        For data collected from the public database GEO, the raw SRA format data is converted to fastq format using the fastq-dump tool from the SRA Toolkit (v3.3.1). Next, adapter sequences are removed from the fastq files using Trim Galore (v0.6.10) and quality filtering is performed using Fastp (v0.23.4) on the fastq files.
                      </p>
                      <p className="text-gray-700 leading-relaxed text-sm mt-3">
                        For RNA-seq and RNC-seq data, the trimmed reads were mapped to human reference genome (hg38, Homo_sapiens.GRCh38.107) using STAR (v2.7.11b). For Ribo-seq data, the quality-filtered data is further processed using Bowtie (v1.3.1) to detect and remove rRNA and ncRNA content. The cleaned data are then aligned to the reference genome using STAR (v2.7.11b). Gene/transcript-level expression profiles are generated using FeatureCount (v2.0.8). Counts were normalized to transcripts per million (TPM).
                      </p>
                      <p className="text-gray-700 leading-relaxed text-sm mt-3">
                        Based on the expression profiles, the translation efficiency (TE), translation initiation efficiency (TR) and translation elongation speed (EVI) at the gene/transcript level are calculated.
                      </p>
                    </div>
                    
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mt-6">
                  <div className="flex items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Data Integration</h4>
                      <p className="text-gray-700 leading-relaxed text-sm">
                      For each dataset, z-scores of TE, TR, and EVI values were calculated for all transcripts and genes. Using the ascending order of these z-scores, genome- and transcriptome-wide distribution plots were generated to provide a global overview of translation efficiency dynamics within each dataset. UTR annotations for TEDD transcripts were integrated from UTRdb 2.0 (https://utrdb.cloud.ba.infn.it/utrdb/index_107.html) and IRESbase (http://reprod.njmu.edu.cn/cgi-bin/iresbase/index.php), encompassing 5′ and 3′ UTR regulatory elements such as IRES, miRNAs, poly(A) sites, repeats, Rfam motifs, and uORFs. All TEDD genes were functionally annotated and mapped to 367 KEGG pathways and 18,474 GO terms. To avoid search errors caused by special characters, the character &apos;/&apos; in KEGG pathway has been replaced with &apos;,&apos;, the character &apos;/&apos; in GO term has been replaced with &apos;,&apos;, the characters &apos;[&apos; and &apos;]&apos; have been replaced with &apos;(&apos; and &apos;)&apos; respectively, and the character &apos;-&gt;&apos; has been replaced with &apos;_to_&apos;. TEDD supports multiple visualization formats—including heatmaps, boxplots, bar plots, and pie charts—and offers three dedicated tools to analyze translation efficiency dynamics: TE/TR/EVI of genes across biological contexts, TE/TR/EVI of transcripts across biological contexts, and TE/TR/EVI of genes across KEGG pathways or GO terms.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Database Usage */}
            <section id="database-usage" className="mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                Database Usage
              </h2>

              {/* Browse functionality */}
              <div id="browse" className="mb-8">
                <h3 className="text-2xl font-semibold text-gray-700 mb-4">
                  1. Browse
                </h3>
                
                <div className="bg-blue-50 p-4 rounded-lg mb-6 border-l-4 border-blue-400">
                  <p className="text-gray-700">
                  The navigation bar features a &apos;Browse&apos; drop-down menu with three options: &apos;Dataset&apos;, &apos;Sample&apos; and &apos;Gene&apos;.
                  </p>
                </div>

                <div className="space-y-6">
                  <div id="project" className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">1.1 Dataset</h4>
                    <p className="text-gray-700 leading-relaxed mb-3 text-sm">
                    By clicking on &apos;Dataset&apos;, users can access information about 143 projects collected in the database, which are organized into 279 Datasets based on different BioProject IDs, tissues/cell types, cell lines and conditions. The information includes the Dataset ID, BioProject ID, GEO accession, the number of translated genes and transcripts within the Dataset, data type, as well as the number of samples, tissue/cell type, cell line, condition, category and the PMID of the corresponding article. Users can filter by tissue/cell type, cell line and condition from the sidebar checkboxes, where the numbers indicate the number of datasets associated with each biological context or locate specific datasets by entering keywords in the search box. By clicking on a specific BioProject (e.g., &apos;PRJNA244941&apos;) or a specific GEO accession (e.g., &apos;GSE56924&apos;), users will be redirected to the detailed page of the corresponding study on NCBI to view further information.
                    </p>
                    <div className="mt-4 ml-4">
                      <h5 className="font-medium text-gray-700 mb-2">1.1.1 Transcriptome-wide distribution</h5>
                      <p className="text-gray-600 text-sm">
                        By clicking on the specific number of translated transcripts in the table on the &apos;Dataset&apos; page, users will be redirected to the &apos;Transcriptome-wide distribution&apos; page for that dataset, displaying the distribution of TE/TR/EVI of all translated transcripts within the specific dataset.
                      </p>
                      <h5 className="font-medium text-gray-700 mb-2 mt-4">1.1.2 Genome-wide distribution</h5>
                      <p className="text-gray-600 text-sm">
                      By clicking on the specific number of translated genes in the table on the &apos;Dataset&apos; page, users will be redirected to the &apos;Genome-wide distribution&apos; page for that dataset, displaying the distribution of TE/TR/EVI of all translated genes within the specific dataset.
                      </p>
                    </div>
                  </div>

                  <div id="sample" className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">1.2 Sample</h4>
                    <p className="text-gray-700 leading-relaxed mb-3 text-sm">
                      By clicking on &apos;Sample&apos;, users can access information about 1,518 samples collected, including the BioSample ID, SRA accession, Dataset ID, GEO accession, BioProject ID, the number of translated transcripts and genes in the sample, tissue/cell Type, cell line, condition, category, data type, and detail experimental information. Users can filter by tissue/cell type, cell line, condition, and data type from the sidebar checkboxes, where the numbers indicate the number of samples associated with each biological context or locate specific samples by entering keywords in the search box. By clicking on a specific BioSample ID (e.g., &apos;SAMN02730062&apos;), SRA accession (e.g., &apos;SRR1257233&apos;), BioProject (e.g., &apos;PRJNA244941&apos;) or GEO accession (e.g., &apos;GSE56924&apos;), users will be redirected to the detailed page of the corresponding NCBI study to view more information. Clicking on a specific Dataset ID (e.g., &apos;TEDD00001&apos;) will take users to the Dataset page to view the specific Dataset corresponding to this sample.
                    </p>
                  </div>

                  <div id="gene" className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">1.3 Gene</h4>
                    <p className="text-gray-700 leading-relaxed mb-3 text-sm">
                      By clicking on &apos;Gene&apos;, users can obtain the information of the genes expressed under each Dataset. Including Gene symbol, Gene ID, Dataset ID, tissue/cell type, cell line, condition, the translation efficiency dynamics of the selected gene under the specific biological context, and the number of translated transcripts corresponding to the gene expression under this dataset. Users can directly enter the Gene Symbol or Gene ID they want to query in the selection bar above, and further filter the results by tissue/cell type, cell line, condition, or locate specific genes by entering keywords in the search box.
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-3 text-sm">
                      When users click on the specific Dataset ID (e.g., &apos;TEDD00001&apos;), they can be redirected to the &apos;Dataset&apos; page to view the specific dataset corresponding to this gene. Clicking on the specific Gene ID (e.g., &apos;ENSG00000139618&apos;), users can obtain the detailed information of the gene in the pop-up window. Including Gene Symbol, Gene ID, Approved Name, Locus Type, Location, Transcript number corresponding to this gene, Transcript Name and Transcript ID. In the pop-up window, clicking on the specific Gene ID (e.g., &apos;ENSG00000139618&apos;), users can be redirected to the specific page of the corresponding gene in NCBI to view detailed information.
                    </p>
                    <div className="mt-4 ml-4">
                      <h5 className="font-medium text-gray-700 mb-2">1.3.1 Translated Transcripts of Gene</h5>
                      <p className="text-gray-600 text-sm">
                      By clicking on the specific translated transcript number in the table on the &apos;Gene&apos; page, users will be redirected to the &apos;Translated Transcripts of Gene&apos; page for that gene. This page provides the translation status of all transcripts under the corresponding gene, including Transcript ID, TE, TR, EVI and the elements in the 5&apos; UTR and 3&apos; UTR of each transcript. When users click on the specific Dataset ID (e.g., &apos;TEDD00001&apos;), they can be redirected to the &apos;Dataset&apos; page to view the specific dataset corresponding to this transcript. Clicking on the specific Transcript ID (e.g., &apos;ENST00000380152&apos;), users can obtain the detailed information of this transcript in the pop-up window. Including the basic information of Transcript ID, Gene symbol, 5&apos; UTR ID, 3&apos; UTR ID, Location and UTR element, including whether IRES, miRNA, PolyA sites, Repeats, Rfam motif, uORF exist, as well as their specific numbers and proportions in UTR sequences. In the pop-up window, clicking on the specific UTR ID (e.g., &apos;5UTR_107_ENST00000380152.8&apos;), users can be redirected to the specific page of the corresponding UTR in UTRdb2.0 to view detailed information.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Search functionality */}
              <div id="search" className="mb-8">
                <h3 className="text-2xl font-semibold text-gray-700 mb-4">
                  2. Search
                </h3>
                
                <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                  <div className="flex items-start mb-4">
                    <div className="bg-green-100 p-2 rounded-full mr-3 mt-1">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <p className="text-gray-700 font-medium">TEDD provides multiple search options to enable efficient access to datasets, samples or genes of interest: i) Quick Search: the search box on the homepage supports real-time queries, allowing users to instantly locate relevant records by entering a gene symbol (e.g., TP53), gene ID (e.g., ENSG00000139618), or dataset ID (e.g., TEDD00001). ii) Advanced Search: TEDD offers three advanced search modules on the &apos;Search&apos; page, as follows:</p>
                  </div>
                  <div className="space-y-4">
                    <div className="bg-white p-4 rounded-lg border border-green-100 shadow-sm">
                      <div className="flex items-start">
                        <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full mr-3 mt-0.5">1</span>
                        <div>
                          <h5 className="font-semibold text-gray-800 mb-1">Search by Dataset</h5>
                          <p className="text-gray-700 text-sm">
                          Users can input Dataset ID, tissue/cell type, cell line and condition to retrieve detailed information in the &apos;Browse Dataset&apos; page.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-green-100 shadow-sm">
                      <div className="flex items-start">
                        <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full mr-3 mt-0.5">2</span>
                        <div>
                          <h5 className="font-semibold text-gray-800 mb-1">Search by Sample</h5>
                          <p className="text-gray-700 text-sm">
                          Users can enter SRA accession, tissue/cell type, cell line and condition to access detailed information in the &apos;Browse Sample&apos; page.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-green-100 shadow-sm">
                      <div className="flex items-start">
                        <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full mr-3 mt-0.5">3</span>
                        <div>
                          <h5 className="font-semibold text-gray-800 mb-1">Search by Gene</h5>
                          <p className="text-gray-700 text-sm">
                          Users can input Gene Symbol, Gene ID, tissue/cell type, cell line and condition to obtain detailed information in the &apos;Browse Gene&apos; page.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Analysis functionality */}
              <div id="analysis" className="mb-8">
                <h3 className="text-2xl font-semibold text-gray-700 mb-4">
                  3. Analysis
                </h3>
                
                <div className="bg-blue-50 p-4 rounded-lg mb-6 border-l-4 border-blue-400">
                  <p className="text-gray-700">
                    The navigation bar features an &apos;Analysis&apos; drop-down menu with three options: &apos;TE/TR/EVI of Genes across Biological Contexts&apos;, &apos;TE/TR/EVI of Transcripts across Biological Contexts&apos; and &apos;TE/TR/EVI of Genes across KEGG pathways/GO_terms&apos;.
                  </p>
                </div>

                <div className="space-y-6">
                  <div id="gene-level" className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">3.1 TE/TR/EVI of Genes across Biological Contexts</h4>
                    <p className="text-gray-700 leading-relaxed text-sm">
                      By clicking on &apos;TE/TR/EVI of Genes across Biological Contexts&apos;, users can perform personalized analyses of TE, TR and EVI for transcripts of the specific gene across different tissues/cell types, cell lines, and conditions. Users can input the desired Gene Symbol or Gene ID in the search box. The heatmaps of TE, TR and EVI for all transcripts in the selected gene across various tissue/cell types, cell lines and conditions will then be displayed. Simultaneously, the elements in the 5&apos; UTR and 3&apos; UTR of each transcript will be shown on the left side. Additionally, users can select the tissue/cell type, cell line or condition of interest to compare from the selection box on the left.
                    </p>
                  </div>

                  <div id="transcript-level" className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">3.2 TE/TR/EVI of Transcripts across Biological Contexts</h4>
                    <p className="text-gray-700 leading-relaxed text-sm">
                      By clicking on &apos;TE/TR/EVI of Transcripts across Biological Contexts&apos;, users can perform personalized analyses of TE, TR and EVI for the specific transcript across different tissue/cell types, cell lines and conditions. Users can enter the desired Transcript ID in the search box. The boxplots of TE, TR and EVI for the selected transcript across various biological contexts will then be displayed. Additionally, users can select the tissue/cell type, cell line or condition of interest to compare from the selection box on the left.
                    </p>
                  </div>

                  <div id="functional-level" className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">3.3 TE/TR/EVI of Genes across Pathways/GO terms</h4>
                    <p className="text-gray-700 leading-relaxed text-sm">
                      By clicking on &apos;TE/TR/EVI of Genes across KEGG pathways/GO terms&apos;, users can perform personalized analyses of TE, TR and EVI for gene sets defined by KEGG pathways/GO terms across the specific tissue/cell type, cell line or condition. Users can input the KEGG pathway or GO term of interest, along with the desired tissue/cell Type, cell line or condition. Then, they can select the genes of interest from the list. The boxplots of TE, TR and EVI for the selected genes under the chosen biological context both at the transcript level and gene level will be displayed.
                    </p>
                  </div>
                </div>
              </div>

              {/* Download functionality */}
              <div id="download" className="mb-8">
                <h3 className="text-2xl font-semibold text-gray-700 mb-4">
                  4. Download
                </h3>
                
                <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                  <p className="text-gray-700 leading-relaxed">
                  The &apos;Download&apos; page provides access to original metadata, along with TE, TR and EVI values for all transcripts and genes in each dataset. Additionally, users can export figures and tables generated in both the &apos;Browse&apos; and &apos;Analysis&apos; modules to their local devices.
                  </p>
                </div>
              </div>
            </section>

            {/* Contact us */}
            <section id="contact" className="mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
                Contact us
              </h2>
              
              <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                <p className="text-gray-700 mb-4">
                If you have any questions or would like to offer suggestions, comments or report bugs, please feel free to contact us.
                </p>
                
                <div className="bg-white p-4 rounded-lg shadow-sm border border-blue-200">
                  <div className="flex items-center">
                    <span className="font-bold text-gray-800 mr-2">Email:</span>
                    <a 
                      href="mailto:<EMAIL>" 
                      style={{ color: "#337AB7" }}
                      className="hover:text-blue-800 underline font-medium transition-colors"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
