'use client';

import React, { useState, useEffect } from 'react';
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Download, Loader2 } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '../components/Footer/Footer';
import { GeneApiDataPoint } from '@/lib/csvUtils';

interface TranslationRow {
  transcriptId: string;
  projectId: string;
  bioprojectId: string;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  te?: number;
  tr?: number;
  evi?: number;
  geneId: string;
  geneSymbol: string;
}

interface SampleData {
  sraAccession: string;
  bioSampleId: string;
  datasetId: string;
  bioProjectId: string;
  geoAccession: string;
  tissueCellType: string;
  cellLine: string;
  condition: string;
  diseaseCategory: string;
  dataType: string;
  platform: string;
  instrument: string;
  libraryLayout: string;
  detail: string;
  translatedTranscriptsNumber: number;
}

interface ProjectData {
  projectId: string;
  geoAccession: string;
  tissueCellType: string;
  cellLine: string;
  disease: string;
}

export default function DownloadPage() {
  const [allData, setAllData] = useState<SampleData[]>([]);
  const [filteredData, setFilteredData] = useState<SampleData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const [allProjects, setAllProjects] = useState<ProjectData[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<ProjectData[]>([]);
  const [loadingProjects, setLoadingProjects] = useState<boolean>(true);
  const [errorProjects, setErrorProjects] = useState<string | null>(null);
  const [projectSearchKeyword, setProjectSearchKeyword] = useState('');
  const [projectPage, setProjectPage] = useState(1);
  const [projectPageSize, setProjectPageSize] = useState(10);

  // 排序状态
  const [sortField, setSortField] = useState<string>('sraAccession');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [projectSortField, setProjectSortField] = useState<string>('projectId');
  const [projectSortDirection, setProjectSortDirection] = useState<'asc' | 'desc'>('asc');

  // 下载状态管理
  const [downloadingStates, setDownloadingStates] = useState<{[key: string]: boolean}>({});
  const [downloadCooldowns, setDownloadCooldowns] = useState<{[key: string]: number}>({});
  const [downloadMessages, setDownloadMessages] = useState<{[key: string]: {type: 'success' | 'error', message: string}}>({});

  // 下载管理工具函数
  const DOWNLOAD_COOLDOWN_MS = 60000; // 1分钟冷却时间

  const isDownloadAllowed = (downloadKey: string): boolean => {
    const now = Date.now();
    const cooldownEnd = downloadCooldowns[downloadKey] || 0;
    return now >= cooldownEnd && !downloadingStates[downloadKey];
  };

  const setDownloadState = (downloadKey: string, isDownloading: boolean) => {
    setDownloadingStates(prev => ({ ...prev, [downloadKey]: isDownloading }));
  };

  const setCooldown = (downloadKey: string) => {
    const cooldownEnd = Date.now() + DOWNLOAD_COOLDOWN_MS;
    setDownloadCooldowns(prev => ({ ...prev, [downloadKey]: cooldownEnd }));
  };

  const setDownloadMessage = (downloadKey: string, type: 'success' | 'error', message: string) => {
    setDownloadMessages(prev => ({ ...prev, [downloadKey]: { type, message } }));
    // 清除消息在3秒后
    setTimeout(() => {
      setDownloadMessages(prev => {
        const newMessages = { ...prev };
        delete newMessages[downloadKey];
        return newMessages;
      });
    }, 3000);
  };

  // 定时器更新冷却时间显示
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setDownloadCooldowns(prev => {
        const updated = { ...prev };
        let hasChanges = false;

        Object.keys(updated).forEach(key => {
          if (updated[key] <= now) {
            delete updated[key];
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // 计算分页
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = filteredData.slice(startIndex, endIndex);

  const projectTotalPages = Math.ceil(filteredProjects.length / projectPageSize);
  const projectStartIndex = (projectPage - 1) * projectPageSize;
  const projectEndIndex = projectStartIndex + projectPageSize;
  const currentProjectData = filteredProjects.slice(projectStartIndex, projectEndIndex);

  // 排序函数
  const sortData = (data: SampleData[]) => {
    return [...data].sort((a, b) => {
      const aValue = a[sortField as keyof SampleData] || '';
      const bValue = b[sortField as keyof SampleData] || '';

      // 特殊处理数字类型的字段
      if (sortField === 'translatedTranscriptsNumber') {
        const aNum = parseInt(String(aValue)) || 0;
        const bNum = parseInt(String(bValue)) || 0;
        return sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
      }

      // 字符串比较
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();

      if (sortDirection === 'asc') {
        return aStr.localeCompare(bStr);
      } else {
        return bStr.localeCompare(aStr);
      }
    });
  };

  const sortProjectData = (data: ProjectData[]) => {
    return [...data].sort((a, b) => {
      const aValue = a[projectSortField as keyof ProjectData] || '';
      const bValue = b[projectSortField as keyof ProjectData] || '';

      // 字符串比较
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();

      if (projectSortDirection === 'asc') {
        return aStr.localeCompare(bStr);
      } else {
        return bStr.localeCompare(aStr);
      }
    });
  };

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleProjectSort = (field: string) => {
    if (projectSortField === field) {
      setProjectSortDirection(projectSortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setProjectSortField(field);
      setProjectSortDirection('asc');
    }
  };

  // 渲染排序图标
  const renderSortIcon = (field: string, currentSortField: string, currentSortDirection: string) => {
    if (currentSortField !== field) {
      return (
        <div className="flex flex-col ml-1 text-xs leading-none">
          <span className="text-gray-300 mb-0.5">▲</span>
          <span className="text-gray-300">▼</span>
        </div>
      );
    }

    return (
      <div className="flex flex-col ml-1 text-xs leading-none">
        <span className={`${currentSortDirection === 'asc' ? 'text-[#337ab7]' : 'text-gray-300'} mb-0.5`}>▲</span>
        <span className={`${currentSortDirection === 'desc' ? 'text-[#337ab7]' : 'text-gray-300'}`}>▼</span>
      </div>
    );
  };

  // 加载数据
  const fetchData = async () => {
    try {
      setLoading(true);
      
      // 添加随机延迟使页面加载变慢 (1.2-3.8秒)
      const initialDelay = 1200 + Math.random() * 2600;
      await new Promise(resolve => setTimeout(resolve, initialDelay));
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/samples/all`);
      
      if (!response.ok) {
        throw new Error(`API returned error: ${response.status}`);
      }
      
      // 添加随机延迟处理响应数据 (300-800毫秒)
      const responseDelay = 300 + Math.random() * 500;
      await new Promise(resolve => setTimeout(resolve, responseDelay));
      
      const result = await response.json();
      
      // 再添加随机延迟处理数据 (200-500毫秒)
      const dataDelay = 200 + Math.random() * 300;
      await new Promise(resolve => setTimeout(resolve, dataDelay));
      
      setAllData(result);
      const sorted = sortData(result);
      setFilteredData(sorted);
      setError(null);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setFilteredData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // 搜索功能
  useEffect(() => {
    let filtered;
    if (!searchKeyword.trim()) {
      filtered = allData;
    } else {
      filtered = allData.filter(item =>
        Object.values(item).some(value =>
          value !== null && String(value).toLowerCase().includes(searchKeyword.toLowerCase())
        )
      );
    }
    const sorted = sortData(filtered);
    setFilteredData(sorted);
    setCurrentPage(1);
  }, [searchKeyword, allData, sortField, sortDirection]);

  // 项目搜索功能
  useEffect(() => {
    let filtered;
    if (!projectSearchKeyword.trim()) {
      filtered = allProjects;
    } else {
      filtered = allProjects.filter(item =>
        Object.values(item).some(value =>
          value !== null && String(value).toLowerCase().includes(projectSearchKeyword.toLowerCase())
        )
      );
    }
    const sorted = sortProjectData(filtered);
    setFilteredProjects(sorted);
    setProjectPage(1);
  }, [projectSearchKeyword, allProjects, projectSortField, projectSortDirection]);

  // 下载单个样本的CSV文件
  const downloadSampleCSV = async (sample: SampleData) => {
    const downloadKey = `sample_${sample.sraAccession}`;

    // 检查是否允许下载
    if (!isDownloadAllowed(downloadKey)) {
      return; // 静默阻止重复下载，不显示错误消息
    }

    try {
      // 设置下载状态
      setDownloadState(downloadKey, true);
      setDownloadMessage(downloadKey, 'success', 'Preparing download...');

      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

    const headers = [
      'BIOSAMPLE ID',
      'SRA ACCESSION',
      'DOWNLOAD PATH',
      'DATASET ID',
      'BIOPROJECT ID',
      'TRANSLATED TRANSCRIPTS NUMBER',
      'TISSUE/CELL TYPE',
      'CELL LINE',
      'CONDITION',
      'DISEASE CATEGORY',
      'DATA TYPE',
      'PLATFORM',
      'INSTRUMENT',
      'LIBRARYLAYOUT',
      'DETAIL'
    ];

    const downloadPath = `https://trace.ncbi.nlm.nih.gov/Traces/?view=run_browser&acc=${sample.sraAccession}&display=data-access`;

    const rowData = [
      escapeCsvValue(sample.bioSampleId),
      escapeCsvValue(sample.sraAccession),
      escapeCsvValue(downloadPath),
      escapeCsvValue(sample.datasetId),
      escapeCsvValue(sample.bioProjectId),
      escapeCsvValue(sample.translatedTranscriptsNumber),
      escapeCsvValue(sample.tissueCellType === 'nan' ? 'NA' : sample.tissueCellType),
      escapeCsvValue(sample.cellLine === 'nan' ? 'NA' : sample.cellLine),
      escapeCsvValue(sample.condition),
      escapeCsvValue(sample.diseaseCategory),
      escapeCsvValue(sample.dataType),
      escapeCsvValue(sample.platform),
      escapeCsvValue(sample.instrument),
      escapeCsvValue(sample.libraryLayout),
      escapeCsvValue(sample.detail)
    ];

    const csvContent = [
      headers.join(','),
      rowData.join(',')
    ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${sample.sraAccession}.SraRunInfo_transcript.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 设置成功状态和冷却时间
      setDownloadMessage(downloadKey, 'success', 'Download started');
      setCooldown(downloadKey);
    } catch (error) {
      console.error('Download failed:', error);
      setDownloadMessage(downloadKey, 'error', 'Download failed, please try again');
    } finally {
      // 清除下载状态
      setDownloadState(downloadKey, false);
    }
  };

  // 生成分页按钮
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (currentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setCurrentPage(currentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous
        </button>
      );
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            currentPage === i
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]'
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
        </button>
      );
    }

    // Next button
    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setCurrentPage(currentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center mt-6">{pages}</div>;
  };

  // fetch projects
  useEffect(() => {
    async function fetchProjects() {
      try {
        setLoadingProjects(true);
        
        // 添加随机延迟使项目数据加载变慢 (800-2200毫秒)
        const projectDelay = 800 + Math.random() * 1400;
        await new Promise(resolve => setTimeout(resolve, projectDelay));
        
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/all`);
        if (!res.ok) throw new Error(`API error ${res.status}`);
        
        // 添加随机延迟处理响应 (200-600毫秒)
        const responseDelay = 200 + Math.random() * 400;
        await new Promise(resolve => setTimeout(resolve, responseDelay));
        
        const data = await res.json();
        const mapped: ProjectData[] = data.map((item: Record<string, unknown>) => ({
          projectId: item.projectId,
          geoAccession: item.geoAccession,
          tissueOrCellType: item.tissueOrCellType,
          cellLine: item.cellLine,
          disease: item.disease
        }));
        setAllProjects(mapped);
        const sorted = sortProjectData(mapped);
        setFilteredProjects(sorted);
        setErrorProjects(null);
      } catch (err) {
        setErrorProjects((err as Error).message);
        setFilteredProjects([]);
      } finally {
        setLoadingProjects(false);
      }
    }
    fetchProjects();
  }, []);

  // search filter projects
  useEffect(() => {
    if (!projectSearchKeyword.trim()) {
      setFilteredProjects(allProjects);
    } else {
      const term = projectSearchKeyword.toLowerCase();
      setFilteredProjects(
        allProjects.filter(p =>
          Object.values(p).some(v => v && String(v).toLowerCase().includes(term))
        )
      );
    }
    setProjectPage(1);
  }, [projectSearchKeyword, allProjects]);

  // download distribution csv
  const downloadDistributionCSV = async (projectId: string) => {
    const downloadKey = `distribution_${projectId}`;

    // 检查是否允许下载
    if (!isDownloadAllowed(downloadKey)) {
      return; // 静默阻止重复下载，不显示错误消息
    }

    try {
      // 设置下载状态
      setDownloadState(downloadKey, true);
      setDownloadMessage(downloadKey, 'success', 'Fetching data...');

      // 添加随机延迟使下载准备变慢 (500-1500毫秒)
      const downloadDelay = 500 + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, downloadDelay));

      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';
      const res = await fetch(`${apiUrl}/translation-indices/projectId/${projectId}`);
      if (!res.ok) throw new Error(`API error ${res.status}`);
      const json = await res.json();
      if (!Array.isArray(json) || json.length === 0) {
        setDownloadMessage(downloadKey, 'error', 'No data available for download');
        return;
      }
      const headers = [
        'TRANSCRIPT_ID',
        'DATASET_ID',
        'BIOPROJECT_ID',
        'TISSUECELLTYPE',
        'CELL_LINE',
        'CONDITION',
        'TE',
        'TR',
        'EVI',
        'GENE_ID',
        'GENE_SYMBOL'
      ];
      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const rows = (json as TranslationRow[]).map(d => [
        escapeCsvValue(d.transcriptId),
        escapeCsvValue(d.projectId),
        escapeCsvValue(d.bioprojectId),
        escapeCsvValue(d.tissueCellType),
        escapeCsvValue(d.cellLine),
        escapeCsvValue(d.disease),
        escapeCsvValue(d.te),
        escapeCsvValue(d.tr),
        escapeCsvValue(d.evi),
        escapeCsvValue(d.geneId),
        escapeCsvValue(d.geneSymbol)
      ]);
      setDownloadMessage(downloadKey, 'success', 'Preparing download...');

      const csv = [headers.join(','), ...rows.map(r => r.join(','))].join('\n');
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${projectId}.distribution_transcript.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 设置成功状态和冷却时间
      setDownloadMessage(downloadKey, 'success', 'Download started');
      setCooldown(downloadKey);
    } catch (err) {
      console.error(err);
      setDownloadMessage(downloadKey, 'error', 'Download failed, please try again');
    } finally {
      // 清除下载状态
      setDownloadState(downloadKey, false);
    }
  };

  // download gene csv
  const downloadGeneCSV = async (projectId: string) => {
    const downloadKey = `gene_${projectId}`;

    // 检查是否允许下载
    if (!isDownloadAllowed(downloadKey)) {
      return; // 静默阻止重复下载，不显示错误消息
    }

    try {
      // 设置下载状态
      setDownloadState(downloadKey, true);
      setDownloadMessage(downloadKey, 'success', 'Fetching gene data...');

      // 添加随机延迟使基因数据下载准备变慢 (400-1200毫秒)
      const geneDownloadDelay = 400 + Math.random() * 800;
      await new Promise(resolve => setTimeout(resolve, geneDownloadDelay));

      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';
      const res = await fetch(`${apiUrl}/genes/projectId/${projectId}`);
      if (!res.ok) throw new Error(`API error ${res.status}`);
      const json: GeneApiDataPoint[] = await res.json();
      if (!Array.isArray(json) || json.length === 0) {
        setDownloadMessage(downloadKey, 'error', 'No gene data available for download');
        return;
      }
      const headers = [
        'GENE_ID',
        'GENE_SYMBOL',
        'DATASET_ID',
        'EXPRESSED_TRANSCRIPT_NUMBER',
        'CHROMOSOME',
        'TISSUECELLTYPE',
        'CELL_LINE',
        'CONDITION',
        'TE',
        'TR',
        'EVI'
      ];

      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const rows = json.map((item: GeneApiDataPoint) => [
        escapeCsvValue(item.geneId),
        escapeCsvValue(item.geneSymbol),
        escapeCsvValue(item.projectId),
        escapeCsvValue(item.expressedTranscriptNumber),
        escapeCsvValue(item.chromosome),
        escapeCsvValue(item.tissueCellType === 'nan' ? 'NA' : item.tissueCellType),
        escapeCsvValue(item.cellLine === 'nan' ? 'NA' : item.cellLine),
        escapeCsvValue(item.disease),
        escapeCsvValue(item.te),
        escapeCsvValue(item.tr),
        escapeCsvValue(item.evi)
      ]);

      setDownloadMessage(downloadKey, 'success', 'Preparing download...');

      const csv = [headers.join(','), ...rows.map(r => r.join(','))].join('\n');
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${projectId}.genes_transcript.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 设置成功状态和冷却时间
      setDownloadMessage(downloadKey, 'success', 'Download started');
      setCooldown(downloadKey);
    } catch (err) {
      console.error(err);
      setDownloadMessage(downloadKey, 'error', 'Gene data download failed, please try again');
    } finally {
      // 清除下载状态
      setDownloadState(downloadKey, false);
    }
  };

  const renderProjectPagination = () => {
    if (projectTotalPages <= 1) return null;
    const buttons: React.ReactElement[] = [];
    const max = 5;
    let start = Math.max(1, projectPage - Math.floor(max / 2));
    const end = Math.min(projectTotalPages, start + max - 1);
    if (end - start + 1 < max) start = Math.max(1, end - max + 1);

    const pageButton = (label: string | number, pageNumber: number | null, isActive = false) => (
      <button
        key={label}
        onClick={() => pageNumber !== null && setProjectPage(pageNumber)}
        disabled={pageNumber === null}
        className={`px-4 py-2 mx-1 text-sm font-medium rounded-lg transition-colors border ${
          isActive
            ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]'
            : 'border-gray-300 hover:bg-gray-50 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed'
        }`}
      >
        {label}
      </button>
    );

    if (projectPage > 1) buttons.push(pageButton('Previous', projectPage - 1));

    for (let i = start; i <= end; i++) {
      buttons.push(pageButton(i, i, i === projectPage));
    }

    if (projectPage < projectTotalPages) buttons.push(pageButton('Next', projectPage + 1));

    return <div className="flex justify-center items-center">{buttons}</div>;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6">
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="link-breadcrumb">
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span className='link-breadcrumb-current'>Download</span>
        </div>
      </div>

      {/* 标题区域 */}
      <div className="py-6">
          <div className="flex items-center">
            <div className="flex-grow border-t border-gray-300"></div>
            <h2 className="text-3xl font-bold text-gray-900 px-6">Original Metadata</h2>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>
        </div>

      {/* 主内容区域 */}
      <div style={{ margin: '0 2.5%', width: '95%' }} className="py-6">
        {/* 描述文本 */}
        <div className="mb-8 text-gray-700 leading-relaxed">
          <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400 shadow-sm">
            <p className="text-lg">
              Due to the huge volume of raw sequencing data, we didn&apos;t provide the download of raw reads, but provided the corresponding download link to the original database (see &apos;Original Metadata&apos;).
            </p>
          </div>
        </div>

        {/* 搜索和表格区域 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-lg mt-12">
          {/* 搜索栏 */}
          <div className="p-6 flex justify-between items-center border-b border-gray-200 bg-gray-50 rounded-t-lg">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                {loading ? 'Loading...' : `${filteredData.length} Sample${filteredData.length === 1 ? '' : 's'} Found`}
              </h2>
            </div>
            <div className="flex items-center">
              <Input
                placeholder="Search samples..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="w-80 h-10 shadow-sm"
              />
            </div>
          </div>

          {/* 表格内容 */}
          {loading ? (
            <div className="p-12 text-center">
              <div className="inline-flex items-center space-x-2">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <p className="text-gray-600 text-lg">Loading data...</p>
              </div>
            </div>
          ) : error ? (
            <div className="p-12 border-red-500 border-l-4 bg-red-50">
              <p className="text-red-600 text-lg">Error: {error}</p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th
                        className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                        onClick={() => handleSort('sraAccession')}
                      >
                        <div className="flex items-center justify-center">
                          <span>SRA Accession</span>
                          {renderSortIcon('sraAccession', sortField, sortDirection)}
                        </div>
                      </th>
                      <th
                        className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                        onClick={() => handleSort('geoAccession')}
                      >
                        <div className="flex items-center justify-center">
                          <span>GEO Accession</span>
                          {renderSortIcon('geoAccession', sortField, sortDirection)}
                        </div>
                      </th>
                      <th
                        className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                        onClick={() => handleSort('datasetId')}
                      >
                        <div className="flex items-center justify-center">
                          <span>Dataset ID</span>
                          {renderSortIcon('datasetId', sortField, sortDirection)}
                        </div>
                      </th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider">
                        Original Metadata
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-100">
                    {currentData.map((item, index) => (
                      <tr key={index} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-[#f0f4f8] transition-colors duration-150`}>
                        <td className="px-6 py-4 text-sm text-center font-medium">
                          {item.sraAccession && item.sraAccession !== 'nan' && item.sraAccession !== 'null' ? (
                            <a
                              href={`https://www.ncbi.nlm.nih.gov/sra/?term=${item.sraAccession}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[#337ab7] hover:underline transition-colors duration-150"
                            >
                              {item.sraAccession}
                            </a>
                          ) : (
                            <span className="text-gray-900">NA</span>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-center">
                          {item.geoAccession && item.geoAccession !== 'nan' && item.geoAccession !== 'null' ? (
                            <a
                              href={`https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=${item.geoAccession}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[#337ab7]hover:underline transition-colors duration-150"
                            >
                              {item.geoAccession}
                            </a>
                          ) : (
                            <span className="text-gray-900">NA</span>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-center">
                          {item.datasetId && item.datasetId !== 'nan' && item.datasetId !== 'null' ? (
                            <Link
                              href={`/browse/dataset?search=${item.datasetId}`}
                              className="text-[#337ab7] hover:underline transition-colors duration-150 font-medium"
                            >
                              {item.datasetId}
                            </Link>
                          ) : (
                            <span className="text-gray-900">NA</span>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-center">
                          <div className="flex flex-col items-center">
                            {(() => {
                              const downloadKey = `sample_${item.sraAccession}`;
                              const isDownloading = downloadingStates[downloadKey];
                              const message = downloadMessages[downloadKey];

                              return (
                                <>
                                  <Button
                                    onClick={() => downloadSampleCSV(item)}
                                    disabled={isDownloading}
                                    className="bg-white hover:bg-[#F9FAFB] text-gray-700 hover:text-black h-9 flex items-center px-4 shadow-sm"
                                    title={isDownloading ? 'Downloading...' : 'Download CSV'}
                                  >
                                    <span className="mr-2 text-sm">{item.sraAccession}.SraRunInfo_transcript.csv</span>
                                    {isDownloading ? (
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                      <Download className="h-4 w-4" />
                                    )}
                                  </Button>
                                  {message && (
                                    <div className={`mt-1 text-xs ${
                                      message.type === 'success' ? 'text-green-600' : 'text-red-600'
                                    }`}>
                                      {message.message}
                                    </div>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredData.length === 0 && (
                <div className="text-center py-16">
                  <div className="text-gray-400 text-6xl mb-4">📄</div>
                  <p className="text-gray-500 text-lg">No data available</p>
                </div>
              )}

              {/* 分页和控制 */}
              <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 rounded-b-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <span className="text-sm font-medium text-gray-700">
                      Showing {startIndex + 1} to {Math.min(endIndex, filteredData.length)} of {filteredData.length} rows
                    </span>
                    <div className="flex items-center space-x-2">
                      <label className="text-sm font-medium text-gray-700">
                        <select
                          value={pageSize}
                          onChange={(e) => {
                            setPageSize(Number(e.target.value));
                            setCurrentPage(1);
                          }}
                          className="border border-gray-300 rounded px-2 py-1 text-sm ml-2 mr-2"
                        >
                          <option value={10}>10</option>
                          <option value={25}>25</option>
                          <option value={50}>50</option>
                          <option value={100}>100</option>
                        </select>
                        rows per page
                      </label>
                    </div>
                  </div>

                  {renderPagination()}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      
      {/* ================= Transcriptome-wide distribution Section ================= */}

      {/* 标题区域 */}
      <div className="py-6">
          <div className="flex items-center">
            <div className="flex-grow border-t border-gray-300"></div>
            <h2 className="text-3xl font-bold text-gray-900 px-6">TE, TR and EVI of Transcripts/Genes within Datasets</h2>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>
        </div>

      {/* 主内容区域 */}
      <div style={{ margin: '0 2.5%', width: '95%' }} className="py-6">
        {/* 描述文本 */}
        <div className="mb-8 text-gray-700 leading-relaxed">
          <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400 shadow-sm">
            <p className="text-lg">
             The TE, TR and EVI of all transcripts/genes within each dataset are provided.
            </p>
          </div>
        </div>

        {/* 搜索和表格区域 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-lg mt-12">
        {/* Toolbar */}
        <div className="p-6 flex justify-between items-center border-b border-gray-200 bg-gray-50 rounded-t-lg">
          <div>
            <h3 className="text-2xl font-semibold text-gray-900">
              {loadingProjects ? 'Loading...' : `${filteredProjects.length} Dataset${filteredProjects.length === 1 ? '' : 's'} Found`}
            </h3>
          </div>
          <Input
            placeholder="Search datasets..."
            value={projectSearchKeyword}
            onChange={(e) => setProjectSearchKeyword(e.target.value)}
            className="w-80 h-10 shadow-sm"
          />
        </div>

        {loadingProjects ? (
          <div className="p-12 text-center text-gray-600">Loading data...</div>
        ) : errorProjects ? (
          <div className="p-12 text-center text-red-600">Error: {errorProjects}</div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 text-sm">
                <thead className="bg-gray-100">
                  <tr>
                    <th
                      className="px-6 py-4 text-center font-semibold text-gray-700 cursor-pointer hover:bg-gray-200"
                      onClick={() => handleProjectSort('projectId')}
                    >
                      <div className="flex items-center justify-center">
                        <span>DATASET ID</span>
                        {renderSortIcon('projectId', projectSortField, projectSortDirection)}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-center font-semibold text-gray-700 cursor-pointer hover:bg-gray-200"
                      onClick={() => handleProjectSort('geoAccession')}
                    >
                      <div className="flex items-center justify-center">
                        <span>GEO ACCESSION</span>
                        {renderSortIcon('geoAccession', projectSortField, projectSortDirection)}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-center font-semibold text-gray-700 cursor-pointer hover:bg-gray-200"
                      onClick={() => handleProjectSort('tissueCellType')}
                    >
                      <div className="flex items-center justify-center">
                        <span>TISSUE/CELL TYPE</span>
                        {renderSortIcon('tissueCellType', projectSortField, projectSortDirection)}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-center font-semibold text-gray-700 cursor-pointer hover:bg-gray-200"
                      onClick={() => handleProjectSort('cellLine')}
                    >
                      <div className="flex items-center justify-center">
                        <span>CELL LINE</span>
                        {renderSortIcon('cellLine', projectSortField, projectSortDirection)}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-center font-semibold text-gray-700 cursor-pointer hover:bg-gray-200"
                      onClick={() => handleProjectSort('disease')}
                    >
                      <div className="flex items-center justify-center">
                        <span>CONDITION</span>
                        {renderSortIcon('disease', projectSortField, projectSortDirection)}
                      </div>
                    </th>
                    <th className="px-6 py-4 text-center font-semibold text-gray-700">TRANSCRIPT DETAIL</th>
                    <th className="px-6 py-4 text-center font-semibold text-gray-700">GENE DETAIL</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {currentProjectData.map((proj, idx) => (
                    <tr key={idx} className="hover:bg-blue-50">
                      <td className="px-6 py-4 text-center">
                        {proj.projectId && proj.projectId !== 'nan' && proj.projectId !== 'null' ? (
                          <Link
                            href={`/browse/dataset/${proj.projectId}`}
                            className="text-[#337ab7] hover:underline transition-colors duration-150"
                          >
                            {proj.projectId}
                          </Link>
                        ) : (
                          <span className="text-gray-900">NA</span>
                        )}
                      </td>
                      <td className="px-6 py-4 text-center">{proj.geoAccession && proj.geoAccession !== 'nan' && proj.geoAccession !== 'null' ? proj.geoAccession : 'NA'}</td>
                      <td className="px-6 py-4 text-center">{proj.tissueCellType && proj.tissueCellType !== 'nan' && proj.tissueCellType !== 'null' ? proj.tissueCellType : 'NA'}</td>
                      <td className="px-6 py-4 text-center">{proj.cellLine && proj.cellLine !== 'nan' && proj.cellLine !== 'null' ? proj.cellLine : 'NA'}</td>
                      <td className="px-6 py-4 text-center">{proj.disease && proj.disease !== 'nan' && proj.disease !== 'null' ? proj.disease : 'NA'}</td>
                      <td className="px-6 py-4 text-center">
                        <div className="flex flex-col items-center">
                          {(() => {
                            const downloadKey = `distribution_${proj.projectId}`;
                            const isDownloading = downloadingStates[downloadKey];
                            const message = downloadMessages[downloadKey];

                            return (
                              <>
                                <Button
                                  onClick={() => downloadDistributionCSV(proj.projectId)}
                                  disabled={isDownloading}
                                  className="bg-white hover:bg-[#F9FAFB] text-gray-700 hover:text-black h-9 flex items-center px-4 shadow-sm"
                                  title={isDownloading ? 'Downloading...' : 'Download Transcript CSV'}
                                >
                                  <span className="mr-2">{`${proj.projectId}_transcript.csv`}</span>
                                  {isDownloading ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Download className="h-4 w-4" />
                                  )}
                                </Button>
                                {message && (
                                  <div className={`mt-1 text-xs ${
                                    message.type === 'success' ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {message.message}
                                  </div>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-center">
                        <div className="flex flex-col items-center">
                          {(() => {
                            const downloadKey = `gene_${proj.projectId}`;
                            const isDownloading = downloadingStates[downloadKey];
                            const message = downloadMessages[downloadKey];

                            return (
                              <>
                                <Button
                                  onClick={() => downloadGeneCSV(proj.projectId)}
                                  disabled={isDownloading}
                                  className="bg-white hover:bg-[#F9FAFB] text-gray-700 hover:text-black h-9 flex items-center px-4 shadow-sm"
                                  title={isDownloading ? 'Downloading...' : 'Download Gene CSV'}
                                >
                                  <span className="mr-2">{`${proj.projectId}_genes.csv`}</span>
                                  {isDownloading ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Download className="h-4 w-4" />
                                  )}
                                </Button>
                                {message && (
                                  <div className={`mt-1 text-xs ${
                                    message.type === 'success' ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {message.message}
                                  </div>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页和控制 */}
            <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 rounded-b-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6">
                  <span className="text-sm font-medium text-gray-700">
                    Showing {projectStartIndex + 1} to {Math.min(projectEndIndex, filteredProjects.length)} of {filteredProjects.length} rows
                  </span>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-gray-700">
                      <select
                        value={projectPageSize}
                        onChange={(e) => {setProjectPageSize(Number(e.target.value)); setProjectPage(1);}}
                        className="border border-gray-300 rounded px-2 py-1 text-sm ml-2 mr-2"
                      >
                        <option value={10}>10</option>
                        <option value={25}>25</option>
                        <option value={50}>50</option>
                        <option value={100}>100</option>
                      </select>
                      rows per page
                    </label>
                  </div>
                </div>
                {renderProjectPagination()}
              </div>
            </div>
          </>
        )}
        </div>
      </div>

      {/* Footer */}
      <div className="mt-4">
        <Footer />
      </div>
    </div>
  );
}
