'use client';

import React, { useState, useRef, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDown } from 'lucide-react';

interface IsolatedInputProps {
  value: string;
  onChange: (value: string) => void;
  getOptions: (inputValue: string) => string[];
  placeholder?: string;
}

export const IsolatedInput: React.FC<IsolatedInputProps> = React.memo(({ 
  value, 
  onChange, 
  getOptions, 
  placeholder 
}) => {
  const [showOptions, setShowOptions] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const lastScrollY = useRef<number>(0);

  // 使用getOptions函数动态获取选项，避免数组引用变化
  const filteredOptions = useMemo(() => {
    return getOptions(value);
  }, [getOptions, value]);

  // 计算下拉框位置
  const calculateFixedPosition = useCallback(() => {
    if (!wrapperRef.current) return;
    
    const rect = wrapperRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const dropdownHeight = 240;
    
    const shouldShowUp = spaceBelow < dropdownHeight && rect.top >= dropdownHeight;
    
    setDropdownPosition({
      top: shouldShowUp ? rect.top - dropdownHeight : rect.bottom,
      left: rect.left,
      width: rect.width
    });
    
    lastScrollY.current = window.scrollY;
  }, []);

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setShowOptions(false);
    }
  }, []);

  // 简化的输入处理 - 直接调用onChange，不使用防抖
  const handleInputChange = useCallback((newValue: string) => {
    onChange(newValue);
    
    if (!showOptions) {
      setShowOptions(true);
      calculateFixedPosition();
    }
  }, [onChange, showOptions, calculateFixedPosition]);

  // 选择选项
  const handleOptionClick = useCallback((option: string) => {
    onChange(option);
    setShowOptions(false);
  }, [onChange]);

  const handleFocus = useCallback(() => {
    setShowOptions(true);
    calculateFixedPosition();
  }, [calculateFixedPosition]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowOptions(false);
    } else if (e.key === 'Enter') {
      setShowOptions(false);
    }
  }, []);

  const handleBlur = useCallback(() => {
    // 延迟关闭下拉框，避免与点击选项冲突
    setTimeout(() => {
      setShowOptions(false);
    }, 150);
  }, []);

  // 添加事件监听器
  React.useEffect(() => {
    if (showOptions) {
      const handleScroll = () => {
        const currentScrollY = window.scrollY;
        const scrollDelta = Math.abs(currentScrollY - lastScrollY.current);
        
        if (scrollDelta > 100) {
          setShowOptions(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [showOptions, handleClickOutside]);

  // 下拉选项组件
  const DropdownOptions = () => {
    if (!showOptions) return null;

    return createPortal(
      <div
        data-dropdown-portal
        className="fixed bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto z-50"
        style={{
          top: dropdownPosition.top,
          left: dropdownPosition.left,
          width: dropdownPosition.width,
        }}
      >
        {filteredOptions.length > 0 ? (
          filteredOptions.map((option, index) => (
            <div
              key={index}
              className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
              onMouseDown={(e) => {
                e.preventDefault(); // 防止触发 blur
                e.stopPropagation();
                handleOptionClick(option);
              }}
            >
              {option}
            </div>
          ))
        ) : (
          <div className="px-3 py-2 text-gray-500 text-sm">No options found</div>
        )}
      </div>,
      document.body
    );
  };

  return (
    <div className="relative" ref={wrapperRef}>
      <div className="relative flex items-center">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || 'Select or type...'}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
        />
        <ChevronDown 
          className="absolute right-2 h-4 w-4 text-gray-400 pointer-events-none" 
        />
      </div>
      <DropdownOptions />
    </div>
  );
});

IsolatedInput.displayName = 'IsolatedInput';
