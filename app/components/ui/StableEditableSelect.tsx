'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDown } from 'lucide-react';

interface StableEditableSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder?: string;
}

export const StableEditableSelect: React.FC<StableEditableSelectProps> = ({ 
  value, 
  onChange, 
  options, 
  placeholder 
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [showOptions, setShowOptions] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const lastScrollY = useRef<number>(0);
  const changeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInternalChange = useRef(false);

  // 只在外部 value 变化且不是内部触发的变化时更新 inputValue
  useEffect(() => {
    if (!isInternalChange.current && value !== inputValue) {
      setInputValue(value);
    }
    isInternalChange.current = false;
  }, [value, inputValue]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
      }
    };
  }, []);

  const filteredOptions = inputValue 
    ? options.filter(option => 
        option.toLowerCase().includes(inputValue.toLowerCase())
      )
    : options;

  // 计算并固定下拉框位置
  const calculateFixedPosition = useCallback(() => {
    if (!wrapperRef.current) return;
    
    const rect = wrapperRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const dropdownHeight = 240;
    
    // 确定显示方向和位置
    const shouldShowUp = spaceBelow < dropdownHeight && rect.top >= dropdownHeight;
    
    setDropdownPosition({
      top: shouldShowUp ? rect.top - dropdownHeight : rect.bottom,
      left: rect.left,
      width: rect.width
    });
    
    // 记录当前滚动位置
    lastScrollY.current = window.scrollY;
  }, []);

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setShowOptions(false);
    }
  }, []);

  useEffect(() => {
    if (showOptions) {
      const handleScroll = () => {
        const currentScrollY = window.scrollY;
        const scrollDelta = Math.abs(currentScrollY - lastScrollY.current);
        
        // 如果滚动距离超过100px，关闭下拉框
        if (scrollDelta > 100) {
          setShowOptions(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [showOptions, handleClickOutside]);

  const handleInputChange = useCallback((newValue: string) => {
    // 立即更新本地状态
    setInputValue(newValue);
    isInternalChange.current = true;

    // 清除之前的定时器
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
    }

    // 不在输入过程中调用 onChange，只在失去焦点或选择选项时调用
    // 这样可以完全避免输入过程中的父组件重新渲染

    if (!showOptions) {
      setShowOptions(true);
      calculateFixedPosition();
    }
  }, [showOptions, calculateFixedPosition]);

  const handleOptionClick = useCallback((option: string) => {
    setInputValue(option);
    isInternalChange.current = true;

    // 清除防抖定时器
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
    }

    // 选择选项时立即更新父组件
    onChange(option);
    setShowOptions(false);
  }, [onChange]);

  const handleFocus = useCallback(() => {
    setShowOptions(true);
    calculateFixedPosition();
  }, [calculateFixedPosition]);

  const handleBlur = useCallback(() => {
    // 延迟处理 blur，避免点击下拉选项时触发
    setTimeout(() => {
      // 失去焦点时，立即同步到父组件
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
      }

      // 只在值真正改变时才调用 onChange
      if (inputValue !== value) {
        onChange(inputValue);
      }
    }, 150);
  }, [onChange, inputValue, value]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowOptions(false);
    } else if (e.key === 'Enter') {
      // Enter 键确认输入
      setShowOptions(false);
      if (inputValue !== value) {
        onChange(inputValue);
      }
    }
  }, [inputValue, value, onChange]);

  // 下拉选项组件 - 使用Portal渲染到body
  const DropdownOptions = () => {
    if (!showOptions) return null;

    return createPortal(
      <div
        className="fixed bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto z-50"
        style={{
          top: dropdownPosition.top,
          left: dropdownPosition.left,
          width: dropdownPosition.width,
        }}
      >
        {filteredOptions.length > 0 ? (
          filteredOptions.map((option, index) => (
            <div
              key={index}
              className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
              onClick={() => handleOptionClick(option)}
            >
              {option}
            </div>
          ))
        ) : (
          <div className="px-3 py-2 text-gray-500 text-sm">No options found</div>
        )}
      </div>,
      document.body
    );
  };

  return (
    <div className="relative" ref={wrapperRef}>
      <div className="relative flex items-center">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || 'Select or type...'}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
        />
        <ChevronDown 
          className="absolute right-2 h-4 w-4 text-gray-400 pointer-events-none" 
        />
      </div>
      <DropdownOptions />
    </div>
  );
};
