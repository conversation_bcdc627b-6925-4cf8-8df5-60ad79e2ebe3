import React from "react";
import Image from "next/image";

const HomeCard = ({
  title,
  iconSource,
  description,
}: {
  title: string;
  iconSource: string;
  description: string;
}) => {
  return (
    <div className="p-2">
      <div className="relative flex items-start mt-[50px] group">
        <div className="flex items-start justify-end border border-teal-700 bg-white p-6 min-w-[300px] w-[380px] h-[180px] rounded-md shadow-md relative group-hover:border-none">
          <div className="flex-1">
            <h2 className="text-lg font-bold mb-2 w-3/5">{title}</h2>
            <p className="text-gray-600">{description}</p>
          </div>
        </div>
        <div className="absolute top-[-50px] right-[20px] w-[100px] h-[100px] rounded-full bg-sky-700 group-hover:bg-teal-700 flex items-center justify-center">
            <Image
            src={iconSource}
            alt="icon"
            width={80}
            height={80}
            />
        </div>
      </div>
    </div>
  );
};

export default HomeCard;