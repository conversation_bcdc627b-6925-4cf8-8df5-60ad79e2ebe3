'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDown } from 'lucide-react';

interface EditableSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder?: string;
}

export const EditableSelect: React.FC<EditableSelectProps> = React.memo(({ value, onChange, options, placeholder }) => {
  const [inputValue, setInputValue] = useState(value);
  const [showOptions, setShowOptions] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const [isFocused, setIsFocused] = useState(false);

  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const lastScrollY = useRef<number>(0);
  const changeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 只在组件首次挂载或 value 从外部改变且输入框未聚焦时更新 inputValue
  useEffect(() => {
    if (!isFocused && value !== inputValue) {
      setInputValue(value);
    }
  }, [value, isFocused, inputValue]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
      }
    };
  }, []);


  
  const filteredOptions = inputValue 
    ? options.filter(option => 
        option.toLowerCase().includes(inputValue.toLowerCase())
      )
    : options;

  // 计算并固定下拉框位置
  const calculateFixedPosition = useCallback(() => {
    if (!wrapperRef.current) return;
    
    const rect = wrapperRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const dropdownHeight = 240;
    
    // 确定显示方向和位置
    const shouldShowUp = spaceBelow < dropdownHeight && rect.top >= dropdownHeight;
    
    setDropdownPosition({
      top: shouldShowUp ? rect.top - dropdownHeight : rect.bottom,
      left: rect.left,
      width: rect.width
    });
    
    // 记录当前滚动位置
    lastScrollY.current = window.scrollY;
  }, []);

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setShowOptions(false);
    }
  }, []);

  useEffect(() => {
    if (showOptions) {
      const handleScroll = () => {
        const currentScrollY = window.scrollY;
        const scrollDelta = Math.abs(currentScrollY - lastScrollY.current);
        
        // 如果滚动距离超过100px，关闭下拉框
        if (scrollDelta > 100) {
          setShowOptions(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [showOptions, handleClickOutside]);

  const handleInputChange = useCallback((newValue: string) => {
    // 立即更新本地状态，不等待父组件
    setInputValue(newValue);

    // 清除之前的定时器
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
    }

    // 使用防抖延迟调用父组件的 onChange
    changeTimeoutRef.current = setTimeout(() => {
      onChange(newValue);
    }, 300); // 300ms 防抖

    if (!showOptions) {
      setShowOptions(true);
      calculateFixedPosition();
    }
  }, [onChange, showOptions, calculateFixedPosition]);

  const handleOptionClick = useCallback((option: string) => {
    setInputValue(option);

    // 清除防抖定时器，立即更新
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
    }

    onChange(option);
    setShowOptions(false);
  }, [onChange]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
    setShowOptions(true);
    calculateFixedPosition();
  }, [calculateFixedPosition]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);

    // 失去焦点时，如果有待处理的更新，立即执行
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
      onChange(inputValue);
    }
  }, [onChange, inputValue]);

  const toggleOptions = useCallback(() => {
    if (!showOptions) {
      calculateFixedPosition();
    }
    setShowOptions(!showOptions);
  }, [showOptions, calculateFixedPosition]);



  // 下拉选项组件 - 使用Portal渲染到body
  const DropdownOptions = () => {
    if (!showOptions) return null;

    return createPortal(
      <div 
        className="fixed bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto z-50"
        style={{
          top: dropdownPosition.top,
          left: dropdownPosition.left,
          width: dropdownPosition.width
        }}
      >
        {filteredOptions.map((option, index) => (
          <div
            key={`${option}-${index}`}
            className="px-4 py-2 cursor-pointer hover:bg-gray-100"
            onMouseDown={(e) => {
              e.preventDefault();
              handleOptionClick(option);
            }}
          >
            {option}
          </div>
        ))}
      </div>,
      document.body
    );
  };

  return (
    <div className="relative" ref={wrapperRef}>
      <div className="relative flex items-center">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder || 'Select or type...'}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
        />
        <ChevronDown 
            className="h-4 w-4 text-gray-500 absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
            onClick={toggleOptions}
        />
      </div>
      <DropdownOptions />
    </div>
  );
});

EditableSelect.displayName = 'EditableSelect';