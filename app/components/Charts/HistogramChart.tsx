'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, ReferenceLine } from 'recharts';

interface HistogramData {
  bin: number;
  count: number;
  Q1: number;
  Q2: number;
  Q3: number;
  Q4: number;
}

interface HistogramChartProps {
  data: HistogramData[];
  title: string;
  skewness: number;
  totalCount: number;
}

interface TooltipProps {
  active?: boolean;
  payload?: Array<{
    color: string;
    dataKey: string;
    value: number;
  }>;
  label?: number;
}

const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border rounded shadow-lg">
        <p className="font-medium">{`Position: ${label}σ`}</p>
        {payload.map((entry, index: number) => (
          <p key={index} style={{ color: entry.color }}>
            {`${entry.dataKey}: ${entry.value}`}
          </p>
        ))}
        <p className="text-sm text-gray-600 mt-1">
          Total: {payload.reduce((sum: number, entry) => sum + entry.value, 0)}
        </p>
      </div>
    );
  }
  return null;
};

export default function HistogramChart({ data, title, skewness, totalCount }: HistogramChartProps) {
  const formatXAxisLabel = (value: number) => `${value}σ`;

  return (
    <div className="w-full h-full bg-white p-2">
      <div className="mb-3">
        <h3 className="text-lg font-semibold text-center mb-2">{title}</h3>
        <div className="text-sm text-gray-700 mb-2">
          <p>skewness = {skewness.toFixed(3)}</p>
          <p>n = {totalCount.toLocaleString()}</p>
        </div>
      </div>

      <div style={{ height: 'calc(100% - 120px)' }} className="relative">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 20,
              right: 120,
              left: 60,
              bottom: 50,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            {/* Add reference line at x=0 */}
            <ReferenceLine 
              x={0} 
              stroke="#ff0000" 
              strokeWidth={2} 
              strokeDasharray="5 5"
              label={{ value: "0", position: "top" }}
            />
            <XAxis 
              dataKey="bin" 
              tickFormatter={formatXAxisLabel}
              domain={['dataMin', 'dataMax']}
              fontSize={11}
              axisLine={true}
              tickLine={true}
            />
            <YAxis 
              label={{ value: 'Frequency', angle: -90, position: 'insideLeft' }}
              fontSize={11}
              axisLine={true}
              tickLine={true}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend
              layout="vertical"
              align="right"
              verticalAlign="middle"
              wrapperStyle={{
                position: 'absolute',
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '12px',
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '4px',
                padding: '6px 10px',
                zIndex: 10
              }}
              content={({ payload }) => (
                <div>
                  <div className="font-medium text-sm mb-2">Quantile</div>
                  {payload?.map((entry, index) => (
                    <div key={index} className="flex items-center mb-1">
                      <div
                        className="w-4 h-3 mr-2"
                        style={{ backgroundColor: entry.color }}
                      ></div>
                      <span className="text-xs">{entry.value}</span>
                    </div>
                  ))}
                </div>
              )}
            />
            
            <Bar 
              dataKey="Q1" 
              stackId="a" 
              fill="#D73027" 
              name="Q1"
            />
            <Bar 
              dataKey="Q2" 
              stackId="a" 
              fill="#FC8D59" 
              name="Q2"
            />
            <Bar 
              dataKey="Q3" 
              stackId="a" 
              fill="#91BFDB" 
              name="Q3"
            />
            <Bar 
              dataKey="Q4" 
              stackId="a" 
              fill="#4575B4" 
              name="Q4"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
} 