'use client';

import React from 'react';
import { <PERSON><PERSON>hart, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface SimpleBarData {
  range: string;
  count: number;
}

interface SimpleBarChartProps {
  data: SimpleBarData[];
  title: string;
  totalCount: number;
  color?: string;
}

interface TooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
  }>;
  label?: string;
}

const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border rounded shadow-lg">
        <p className="font-medium">{`范围: ${label}`}</p>
        <p>{`数量: ${payload[0].value}`}</p>
      </div>
    );
  }
  return null;
};

export default function SimpleBarChart({ 
  data, 
  title, 
  totalCount, 
  color = "#8884d8" 
}: SimpleBarChartProps) {
  return (
    <div className="w-full h-96 bg-white p-4 rounded-lg shadow-md">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-center mb-2">{title}</h3>
        <div className="text-sm text-gray-600">
          <p>总数量: {totalCount.toLocaleString()}</p>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="range"
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis 
            label={{ value: '频率', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          
          <Bar 
            dataKey="count" 
            fill={color}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
} 