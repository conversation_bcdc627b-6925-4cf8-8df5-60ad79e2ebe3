'use client';

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface HistogramData {
  bin: string;
  frequency: number;
  quantile: string;
}

interface EChartsHistogramProps {
  data: HistogramData[];
  title: string;
  skewness: number;
  totalCount: number;
  chartId: string; // 用于唯一标识图表
  xAxisLabel?: string; // 横坐标标签
}

const EChartsHistogram: React.FC<EChartsHistogramProps> = ({
  data,
  title,
  skewness,
  totalCount,
  chartId,
  xAxisLabel
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current || !data.length) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 准备数据 - 按quantile分组，使用与表格一致的颜色
    const quantileColors = {
      'Q1': '#1d4ed8',  // blue-600，对应 text-blue-800
      'Q2': '#0284c7',  // sky-600，对应 text-sky-800  
      'Q3': '#ea580c',  // orange-600，对应 text-orange-800
      'Q4': '#dc2626'   // red-600，对应 text-red-800
    };

    // 为每个quantile创建一个系列
    const seriesData = ['Q1', 'Q2', 'Q3', 'Q4'].map(quantile => {
      const quantileData = data
        .filter(d => d.quantile === quantile)
        .map(d => [parseFloat(d.bin), d.frequency])
        .sort((a, b) => a[0] - b[0]);

      return {
        name: quantile,
        type: 'bar',
        stack: 'total',
        data: quantileData,
        itemStyle: {
          color: quantileColors[quantile as keyof typeof quantileColors]
        }
      };
    });

    // 配置选项
    const option = {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#374151'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: Array<{axisValue: number; value: [number, number]; seriesName: string; color: string}>) {
          const binValue = params[0].axisValue;
          const formattedBin = `${binValue}σ`;
          let total = 0;
          let content = `<strong>Bin: ${formattedBin}</strong><br/>`;

          params.forEach((param) => {
            if (param.value && param.value[1] > 0) {
              content += `${param.seriesName}: ${param.value[1].toLocaleString()}<br/>`;
              total += param.value[1];
            }
          });

          content += `<strong>Total: ${total.toLocaleString()}</strong>`;
          return content;
        }
      },
      grid: {
        left: '15%',
        right: '10%',
        top: '25%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: -6,
        max: 6,
        interval: 2,
        name: xAxisLabel,
        nameLocation: 'middle',
        nameGap: 35,
        nameTextStyle: {
          fontSize: 14,
          color: '#374151'
        },
        axisLabel: {
          fontSize: 12,
          color: '#6b7280',
          formatter: (value: number) => {
            return `${value}σ`;
          }
        },
        axisLine: {
          lineStyle: {
            color: '#d1d5db'
          }
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        name: 'Frequency',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 14,
          color: '#374151'
        },
        min: 0,
        scale: false,
        boundaryGap: [0, '5%'],
        axisLabel: {
          fontSize: 12,
          color: '#6b7280',
          formatter: (value: number) => value.toLocaleString()
        },
        axisLine: {
          lineStyle: {
            color: '#d1d5db'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6',
            type: 'dashed'
          }
        }
      },
      series: [
        ...seriesData.map(series => ({
          ...series,
          barWidth: '80%',
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        })),
        // 添加中心线系列
        {
          name: 'Center Line',
          type: 'line',
          data: [[0, 0]], // 在x=0处添加一个虚拟点
          showSymbol: false,
          lineStyle: {
            color: 'transparent'
          },
          markLine: {
            silent: true,
            symbol: 'none',
            lineStyle: {
              color: '#dc2626',
              width: 2,
              type: 'dashed'
            },
            data: [
              {
                xAxis: 0,
                label: {
                  show: false
                }
              }
            ]
          }
        }
      ],
      graphic: [
        {
          type: 'text',
          left: '10%',
          top: 60,
          style: {
            text: `skewness = ${skewness.toFixed(3)}`,
            fontSize: 14,
            fontWeight: 'bold',
            fill: '#374151'
          }
        },
        {
          type: 'text',
          left: '10%',
          top: 80,
          style: {
            text: `n = ${totalCount.toLocaleString()}`,
            fontSize: 14,
            fontWeight: 'bold',
            fill: '#374151'
          }
        }
      ],
      legend: {
        data: [
          { name: 'Q1', itemStyle: { color: '#1d4ed8' } },
          { name: 'Q2', itemStyle: { color: '#0284c7' } },
          { name: 'Q3', itemStyle: { color: '#ea580c' } },
          { name: 'Q4', itemStyle: { color: '#dc2626' } }
        ],
        right: 20,
        top: 60,
        orient: 'vertical',
        itemWidth: 14,
        itemHeight: 14,
        textStyle: {
          fontSize: 12,
          color: '#374151'
        },
        type: 'scroll'
      },
      toolbox: {
        feature: {
          saveAsImage: {
            show: true,
            title: 'Download as PNG',
            name: `${chartId}_distribution`,
            pixelRatio: 2,
            backgroundColor: '#ffffff',
            icon: 'path://M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4 M7 10l5 5 5-5 M12 15V3'  // Download图标SVG路径
          }
        },
        right: 20,
        top: 20,
        iconStyle: {
          borderColor: '#666'
        },
        emphasis: {
          iconStyle: {
            borderColor: '#333'
          }
        }
      },
      backgroundColor: '#ffffff'
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chartInstance.current?.dispose();
    };
  }, [data, title, skewness, totalCount, chartId, xAxisLabel]);

  // 提供下载方法给父组件
  useEffect(() => {
    if (chartInstance.current) {
      // 将下载方法暴露到全局，供父组件调用
      (window as unknown as Record<string, () => void>)[`downloadChart_${chartId}`] = () => {
        if (chartInstance.current) {
          const url = chartInstance.current.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#ffffff'
          });
          
          const link = document.createElement('a');
          link.download = `${chartId}_distribution.png`;
          link.href = url;
          link.click();
        }
      };
    }

    return () => {
      delete (window as unknown as Record<string, () => void>)[`downloadChart_${chartId}`];
    };
  }, [chartId]);

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: '100%', minHeight: '300px' }}
      data-chart-type={chartId}
    />
  );
};

export default EChartsHistogram;
