"use client";

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { motion, MotionConfig } from 'framer-motion';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import {
  Home,
  Eye,
  FileText,
  BarChart3,
  BookOpen,
  Search,
  ClipboardList,
  Download
} from 'lucide-react';
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";

export type IMenu = {
  id: number;
  title: string;
  url: string;
  dropdown?: boolean;
  items?: IMenu[];
  icon?: React.ReactNode;
};

const menuItems: IMenu[] = [
  {
    id: 1,
    title: 'Home',
    url: '/',
    icon: <Home className="w-5 h-5 mr-2" />
  },
  {
    id: 2,
    title: 'Browse',
    url: '/browse/dataset',
    dropdown: true,
    icon: <Eye className="w-5 h-5 mr-2" />,
    items: [
      {
        id: 21,
        title: 'Dataset',
        url: '/browse/dataset',
      },
      {
        id: 22,
        title: 'Sample',
        url: '/browse/sample',
      },
      {
        id: 23,
        title: 'Gene',
        url: '/browse/gene',
      },
    ],
  },
  {
    id: 3,
    title: 'Search',
    url: '/search',
    icon: <Search className="w-5 h-5 mr-2" />
  },
  {
    id: 4,
    title: 'Analysis',
    url: '/analysis',
    dropdown: true,
    icon: <FileText className="w-5 h-5 mr-2" />,
    items: [
      {
        id: 41,
        title: 'TE/TR/EVI of Genes across Biological Contexts',
        url: '/analysis/gene-level',
      },
      {
        id: 42,
        title: 'TE/TR/EVI of Transcripts across Biological Contexts',
        url: '/analysis/transcript-level',
      },
      {
        id: 43,
        title: 'TE/TR/EVI of Genes across KEGG pathways/GO terms',
        url: '/analysis/function-level',
      },
    ],
  },
  {
    id: 5,
    title: 'Statistics',
    url: '/statistics',
    icon: <BarChart3 className="w-5 h-5 mr-2" />
  },
  {
    id: 6,
    title: 'Documentation',
    url: '/documentation',
    icon: <BookOpen className="w-5 h-5 mr-2" />
  },
  {
    id: 7,
    title: 'Download',
    url: '/download',
    icon: <Download className="w-5 h-5 mr-2" />
  },
  {
    id: 8,
    title: 'Reference',
    url: '/reference',
    icon: <ClipboardList className="w-5 h-5 mr-2" />
  },
];

const NavBar = () => {
  const router = useRouter();
  const [hovered, setHovered] = useState<number | null>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>("");

  const handleSearch = () => {
    if (!searchKeyword.trim()) {
      return;
    }

    const trimmedKeyword = searchKeyword.trim();

    // 根据搜索内容的前缀决定跳转的页面
    if (trimmedKeyword.startsWith('TEDD')) {
      // TEDD开头 -> browse/dataset页面
      router.push(`/browse/dataset?search=${encodeURIComponent(trimmedKeyword)}`);
    } else if (trimmedKeyword.startsWith('ENSG')) {
      // ENSG开头 -> browse/gene页面
      router.push(`/browse/gene?search=${encodeURIComponent(trimmedKeyword)}`);
    } else {
      // 其他内容 -> browse/sample页面
      router.push(`/browse/sample?search=${encodeURIComponent(trimmedKeyword)}`);
    }
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKeyword(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <MotionConfig transition={{ bounce: 0, type: 'tween' }}>
      <div className="relative w-screen">
        {/* Background Image - covers both header and navigation */}
        <div
          className="absolute inset-0 w-screen bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url(${process.env.basePath || ''}/logo/Background.png)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center center',
          }}
        />

        {/* Background Color Overlay - covers both header and navigation */}
        <div
          className="absolute inset-0 w-screen"
          style={{
            backgroundColor: 'rgba(0, 76, 153, 0.4)',
          }}
        />

        <div className="relative z-10">
          {/* Top Section with TED-DB, Search, and Description */}
          <div>
            <div className="px-6 py-6">
              <div className="flex items-start justify-between">
                {/* Left side: Logo, Title and Description */}

                {/* Logo and TED-DB Title */}
                <div className="flex items-center ml-6">
                  <Image
                    src={`${process.env.basePath || ''}/logo/TEDDB.png`}
                    alt="TED-DB Logo"
                    width={100}
                    height={100}
                    className="mr-2 flex-shrink-0"
                    style={{ width: '100px', height: '100px' }}
                    priority
                  />
                  <div className="flex flex-col items-start flex-shrink-0">
                    <h1 className="text-[40px] font-bold text-white tracking-wider">
                      TEDD
                    </h1>
                    <p className="text-[14px] font-medium leading-relaxed text-white">
                    <span className="font-bold">T</span>ranslation <span className="font-bold">E</span>fficiency <span className="font-bold">D</span>ynamics <span className="font-bold">D</span>atabase is a comprehensive<br/>
                    resource for translation efficiency (TE), translation initiation efficiency (TR), <br/>
                    and translation elongation speed (EVI).
                    </p>
                  </div>
                </div>


                {/* Center Search Box - 向右移动，离右边框更近 */}
                <div className="w-[40%] mt-2 max-w-2xl ml-20 mr-[2%] flex items-center justify-center">
                  <div className="flex flex-col w-full">
                      <div className="flex pt-6">
                      <Input
                        placeholder="Search..."
                        value={searchKeyword}
                        onChange={handleSearchInputChange}
                        onKeyDown={handleKeyDown}
                        className="rounded-r-none h-10 text-[14px] bg-white border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 shadow-sm focus:shadow-md transition-all duration-200"
                      />
                      <Button
                        onClick={handleSearch}
                        className="rounded-l-none h-10 px-6 text-[14px] font-semibold text-white bg-[#0071BC] hover:bg-[#2B7FFF] flex-shrink-0 shadow-sm hover:shadow-md transition-all duration-200 border border-blue-600 hover:border-blue-700"
                      >
                        Search
                      </Button>
                    </div>
                    {/* Examples Text - moved here */}
                    <div className="text-white text-[14px] mt-2 text-left">
                      <span className="font-medium italic">e.g.</span>
                      <Link
                        href="/browse/gene?geneSymbol=TP53"
                        className="ml-2 hover:underline text-white italic"
                        style={{ color: 'white' }}
                      >
                        TP53
                      </Link> 
                      <span>, </span>
                      <Link
                        href="/browse/gene?geneId=ENSG00000139618"
                        className="hover:underline"
                        style={{ color: 'white' }}
                      >
                        ENSG00000139618
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Right Side Description
                <div className="flex-shrink-0 max-w-xs mr-4">
                  <div 
                    className="p-4 rounded-lg text-white text-center"
                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  >
                    <p className="text-xl leading-relaxed font-medium">
                    Translation Efficiency Dynamic Database
                    </p>
                  </div>
                </div> */}
              </div>


            </div>
          </div>

          {/* Navigation Menu - now with background image */}
          <nav style={{ backgroundColor: 'rgba(0, 102, 204, 0.3)' }}>
            <div className="mx-auto max-w-full">
              <div className="px-6">
                <ul className="flex items-center gap-12 px-6">
                  {menuItems.map((item) => {
                    return (
                      <li key={item.id} className="relative flex-shrink-0">
                        {/* 修改菜单栏的hover颜色 */}
                        <Link
                          className={`
                            relative flex items-center justify-center px-4 py-4 text-[14px] font-bold transition-all
                            hover:bg-[#ace8ff] hover:text-[#054a91]
                            ${hovered === item.id ? 'bg-[#ace8ff] text-[#054a91]' : 'text-white'}
                      `}
                          onMouseEnter={() => setHovered(item.id)}
                          onMouseLeave={() => setHovered(null)}
                          href={item.url}
                        >
                          {item.icon}
                          <span className="whitespace-nowrap">{item.title}</span>
                        </Link>
                        {hovered === item.id && !item.dropdown && (
                          <motion.div
                            layout
                            layoutId="cursor"
                            className="absolute bottom-0 h-0 w-full bg-white"
                          />
                        )}
                        {item.dropdown && hovered === item.id && (
                          <div
                            className="absolute left-0 top-full z-10"
                            onMouseEnter={() => setHovered(item.id)}
                            onMouseLeave={() => setHovered(null)}
                          >
                            <motion.div
                              layout
                              transition={{ bounce: 0 }}
                              initial={{ y: 10, opacity: 0 }}
                              animate={{ y: 0, opacity: 1 }}
                              exit={{ y: 10, opacity: 0 }}
                              className="mt-1 flex w-96 flex-col rounded-md shadow-lg ring-1 ring-white ring-opacity-20"
                              style={{ backgroundColor: '#ace8ff', color: '#054a91' }}
                            >
                              {item.items?.map((nav) => (
                                <Link
                                  key={`link-${nav.id}`}
                                  href={nav.url}
                                  className="block px-4 py-3 text-[14px] text-[#054a91] hover:bg-[#f1f5f9] whitespace-nowrap font-bold"
                                >
                                  {nav.title}
                                </Link>
                              ))}
                            </motion.div>
                          </div>
                        )}
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </MotionConfig>
  );
};

export default NavBar; 