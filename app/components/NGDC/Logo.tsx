import Image from "next/image";
import Link from "next/link";
export default function Logo({ variant }: { variant: "light" | "dark" }) {
  const logoSource =
    variant === "dark"
      ? `${process.env.basePath}/logo/cncb-logo-w.png`
      : `${process.env.basePath}/logo/cncb-logo.png`;
  return (
    <div>
      <Link href="https://www.cncb.ac.cn/services">
        <Image
          src={logoSource}
          alt="logo"
          width={150}
          height={130}
        />
      </Link>
    </div>
  );
}
