import React from "react";
import { Button } from "@/app/components/ui/button";

const NGDC_header = () => {
  // 统一使用白色文字，因为背景是蓝色
  const textColor = "text-white hover:text-white hover:bg-white/20";
  
  return (
    <div className="px-2 flex items-center justify-between" style={{ backgroundColor: 'rgba(0, 102, 204, 0.85)', height: '30px' }}>
      <div className="flex items-center gap-4">
        {/* CNCB Brand */}
        <a href="https://www.cncb.ac.cn" target="_blank" className="bigd-navbar-brand">
          <img 
            src="https://ngdc.cncb.ac.cn/cdn/image/cncb-nav.png" 
            className="bigd-img-responsive" 
            alt="CNCB"
            style={{ width: '60.87px', height: '20px' }}
          />
        </a>
        
        {/* NGDC Brand */}
        <a href="https://ngdc.cncb.ac.cn/" className="bigd-navbar-brand">
          <img 
            src="https://ngdc.cncb.ac.cn/cdn/image/ngdc-nav.png" 
            className="bigd-img-responsive" 
            alt="NGDC"
            style={{ width: '60.87px', height: '20px' }}
          />
        </a>
      </div>
      
      <div className="bigd-collapse bigd-navbar-collapse" id="bigd-coll">
        <ul className="bigd-nav bigd-navbar-nav bigd-navbar-right flex items-center space-x-1">
          <li>
        <Button
              className={`font-semibold text-xs px-3 py-1 ${textColor}`}
          variant="ghost"
        >
              <a href="https://ngdc.cncb.ac.cn/databases">Databases</a>
        </Button>
          </li>
          <li>
        <Button
              className={`font-semibold text-xs px-3 py-1 ${textColor}`}
          variant="ghost"
        >
              <a href="https://ngdc.cncb.ac.cn/tools">Tools</a>
        </Button>
          </li>
          <li>
        <Button
              className={`font-semibold text-xs px-3 py-1 ${textColor}`}
          variant="ghost"
        >
              <a href="https://ngdc.cncb.ac.cn/standards">Standards</a>
        </Button>
          </li>
          <li>
        <Button
              className={`font-semibold text-xs px-3 py-1 ${textColor}`}
          variant="ghost"
        >
              <a href="https://ngdc.cncb.ac.cn/publications">Publications</a>
        </Button>
          </li>
          <li>
        <Button
              className={`font-semibold text-xs px-3 py-1 ${textColor}`}
          variant="ghost"
        >
              <a href="https://ngdc.cncb.ac.cn/about">About</a>
        </Button>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default NGDC_header;
