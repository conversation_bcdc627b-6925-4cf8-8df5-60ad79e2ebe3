// 基因名称数据类型定义
export type GeneName = string;

// 导入唯一基因名称数据
import uniqueGeneNamesData from './unique_gene_names.json';

// 导出基因名称数组
export const uniqueGeneNames: GeneName[] = uniqueGeneNamesData;

// 导出基因名称集合（用于快速查找）
export const uniqueGeneNamesSet: Set<GeneName> = new Set(uniqueGeneNames);

// 工具函数：检查基因名称是否存在
export const isValidGeneName = (geneName: string): boolean => {
  return uniqueGeneNamesSet.has(geneName);
};

// 工具函数：获取基因名称总数
export const getGeneNamesCount = (): number => {
  return uniqueGeneNames.length;
};

// 工具函数：根据前缀搜索基因名称
export const searchGeneNamesByPrefix = (prefix: string, limit: number = 10): GeneName[] => {
  const lowerPrefix = prefix.toLowerCase();
  return uniqueGeneNames
    .filter(name => name.toLowerCase().startsWith(lowerPrefix))
    .slice(0, limit);
};

// 工具函数：模糊搜索基因名称
export const searchGeneNamesFuzzy = (query: string, limit: number = 10): GeneName[] => {
  const lowerQuery = query.toLowerCase();
  return uniqueGeneNames
    .filter(name => name.toLowerCase().includes(lowerQuery))
    .slice(0, limit);
};

// 导出统计信息
export const geneNamesStats = {
  total: uniqueGeneNames.length,
  firstTen: uniqueGeneNames.slice(0, 10),
  lastTen: uniqueGeneNames.slice(-10)
};

