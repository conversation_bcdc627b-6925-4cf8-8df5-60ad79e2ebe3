import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ation<PERSON>ontent,
  Pagin<PERSON><PERSON><PERSON>psis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/app/components/ui/pagination";

interface PaginationGroupProps {
  page: number;
  totalPages: number;
  setPage: (page: number) => void;
}

const PaginationGroup: React.FC<PaginationGroupProps> = ({ 
  page, 
  totalPages = 1,
  setPage 
}) => {
  const handlePrevious = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNext = () => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  };

  // Get visible page numbers - more compact version
  const getVisiblePages = () => {
    // If 5 or fewer pages, show all
    if (totalPages <= 5) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    
    // Always show first and last
    const pages = [1];
    
    // Show only current page and one page on either side
    let startMiddle = Math.max(2, page - 1);
    let endMiddle = Math.min(totalPages - 1, page + 1);
    
    // Handle case when current page is near start or end
    if (page <= 2) {
      endMiddle = 3;
    } else if (page >= totalPages - 1) {
      startMiddle = totalPages - 2;
    }
    
    // Add ellipsis if needed at the beginning
    if (startMiddle > 2) {
      pages.push(-1); // -1 represents an ellipsis
    }
    
    // Add the middle pages
    for (let i = startMiddle; i <= endMiddle; i++) {
      pages.push(i);
    }
    
    // Add ellipsis if needed at the end
    if (endMiddle < totalPages - 1) {
      pages.push(-2); // -2 represents an ellipsis (different key)
    }
    
    // Add the last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  return (
    <Pagination className="justify-end">
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious 
            href="#" 
            onClick={(e) => {
              e.preventDefault();
              handlePrevious();
            }}
            className={page <= 1 ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>

        {visiblePages.map((pageNum) => {
          // If it's an ellipsis
          if (pageNum < 0) {
            return (
              <PaginationItem key={`ellipsis-${pageNum}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }
          
          // Regular page number
          return (
            <PaginationItem key={pageNum}>
              <PaginationLink 
                href="#" 
                isActive={pageNum === page}
                onClick={(e) => {
                  e.preventDefault();
                  setPage(pageNum);
                }}
              >
                {pageNum}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        <PaginationItem>
          <PaginationNext 
            href="#" 
            onClick={(e) => {
              e.preventDefault();
              handleNext();
            }}
            className={page >= totalPages ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};

export default PaginationGroup; 