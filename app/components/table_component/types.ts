export interface FilterData {
  uniqueValues: {
    tissueOrCellTypes: string[];
    cellLines: string[];
    diseases: string[];
  };
  relationships: {
    tissueToCell: Record<string, string[]>;
    tissueToDisease: Record<string, string[]>;
    cellToTissue: Record<string, string[]>;
    cellToDisease: Record<string, string[]>;
    diseaseToTissue: Record<string, string[]>;
    diseaseToCell: Record<string, string[]>;
  };
  meta: {
    totalRecords: number;
    processedAt: string;
    version: string;
  };
}

export interface FilterState {
  tissueOrCellType: string[];
  cellLine: string[];
  disease: string[];
}

export interface FilterRelationManager {
  getFilteredOptions(
    filterType: 'tissueOrCellType' | 'cellLine' | 'disease',
    currentSelections: FilterState
  ): string[];
} 