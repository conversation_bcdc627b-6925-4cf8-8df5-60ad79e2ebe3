"use client";

import React from 'react';
import { Input } from "@/app/components/ui/input";

interface FilterDropdownProps {
  label: string;
  inputValue: string;
  dropdownValue: string;
  options: string[];
  onInputChange: (value: string) => void;
  onDropdownChange: (value: string) => void;
  placeholder: string;
  selectPlaceholder: string;
}

export function FilterDropdown({
  label,
  inputValue,
  dropdownValue,
  options,
  onInputChange,
  onDropdownChange,
  placeholder,
  selectPlaceholder
}: FilterDropdownProps) {
  return (
    <div className="flex items-center gap-4 mb-4">
      <div className="w-48 flex-shrink-0">
        <label className="block text-sm font-medium text-gray-700">{label}</label>
      </div>
      <div className="flex gap-4 w-full max-w-3xl">
        <Input
          placeholder={placeholder}
          value={inputValue}
          onChange={(e) => onInputChange(e.target.value)}
          disabled={!!dropdownValue}
          className="flex-1 min-w-0 w-[340px]"
        />
        <select
          value={dropdownValue}
          onChange={(e) => onDropdownChange(e.target.value)}
          disabled={!!inputValue}
          className="flex-1 min-w-0 w-[340px] px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">{selectPlaceholder}</option>
          {options.map(item => (
            <option key={item} value={item}>{item}</option>
          ))}
        </select>
      </div>
    </div>
  );
} 