import { useState } from "react";
import { ChevronDown } from "lucide-react";

interface PageSizeSelectProps {
  pageSize: number;
  setPageSize: (size: number) => void;
}

export default function PageSizeSelect({ pageSize, setPageSize }: PageSizeSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const options = ["10", "20", "50", "100"];

  return (
    <div className="relative inline-block">
      <div className="flex items-center gap-2">
        <span className="text-[16px] text-[#212121]">Show</span>
        <div 
          className="flex items-center px-3 py-2 border rounded-md cursor-pointer bg-white hover:bg-gray-50"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="min-w-[20px] text-center">{pageSize}</span>
          <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
        </div>
        <span className="text-[16px] text-[#212121]">entries</span>
      </div>
      
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white border rounded-md shadow-lg">
          {options.map((option) => (
            <div
              key={option}
              className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${
                pageSize.toString() === option ? "bg-gray-50 text-[#0071BC]" : ""
              }`}
              onClick={() => {
                setPageSize(parseInt(option, 10));
                setIsOpen(false);
              }}
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
