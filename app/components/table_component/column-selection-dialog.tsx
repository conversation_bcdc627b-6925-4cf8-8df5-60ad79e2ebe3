"use client";
import { useState } from "react";
import {
  Table as ReactTableInstance,
  Column,
} from "@tanstack/react-table";

import { Button } from "@/app/components/ui/button";
import { Checkbox } from "@/app/components/ui/checkbox";
import { Label } from "@/app/components/ui/label";
import { ChevronDown, ChevronUp } from "lucide-react";

interface SelectColumnsProps<TData> {
  table: ReactTableInstance<TData>;
  defaultColumns?: string[];
  columnLabels?: Record<string, string>;
  title?: string;
}

const SelectColumns = <TData,>({
  table,
  defaultColumns = ['projectId', 'bioProjectId', 'title', 'strategy', 'tissue'],
  columnLabels,
  title = "Display Columns",
}: SelectColumnsProps<TData>) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Group columns into 4 columns for display
  const allColumns = table.getAllColumns();
  const columnChunks: Column<TData, unknown>[][] = [];
  const chunkSize = Math.ceil(allColumns.length / 4);
  
  for (let i = 0; i < allColumns.length; i += chunkSize) {
    columnChunks.push(allColumns.slice(i, i + chunkSize));
  }

  return (
    <div className="w-full mb-0">
      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 text-[#0071BC] border-[#0071BC] hover:bg-blue-50"
        >
          {title}
          {isExpanded ? 
            <ChevronUp className="h-4 w-4" /> : 
            <ChevronDown className="h-4 w-4" />
          }
        </Button>
      </div>

      {isExpanded && (
        <div className="border rounded-lg p-6 bg-white mb-4 mt-2 w-full">
          <div className="flex gap-4 mb-6">
            <Button 
              variant="outline" 
              className="px-8 py-2 font-medium rounded-md"
              onClick={() => {
                allColumns.forEach(column => {
                  column.toggleVisibility(true);
                });
              }}
            >
              Select ALL
            </Button>
            <Button 
              variant="outline"
              className="px-8 py-2 font-medium rounded-md"
              onClick={() => {
                allColumns.forEach(column => {
                  column.toggleVisibility(defaultColumns.includes(column.id));
                });
              }}
            >
              Select Default
            </Button>
          </div>

          <div className="grid grid-cols-4 gap-6">
            {columnChunks.map((chunk, chunkIndex) => (
              <div key={chunkIndex} className="space-y-4">
                {chunk.map((column) => (
                  <div key={column.id} className="flex items-center space-x-3">
                  <Checkbox
                    id={column.id}
                    checked={column.getIsVisible()}
                    onCheckedChange={(checked: boolean) => column.toggleVisibility(!!checked)}
                    className="h-5 w-5"
                  />
                    <Label htmlFor={column.id} className="text-[#212121] text-base">
                      {columnLabels?.[column.id] || column.id}
                    </Label>
                  </div>
                ))}
                </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectColumns;
