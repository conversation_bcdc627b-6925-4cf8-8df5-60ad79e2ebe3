"use client";

import React from 'react';
import { Checkbox } from "@/app/components/ui/checkbox";
import { ChevronDown, ChevronUp } from 'lucide-react';

interface ExpandableFilterGroupProps {
  label: string;
  isExpanded: boolean;
  items: string[];
  selectedItems: Set<string>;
  onToggleExpand: () => void;
  onToggleItem: (item: string) => void;
}

export function ExpandableFilterGroup({
  label,
  isExpanded,
  items,
  selectedItems,
  onToggleExpand,
  onToggleItem
}: ExpandableFilterGroupProps) {
  return (
    <div className="border-t pt-4 w-full">
      <div 
        className="flex items-center justify-between cursor-pointer mb-2" 
        onClick={onToggleExpand}
      >
        <div className="flex items-center gap-2">
          <div className="w-40">
            <span className="text-sm font-medium text-gray-700">{label}</span>
          </div>
          <div className="text-xs text-gray-500">
            ({selectedItems.size} of {items.length} selected)
          </div>
        </div>
        <div className="text-gray-600 hover:text-gray-900 transition-colors">
          {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>
      </div>
      
      {isExpanded && (
        <div className="pl-40 mt-2">
          <div className="p-3 bg-gray-50 rounded-md border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-200">
              <span className="text-sm font-medium text-gray-700">Select options:</span>
              <div className="flex gap-3">
                <button 
                  className="text-xs text-blue-600 hover:text-blue-800"
                  onClick={(e) => {
                    e.stopPropagation();
                    items.forEach(item => {
                      if (!selectedItems.has(item)) {
                        onToggleItem(item);
                      }
                    });
                  }}
                >
                  Select All
                </button>
                <button 
                  className="text-xs text-blue-600 hover:text-blue-800"
                  onClick={(e) => {
                    e.stopPropagation();
                    items.forEach(item => {
                      if (selectedItems.has(item)) {
                        onToggleItem(item);
                      }
                    });
                  }}
                >
                  Clear All
                </button>
              </div>
            </div>
            <div className="max-h-56 overflow-y-auto pr-2">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {items.map(item => (
                  <div key={item} className="flex items-start gap-2 bg-white p-1.5 rounded border border-gray-100 hover:border-gray-300 transition-colors">
                    <Checkbox 
                      id={`item-${label.toLowerCase().replace(/\s+/g, '-')}-${item.replace(/\s+/g, '-')}`} 
                      checked={selectedItems.has(item)} 
                      onCheckedChange={() => onToggleItem(item)}
                      className="mt-0.5"
                    />
                    <label 
                      htmlFor={`item-${label.toLowerCase().replace(/\s+/g, '-')}-${item.replace(/\s+/g, '-')}`} 
                      className="text-xs leading-tight cursor-pointer text-gray-700"
                    >
                      {item}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 