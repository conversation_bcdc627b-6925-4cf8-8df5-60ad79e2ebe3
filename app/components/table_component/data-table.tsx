"use client";
import * as React from "react";
import {
  flexRender,
  Table as ReactTableInstance,
  Cell,
} from "@tanstack/react-table";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faDownload, faEllipsis } from "@fortawesome/free-solid-svg-icons";

interface DataTableProps<TData> {
  table: ReactTableInstance<TData>;
  pageSize?: number;
  setPageSize?: (size: number) => void;
  totalCount?: number;
  pageIndex?: number;
  pagination?: React.ReactNode;
}

export function DataTable<TData>({
  table,
  pageSize = 10,
  setPageSize,
  totalCount = 0,
  pageIndex = 0,
  pagination,
}: DataTableProps<TData>) {
  const tableContainerRef = React.useRef<HTMLDivElement>(null);
  const [expandedCells, setExpandedCells] = React.useState<Set<string>>(new Set());

  // 点击左滚动按钮
  const handleScrollLeft = () => {
    if (tableContainerRef.current) {
      tableContainerRef.current.scrollBy({
        left: -200,
        behavior: 'smooth',
      });
    }
  };

  // 点击右滚动按钮
  const handleScrollRight = () => {
    if (tableContainerRef.current) {
      tableContainerRef.current.scrollBy({
        left: 200,
        behavior: 'smooth',
      });
    }
  };

  // 下载表格数据为 JSON 文件
  const downloadJSON = () => {
    const allData = table.getRowModel().rows.map(row => {
      return row.getVisibleCells().reduce((acc: Record<string, unknown>, cell) => {
        const header = cell.column.columnDef.header;
        if (typeof header === 'string') {
          acc[header] = cell.getContext().getValue();
        } else {
          acc[cell.column.id] = cell.getContext().getValue();
        }
        return acc;
      }, {});
    });

    const json = JSON.stringify(allData, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'table-data.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  // 切换单元格内容展开
  const toggleCellExpansion = (cellId: string) => {
    setExpandedCells(prev => {
      const newSet = new Set(prev);
      if (newSet.has(cellId)) {
        newSet.delete(cellId);
      } else {
        newSet.add(cellId);
      }
      return newSet;
    });
  };

  // 渲染单元格内容
  const renderCellContent = (cell: Cell<TData, unknown>, cellId: string) => {
    const content = flexRender(cell.column.columnDef.cell, cell.getContext());
    
    // 如果内容不是字符串或者已经展开，直接返回
    if (typeof content !== 'string' || expandedCells.has(cellId)) {
      return content;
    }

    // 检查内容是否长
    const isLong = content.length > 100 || content.split('\n').length > 3;
    
    if (isLong) {
      return (
        <div>
          <div className="line-clamp-3 text-ellipsis overflow-hidden">{content}</div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleCellExpansion(cellId);
            }}
            className="text-[#337ab7] hover:text-[#1a3d5c] text-sm mt-1 flex items-center"
          >
            <FontAwesomeIcon icon={faEllipsis} className="mr-1" />
            Show more
          </button>
        </div>
      );
    }
    
    return content;
  };

  return (
    <div className="relative">
      {/* 表格控制栏 */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-4">
          <button
            className="bg-[#0071BC] text-white px-4 py-2 rounded-md text-sm hover:bg-[#005a9a]"
            onClick={handleScrollLeft}
          >
            {"<"}
          </button>
          
          {setPageSize && (
            <div className="flex items-center gap-2">
              <span className="text-[16px] text-[#212121]">Show</span>
              <select
                value={pageSize}
                onChange={e => setPageSize(Number(e.target.value))}
                className="border rounded-md px-3 py-1 bg-white"
              >
                {[10, 20, 50, 100].map(size => (
                  <option key={size} value={size}>{size}</option>
                ))}
              </select>
              <span className="text-[16px] text-[#212121]">entries</span>
            </div>
          )}
          
          <button
            className="bg-[#0071BC] text-white px-4 py-2 rounded-md text-sm hover:bg-[#005a9a]"
            onClick={downloadJSON}
          >
            <FontAwesomeIcon icon={faDownload} className="mr-2" /> Download
          </button>
          
          {totalCount > 0 && (
            <div className="text-[16px] text-[#212121]">
              Showing {pageIndex * pageSize + 1} to {Math.min(
                (pageIndex + 1) * pageSize,
                totalCount
              )} of {totalCount} entries
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {pagination}
          <button
            className="bg-[#0071BC] text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
            onClick={handleScrollRight}
          >
            {">"}
          </button>
        </div>
      </div>

      {/* 表格容器 - 使用正确的方式实现粘性表头 */}
      <div className="border-t border-gray-200 overflow-hidden">
        <div 
          ref={tableContainerRef}
          className="overflow-auto"
          style={{ maxHeight: '800px' }}
        >
          <table className="w-full divide-y divide-gray-200 border-collapse">
            <thead className="bg-white">
              <tr>
                {table.getHeaderGroups().map(headerGroup =>
                  headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      className="sticky top-0 px-6 py-4 text-center text-[14px] font-medium text-[#212121] uppercase tracking-wider cursor-pointer border-b whitespace-nowrap bg-white shadow-sm z-10"
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {!header.isPlaceholder &&
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </th>
                  ))
                )}
              </tr>
            </thead>
            
            <tbody className="bg-white divide-y divide-gray-200">
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <tr key={row.id} className="hover:bg-gray-50">
                    {row.getVisibleCells().map(cell => (
                      <td
                        key={cell.id}
                        className="px-6 py-4 text-center text-[16px] text-[#212121] overflow-hidden text-ellipsis"
                      >
                        {renderCellContent(cell, `${row.id}-${cell.id}`)}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={table.getAllColumns().length}
                    className="px-6 py-4 text-center text-[16px] text-[#212121]"
                  >
                    No records found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}