"use client";

import React from 'react';
import { Checkbox } from "@/app/components/ui/checkbox";
import { Eye } from 'lucide-react';

interface ColumnVisibilityToggleProps {
  columns: { id: string; label: string; checked: boolean }[];
  onToggle: (column: string, state: boolean) => void;
}

export function ColumnVisibilityToggle({ columns, onToggle }: ColumnVisibilityToggleProps) {
  return (
    <div className="flex flex-col bg-white rounded-md border border-gray-200 p-4 shadow-sm">
      <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-100">
        <Eye size={18} className="text-[#0071BC]" />
        <h3 className="font-medium text-gray-700">Column Visibility</h3>
      </div>
      <div className="flex gap-6">
        {columns.map((column) => (
          <div key={column.id} className="flex items-center space-x-2">
            <Checkbox
              id={`column-${column.id}`}
              checked={column.checked}
              onCheckedChange={(checked) => onToggle(column.id, checked === true)}
              className="rounded-sm"
            />
            <label
              htmlFor={`column-${column.id}`}
              className="text-sm font-medium leading-none cursor-pointer transition-colors hover:text-[#0071BC]"
            >
              {column.label}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
} 