import filterData from './filterData.json';
import { FilterData, FilterState, FilterRelationManager } from './types';

export class StaticFilterManager implements FilterRelationManager {
  private data: FilterData;

  constructor() {
    this.data = filterData as FilterData;
  }

  /**
   * 获取所有唯一值
   */
  public getAllUniqueValues() {
    return this.data.uniqueValues;
  }

  /**
   * 根据当前选择状态获取可用的筛选选项
   */
  public getFilteredOptions(
    filterType: 'tissueOrCellType' | 'cellLine' | 'disease',
    currentSelections: FilterState
  ): string[] {
    const { tissueOrCellType, cellLine, disease } = currentSelections;

    switch (filterType) {
      case 'tissueOrCellType':
        return this.getAvailableTissueOrCellTypes(cellLine, disease);
      
      case 'cellLine':
        return this.getAvailableCellLines(tissueOrCellType, disease);
      
      case 'disease':
        return this.getAvailableDiseases(tissueOrCellType, cellLine);
      
      default:
        return [];
    }
  }

  /**
   * 获取可用的组织/细胞类型
   */
  private getAvailableTissueOrCellTypes(cellLineFilter: string[], diseaseFilter: string[]): string[] {
    let availableOptions = new Set(this.data.uniqueValues.tissueOrCellTypes);

    // 根据已选择的细胞系过滤
    if (cellLineFilter.length > 0) {
      const tissuesFromCells = new Set<string>();
      cellLineFilter.forEach(cell => {
        const relatedTissues = this.data.relationships.cellToTissue[cell] || [];
        relatedTissues.forEach(tissue => tissuesFromCells.add(tissue));
      });
      availableOptions = new Set([...availableOptions].filter(tissue => tissuesFromCells.has(tissue)));
    }

    // 根据已选择的疾病过滤
    if (diseaseFilter.length > 0) {
      const tissuesFromDiseases = new Set<string>();
      diseaseFilter.forEach(disease => {
        const relatedTissues = this.data.relationships.diseaseToTissue[disease] || [];
        relatedTissues.forEach(tissue => tissuesFromDiseases.add(tissue));
      });
      availableOptions = new Set([...availableOptions].filter(tissue => tissuesFromDiseases.has(tissue)));
    }

    return Array.from(availableOptions).sort();
  }

  /**
   * 获取可用的细胞系
   */
  private getAvailableCellLines(tissueFilter: string[], diseaseFilter: string[]): string[] {
    let availableOptions = new Set(this.data.uniqueValues.cellLines);

    // 根据已选择的组织/细胞类型过滤
    if (tissueFilter.length > 0) {
      const cellsFromTissues = new Set<string>();
      tissueFilter.forEach(tissue => {
        const relatedCells = this.data.relationships.tissueToCell[tissue] || [];
        relatedCells.forEach(cell => cellsFromTissues.add(cell));
      });
      availableOptions = new Set([...availableOptions].filter(cell => cellsFromTissues.has(cell)));
    }

    // 根据已选择的疾病过滤
    if (diseaseFilter.length > 0) {
      const cellsFromDiseases = new Set<string>();
      diseaseFilter.forEach(disease => {
        const relatedCells = this.data.relationships.diseaseToCell[disease] || [];
        relatedCells.forEach(cell => cellsFromDiseases.add(cell));
      });
      availableOptions = new Set([...availableOptions].filter(cell => cellsFromDiseases.has(cell)));
    }

    return Array.from(availableOptions).sort();
  }

  /**
   * 获取可用的疾病
   */
  private getAvailableDiseases(tissueFilter: string[], cellLineFilter: string[]): string[] {
    let availableOptions = new Set(this.data.uniqueValues.diseases);

    // 根据已选择的组织/细胞类型过滤
    if (tissueFilter.length > 0) {
      const diseasesFromTissues = new Set<string>();
      tissueFilter.forEach(tissue => {
        const relatedDiseases = this.data.relationships.tissueToDisease[tissue] || [];
        relatedDiseases.forEach(disease => diseasesFromTissues.add(disease));
      });
      availableOptions = new Set([...availableOptions].filter(disease => diseasesFromTissues.has(disease)));
    }

    // 根据已选择的细胞系过滤
    if (cellLineFilter.length > 0) {
      const diseasesFromCells = new Set<string>();
      cellLineFilter.forEach(cell => {
        const relatedDiseases = this.data.relationships.cellToDisease[cell] || [];
        relatedDiseases.forEach(disease => diseasesFromCells.add(disease));
      });
      availableOptions = new Set([...availableOptions].filter(disease => diseasesFromCells.has(disease)));
    }

    return Array.from(availableOptions).sort();
  }

  /**
   * 获取元数据信息
   */
  public getMetaInfo() {
    return this.data.meta;
  }


} 