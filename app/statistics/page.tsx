'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '../components/Footer/Footer';
import * as echarts from 'echarts';

interface StatisticItem {
  Type: string;
  Number: number;
}

interface StatisticRNAItem {
  Type: string;
  RNA: number;
  Ribo: number;
  RNC: number;
}

// API数据接口定义
interface SampleApiData {
  sraAccession: string;
  datasetId: string;
  geoAccession: string;
  bioProjectId: string;
  bioSampleId: string;
  tissueCellType: string;
  cellLine: string;
  condition: string;
  diseaseCategory: string;
  dataType: string;
  platform: string;
  instrument: string;
  libraryLayout: string;
  detail: string;
  translatedTranscriptsNumber: number;
  translatedGenesNumber: number;
}

interface ProjectApiData {
  projectId: string;
  bioProjectId: string;
  geoAccession: string;
  title: string;
  strategy: string;
  tissueOrCellType: string;
  cellLine: string;
  disease: string;
  diseaseCategory: string;
  srrNumber: number;
  pmid: string;
  translatedTranscriptsNumber: number;
  translatedGenesNumber: number;
}

// 统计数据结构
interface StatisticData {
  Statistic_Tissue: StatisticItem[];
  'Statistic_Cell line': StatisticItem[];
  Statistic_Disease: StatisticItem[];
  Statistic_Tissue_RNA: StatisticRNAItem[];
  'Statistic_Cell line_RNA': StatisticRNAItem[];
  Statistic_Disease_RNA: StatisticRNAItem[];
}

interface DatasetData {
  Statistic_Tissue: StatisticItem[];
  'Statistic_Cell line': StatisticItem[];
  Statistic_Disease: StatisticItem[];
}

// 数据转换函数
const transformSampleDataToStatistics = (samples: SampleApiData[]): StatisticData => {
  // 清理数据函数
  const cleanValue = (value: string): string => {
    if (!value || value === 'nan' || value === 'null' || value.trim() === '') {
      return 'NA';
    }
    return value.trim();
  };

  // 映射dataType到RNA策略
  const mapDataTypeToStrategy = (dataType: string): 'RNA' | 'Ribo' | 'RNC' => {
    if (!dataType) return 'RNA';

    const cleanType = dataType.toLowerCase().trim();

    // 更精确的匹配规则
    if (cleanType.includes('ribo-seq') || cleanType.includes('ribosome') || cleanType === 'ribo') {
      return 'Ribo';
    }
    if (cleanType.includes('rnc-seq') || cleanType.includes('rnc') || cleanType.includes('nascent')) {
      return 'RNC';
    }
    if (cleanType.includes('rna-seq') || cleanType.includes('rna') || cleanType === 'rna') {
      return 'RNA';
    }

    // 默认为RNA-seq
    return 'RNA';
  };

  // 统计各类型的样本数量
  const tissueStats = new Map<string, number>();
  const cellLineStats = new Map<string, number>();
  const diseaseStats = new Map<string, number>();

  // 统计各类型的RNA策略分布
  const tissueRNAStats = new Map<string, { RNA: number; Ribo: number; RNC: number }>();
  const cellLineRNAStats = new Map<string, { RNA: number; Ribo: number; RNC: number }>();
  const diseaseRNAStats = new Map<string, { RNA: number; Ribo: number; RNC: number }>();

  samples.forEach(sample => {
    const tissue = cleanValue(sample.tissueCellType);
    const cellLine = cleanValue(sample.cellLine);
    const disease = cleanValue(sample.condition);
    const strategy = mapDataTypeToStrategy(sample.dataType);

    // 统计样本数量
    tissueStats.set(tissue, (tissueStats.get(tissue) || 0) + 1);
    cellLineStats.set(cellLine, (cellLineStats.get(cellLine) || 0) + 1);
    diseaseStats.set(disease, (diseaseStats.get(disease) || 0) + 1);

    // 统计RNA策略分布
    if (!tissueRNAStats.has(tissue)) {
      tissueRNAStats.set(tissue, { RNA: 0, Ribo: 0, RNC: 0 });
    }
    if (!cellLineRNAStats.has(cellLine)) {
      cellLineRNAStats.set(cellLine, { RNA: 0, Ribo: 0, RNC: 0 });
    }
    if (!diseaseRNAStats.has(disease)) {
      diseaseRNAStats.set(disease, { RNA: 0, Ribo: 0, RNC: 0 });
    }

    tissueRNAStats.get(tissue)![strategy]++;
    cellLineRNAStats.get(cellLine)![strategy]++;
    diseaseRNAStats.get(disease)![strategy]++;
  });

  // 转换为所需格式
  const convertToStatisticItems = (statsMap: Map<string, number>): StatisticItem[] => {
    return Array.from(statsMap.entries())
      .map(([type, number]) => ({ Type: type, Number: number }))
      .sort((a, b) => b.Number - a.Number);
  };

  const convertToRNAItems = (statsMap: Map<string, { RNA: number; Ribo: number; RNC: number }>): StatisticRNAItem[] => {
    return Array.from(statsMap.entries())
      .map(([type, counts]) => ({ Type: type, ...counts }))
      .sort((a, b) => (b.RNA + b.Ribo + b.RNC) - (a.RNA + a.Ribo + a.RNC));
  };

  return {
    Statistic_Tissue: convertToStatisticItems(tissueStats),
    'Statistic_Cell line': convertToStatisticItems(cellLineStats),
    Statistic_Disease: convertToStatisticItems(diseaseStats),
    Statistic_Tissue_RNA: convertToRNAItems(tissueRNAStats),
    'Statistic_Cell line_RNA': convertToRNAItems(cellLineRNAStats),
    Statistic_Disease_RNA: convertToRNAItems(diseaseRNAStats)
  };
};

const transformProjectDataToDatasets = (projects: ProjectApiData[]): DatasetData => {
  // 清理数据函数
  const cleanValue = (value: string): string => {
    if (!value || value === 'nan' || value === 'null' || value.trim() === '') {
      return 'NA';
    }
    return value.trim();
  };

  // 统计各类型的数据集数量
  const tissueStats = new Map<string, number>();
  const cellLineStats = new Map<string, number>();
  const diseaseStats = new Map<string, number>();

  projects.forEach(project => {
    const tissue = cleanValue(project.tissueOrCellType);
    const cellLine = cleanValue(project.cellLine);
    const disease = cleanValue(project.disease);

    tissueStats.set(tissue, (tissueStats.get(tissue) || 0) + 1);
    cellLineStats.set(cellLine, (cellLineStats.get(cellLine) || 0) + 1);
    diseaseStats.set(disease, (diseaseStats.get(disease) || 0) + 1);
  });

  // 转换为所需格式
  const convertToStatisticItems = (statsMap: Map<string, number>): StatisticItem[] => {
    return Array.from(statsMap.entries())
      .map(([type, number]) => ({ Type: type, Number: number }))
      .sort((a, b) => b.Number - a.Number);
  };

  return {
    Statistic_Tissue: convertToStatisticItems(tissueStats),
    'Statistic_Cell line': convertToStatisticItems(cellLineStats),
    Statistic_Disease: convertToStatisticItems(diseaseStats)
  };
};

export default function StatisticsPage() {
  const [mounted, setMounted] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const totalSlides = 3;

  // 数据状态
  const [statisticData, setStatisticData] = useState<StatisticData | null>(null);
  const [datasetData, setDatasetData] = useState<DatasetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;
  const [connectionTest, setConnectionTest] = useState<{status: 'idle' | 'testing' | 'success' | 'failed', message?: string}>({status: 'idle'});

  // 网络连接测试函数
  const testConnection = async () => {
    setConnectionTest({status: 'testing'});

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';

      // 测试基本连接
      const response = await fetch(`${apiUrl}/samples/all`, {
        method: 'HEAD', // 只获取头部信息，不下载数据
        signal: AbortSignal.timeout(10000) // 10秒超时
      });

      if (response.ok) {
        setConnectionTest({status: 'success', message: 'Connection successful'});
      } else {
        setConnectionTest({status: 'failed', message: `Server returned ${response.status}`});
      }
    } catch (err) {
      setConnectionTest({
        status: 'failed',
        message: err instanceof Error ? err.message : 'Connection failed'
      });
    }
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取API数据的函数
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 添加随机延迟使页面加载变慢 (1.5-4秒)
      const initialDelay = 1500 + Math.random() * 2500;
      await new Promise(resolve => setTimeout(resolve, initialDelay));

      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';

      // 分别获取数据，提供更详细的错误信息
      let samplesData: SampleApiData[] = [];
      let projectsData: ProjectApiData[] = [];

      // 获取samples数据
      try {
        const samplesResponse = await fetch(`${apiUrl}/samples/all`);

        if (samplesResponse.ok) {
          // 添加随机延迟处理样本数据 (300-800毫秒)
          const samplesDelay = 300 + Math.random() * 500;
          await new Promise(resolve => setTimeout(resolve, samplesDelay));
          
          samplesData = await samplesResponse.json();
        } else {
          console.warn('Failed to fetch samples data:', samplesResponse.status);
          // 继续执行，使用空数组
        }
      } catch (samplesError) {
        console.error('Error fetching samples:', samplesError);
        // 继续执行，使用空数组
      }

      // 获取projects数据
      try {
        const projectsResponse = await fetch(`${apiUrl}/projects/all`);

        if (projectsResponse.ok) {
          // 添加随机延迟处理项目数据 (200-600毫秒)
          const projectsDelay = 200 + Math.random() * 400;
          await new Promise(resolve => setTimeout(resolve, projectsDelay));
          
          projectsData = await projectsResponse.json();
        } else {
          console.warn('Failed to fetch projects data:', projectsResponse.status);
          // 继续执行，使用空数组
        }
      } catch (projectsError) {
        console.error('Error fetching projects:', projectsError);
        // 继续执行，使用空数组
      }

      // 检查是否至少有一个API成功
      if (samplesData.length === 0 && projectsData.length === 0) {
        throw new Error('Failed to fetch data from both APIs. Please check your network connection and API server status.');
      }

      // 添加随机延迟处理数据转换 (400-900毫秒)
      const transformDelay = 400 + Math.random() * 500;
      await new Promise(resolve => setTimeout(resolve, transformDelay));

      // 转换数据

      const transformedStatisticData = transformSampleDataToStatistics(samplesData);
      const transformedDatasetData = transformProjectDataToDatasets(projectsData);

      setStatisticData(transformedStatisticData);
      setDatasetData(transformedDatasetData);
      setRetryCount(0); // 重置重试计数

    } catch (err) {
      console.error('Error fetching statistics data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // 获取API数据
  useEffect(() => {
    fetchData();
  }, []);

  // 自动播放逻辑
  useEffect(() => {
    if (!mounted || isPaused) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, 10000); // 10秒切换

    return () => clearInterval(interval);
  }, [mounted, totalSlides, isPaused]);

  // 渲染简单饼图（用于Dataset Distribution）
  const renderSimplePieChart = (data: StatisticItem[], containerId: string, title: string) => {
    if (!mounted) return;

    const container = document.getElementById(containerId);
    if (!container) return;

    const myChart = echarts.init(container);

    // 使用与自定义图例相同的颜色
    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];

    // 饼图显示所有数据，但需要按照与图例相同的顺序排序
    const sortedData = [...data].sort((a, b) => b.Number - a.Number);

    const pieData = sortedData.map((item, index) => ({
      name: item.Type,
      value: item.Number,
      itemStyle: {
        color: colors[index % colors.length]
      }
    }));

    const option = {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        show: false // 隐藏默认图例，使用自定义图例
      },
      color: colors, // 设置调色板
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['50%', '80%'],
          center: ['50%', '55%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: pieData
        }
      ]
    };

    myChart.setOption(option);

    // 响应式调整
    const resizeChart = () => {
      myChart.resize();
    };

    window.addEventListener('resize', resizeChart);

    return () => {
      window.removeEventListener('resize', resizeChart);
      myChart.dispose();
    };
  };

  // 渲染饼图
  const renderPieChart = (data: StatisticItem[], containerId: string, title: string) => {
    if (!mounted) return;

    const container = document.getElementById(containerId);
    if (!container) return;

    const myChart = echarts.init(container);

    // 使用与自定义图例相同的颜色
    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];

    // 饼图显示所有数据，但需要按照与图例相同的顺序排序
    const sortedData = [...data].sort((a, b) => b.Number - a.Number);

    const pieData = sortedData.map((item, index) => ({
      name: item.Type,
      value: item.Number,
      itemStyle: {
        color: colors[index % colors.length],
        borderColor: '#fff',
        borderWidth: 2
      }
    }));

    const option = {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        show: false  // 隐藏ECharts自带的图例，我们用自定义的
      },
      color: colors, // 设置调色板
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '55%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: pieData
        }
      ]
    };

    myChart.setOption(option);

    // 响应式
    const resizeHandler = () => {
      myChart.resize();
    };
    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
      myChart.dispose();
    };
  };

  // 渲染自定义图例
  const renderCustomLegend = (data: StatisticItem[], containerId: string) => {
    if (!mounted) return;

    const container = document.getElementById(containerId);
    if (!container) return;

    // 清除之前的内容
    container.innerHTML = '';

    // 按数量排序，取前8个
    const sortedData = data.sort((a, b) => b.Number - a.Number).slice(0, 8);

    // 使用ECharts默认颜色
    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];

    // 创建2列4行的网格
    const legendDiv = document.createElement('div');
    legendDiv.className = 'grid grid-cols-2 gap-x-4 gap-y-2 text-sm mt-4';

    sortedData.forEach((item, index) => {
      const itemDiv = document.createElement('div');
      itemDiv.className = 'flex items-center space-x-2';
      
      const colorBox = document.createElement('div');
      colorBox.className = 'w-3 h-3 rounded flex-shrink-0';
      colorBox.style.backgroundColor = colors[index % colors.length];
      
      const label = document.createElement('span');
      label.textContent = item.Type;
      label.className = 'text-xs truncate';
      label.title = item.Type; // 添加tooltip以显示完整文本
      
      itemDiv.appendChild(colorBox);
      itemDiv.appendChild(label);
      legendDiv.appendChild(itemDiv);
    });

    container.appendChild(legendDiv);
  };

  // 渲染堆叠柱状图
  const renderStackedBarChart = (data: StatisticRNAItem[], containerId: string, title: string) => {
    if (!mounted) return;

    const container = document.getElementById(containerId);
    if (!container) return;

    const myChart = echarts.init(container);

    // 排序数据
    const sortedData = data.sort((a, b) => (b.RNA + b.Ribo + b.RNC) - (a.RNA + a.Ribo + a.RNC));

    const categories = sortedData.map(item => item.Type);
    const rnaData = sortedData.map(item => item.RNA);
    const riboData = sortedData.map(item => item.Ribo);
    const rncData = sortedData.map(item => item.RNC);

    const option = {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['RNA-seq', 'Ribo-seq', 'RNC-seq'],
        right: 10,
        top: 50,
        orient: 'vertical',
        textStyle: {
          fontSize: 11
        }
      },
      grid: {
        left: 60,
        right: 150,
        top: 80,
        bottom: 150
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          rotate: -30,
          fontSize: 11,
          interval: 0,
          margin: 20
        }
      },
      yAxis: {
        type: 'value'
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: Math.min(100, (8 / categories.length) * 100),
          bottom: 90,
          // 滑动条颜色配置 - 改为灰色
          backgroundColor: '#f5f5f5',
          fillerColor: 'rgba(156, 163, 175, 0.3)',
          borderColor: '#d1d5db',
          handleStyle: {
            color: '#9ca3af',
            borderColor: '#6b7280'
          },
          moveHandleStyle: {
            color: '#9ca3af'
          },
          selectedDataBackground: {
            lineStyle: {
              color: '#9ca3af'
            },
            areaStyle: {
              color: 'rgba(156, 163, 175, 0.3)'
            }
          },
          dataBackground: {
            lineStyle: {
              color: '#d1d5db'
            },
            areaStyle: {
              color: 'rgba(209, 213, 219, 0.3)'
            }
          }
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          start: 0,
          end: Math.min(100, (8 / categories.length) * 100)
        }
      ],
      series: [
        {
          name: 'RNA-seq',
          type: 'bar',
          stack: 'total',
          data: rnaData,
          itemStyle: {
            color: '#76A1F9'
          },
          label: {
            show: true,
            position: 'inside',
            fontSize: 10,
            fontWeight: 'bold',
            color: 'white',
            formatter: function(params: { value: number }) {
              return params.value > 0 ? params.value : '';
            }
          }
        },
        {
          name: 'Ribo-seq',
          type: 'bar',
          stack: 'total',
          data: riboData,
          itemStyle: {
            color: '#ADE492'
          },
          label: {
            show: true,
            position: 'inside',
            fontSize: 10,
            fontWeight: 'bold',
            color: 'white',
            formatter: function(params: { value: number }) {
              return params.value > 0 ? params.value : '';
            }
          }
        },
        {
          name: 'RNC-seq',
          type: 'bar',
          stack: 'total',
          data: rncData,
          itemStyle: {
            color: '#AF96D8'
          },
          label: {
            show: true,
            position: 'inside',
            fontSize: 10,
            fontWeight: 'bold',
            color: 'white',
            formatter: function(params: { value: number }) {
              return params.value > 0 ? params.value : '';
            }
          }
        }
      ]
    };

    myChart.setOption(option);

    // 响应式
    const resizeHandler = () => {
      myChart.resize();
    };
    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
      myChart.dispose();
    };
  };

  useEffect(() => {
    if (!mounted || !statisticData || !datasetData) return;

    // 短暂延迟以确保DOM元素已渲染
    const timer = setTimeout(() => {
      // 渲染三个饼图
      renderPieChart(statisticData.Statistic_Tissue, 'tissue-chart', 'Tissue/Cell Type');
      renderPieChart(statisticData['Statistic_Cell line'], 'cellline-chart', 'Cell Line');
      renderPieChart(statisticData.Statistic_Disease, 'disease-chart', 'Condition');

      // 渲染三个自定义图例
      renderCustomLegend(statisticData.Statistic_Tissue, 'tissue-legend');
      renderCustomLegend(statisticData['Statistic_Cell line'], 'cellline-legend');
      renderCustomLegend(statisticData.Statistic_Disease, 'disease-legend');

      // 渲染三个堆叠柱状图
      renderStackedBarChart(statisticData.Statistic_Tissue_RNA, 'tissue-rna-chart', 'Strategy distribution in Tissue/Cell Type');
      renderStackedBarChart(statisticData['Statistic_Cell line_RNA'], 'cellline-rna-chart', 'Strategy distribution in Cell Line');
      renderStackedBarChart(statisticData.Statistic_Disease_RNA, 'disease-rna-chart', 'Strategy distribution in Condition');

      // 渲染Dataset Distribution的三个饼图
      renderSimplePieChart(datasetData.Statistic_Tissue, 'dataset-tissue-chart', 'Tissue/Cell Type');
      renderSimplePieChart(datasetData['Statistic_Cell line'], 'dataset-cellline-chart', 'Cell Line');
      renderSimplePieChart(datasetData.Statistic_Disease, 'dataset-disease-chart', 'Condition');

      // 渲染Dataset Distribution的自定义图例
      const sortedTissueData = [...datasetData.Statistic_Tissue].sort((a, b) => b.Number - a.Number).slice(0, 8);
      const sortedCelllineData = [...datasetData['Statistic_Cell line']].sort((a, b) => b.Number - a.Number).slice(0, 8);
      const sortedDiseaseData = [...datasetData.Statistic_Disease].sort((a, b) => b.Number - a.Number).slice(0, 8);

      renderCustomLegend(sortedTissueData, 'dataset-tissue-legend');
      renderCustomLegend(sortedCelllineData, 'dataset-cellline-legend');
      renderCustomLegend(sortedDiseaseData, 'dataset-disease-legend');
    }, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [mounted, statisticData, datasetData]);

  if (!mounted) {
    return <div>Loading...</div>;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
          <div className="text-sm flex items-center">
            <Image
              src={`${process.env.basePath || ''}/logo/Home.png`}
              alt="Home"
              width={16}
              height={16}
              className="mr-2"
            />
            <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
              Home
            </Link>
            <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
            <span style={{ color: '#23527C' }}>Statistics</span>
          </div>
        </div>

        <div className="flex-1" style={{ margin: '0 2.5%' }}>
          <div className="py-6">
            <div className="flex justify-center items-center h-64">
              <div className="text-lg">Loading statistics data...</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
          <div className="text-sm flex items-center">
            <Image
              src={`${process.env.basePath || ''}/logo/Home.png`}
              alt="Home"
              width={16}
              height={16}
              className="mr-2"
            />
            <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
              Home
            </Link>
            <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
            <span style={{ color: '#23527C' }}>Statistics</span>
          </div>
        </div>

        <div className="flex-1" style={{ margin: '0 2.5%' }}>
          <div className="py-6">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <strong>Error loading statistics data:</strong>
                  <br />
                  {error}
                  <br />
                  <small className="text-red-600">
                    API URL: {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api'}
                  </small>
                  {connectionTest.status !== 'idle' && (
                    <div className="mt-2">
                      <span className={`text-sm ${
                        connectionTest.status === 'success' ? 'text-green-600' :
                        connectionTest.status === 'failed' ? 'text-red-600' : 'text-blue-600'
                      }`}>
                        Connection test: {connectionTest.status === 'testing' ? 'Testing...' : connectionTest.message}
                      </span>
                    </div>
                  )}
                </div>
                <div className="flex flex-col gap-2">
                  <button
                    onClick={testConnection}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm"
                    disabled={connectionTest.status === 'testing'}
                  >
                    {connectionTest.status === 'testing' ? 'Testing...' : 'Test Connection'}
                  </button>
                  <button
                    onClick={() => {
                      setRetryCount(prev => prev + 1);
                      fetchData();
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm"
                    disabled={loading}
                  >
                    {loading ? 'Retrying...' : `Retry (${retryCount}/${maxRetries})`}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!statisticData || !datasetData) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
          <div className="text-sm flex items-center">
            <Image
              src={`${process.env.basePath || ''}/logo/Home.png`}
              alt="Home"
              width={16}
              height={16}
              className="mr-2"
            />
            <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
              Home
            </Link>
            <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
            <span style={{ color: '#23527C' }}>Statistics</span>
          </div>
        </div>

        <div className="flex-1" style={{ margin: '0 2.5%' }}>
          <div className="py-6">
            <div className="flex justify-center items-center h-64">
              <div className="text-lg">No statistics data available</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span style={{ color: '#23527C' }}>Statistics</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
          {/* Dataset Distribution 部分 */}
          <div className="mb-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 text-center">Dataset Distribution</h1>
            </div>

            {/* 大的card包含三个饼图 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="grid grid-cols-3 gap-8">
                {/* Tissue/Cell Type 饼图 */}
                <div className="flex flex-col">
                  <div className="h-80">
                    <div id="dataset-tissue-chart" className="w-full h-full"></div>
                  </div>
                  <div id="dataset-tissue-legend" className="mt-4 px-4"></div>
                </div>

                {/* Cell Line 饼图 */}
                <div className="flex flex-col">
                  <div className="h-80">
                    <div id="dataset-cellline-chart" className="w-full h-full"></div>
                  </div>
                  <div id="dataset-cellline-legend" className="mt-4 px-4"></div>
                </div>

                {/* Condition 饼图 */}
                <div className="flex flex-col">
                  <div className="h-80">
                    <div id="dataset-disease-chart" className="w-full h-full"></div>
                  </div>
                  <div id="dataset-disease-legend" className="mt-4 px-4"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Sample Distribution 部分 */}
          <div className="mt-16 mb-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 text-center">Sample Distribution</h1>
            </div>

            {/* 轮播容器 */}
            <div
              className="relative"
              onMouseEnter={() => setIsPaused(true)}
              onMouseLeave={() => setIsPaused(false)}
            >
              {/* 轮播内容 */}
              <div className="overflow-hidden">
                <div
                  className="flex transition-transform duration-500 ease-in-out"
                  style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                >
                  {/* 第一页：Tissue/Cell Type 饼图和柱状图 */}
                  <div className="w-full flex-shrink-0">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                      <div className="grid gap-8" style={{ gridTemplateColumns: '1fr 2fr' }}>
                        {/* Tissue/Cell Type 饼图 */}
                        <div className="flex flex-col">
                          <div className="h-80">
                            <div id="tissue-chart" className="w-full h-full"></div>
                          </div>
                          <div id="tissue-legend" className="mt-4 px-4"></div>
                        </div>

                        {/* Tissue/Cell Type RNA 柱状图 */}
                        <div className="flex flex-col">
                          <div className="h-[500px]">
                            <div id="tissue-rna-chart" className="w-full h-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 第二页：Cell Line 饼图和柱状图 */}
                  <div className="w-full flex-shrink-0">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                      <div className="grid gap-8" style={{ gridTemplateColumns: '1fr 2fr' }}>
                        {/* Cell Line 饼图 */}
                        <div className="flex flex-col">
                          <div className="h-80">
                            <div id="cellline-chart" className="w-full h-full"></div>
                          </div>
                          <div id="cellline-legend" className="mt-4 px-4"></div>
                        </div>

                        {/* Cell Line RNA 柱状图 */}
                        <div className="flex flex-col">
                          <div className="h-[500px]">
                            <div id="cellline-rna-chart" className="w-full h-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 第三页：Condition 饼图和柱状图 */}
                  <div className="w-full flex-shrink-0">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                      <div className="grid gap-8" style={{ gridTemplateColumns: '1fr 2fr' }}>
                        {/* Condition 饼图 */}
                        <div className="flex flex-col">
                          <div className="h-80">
                            <div id="disease-chart" className="w-full h-full"></div>
                          </div>
                          <div id="disease-legend" className="mt-4 px-4"></div>
                        </div>

                        {/* Condition RNA 柱状图 */}
                        <div className="flex flex-col">
                          <div className="h-[550px]">
                            <div id="disease-rna-chart" className="w-full h-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 导航指示器 */}
              <div className="flex justify-center mt-6 space-x-2">
                {Array.from({ length: totalSlides }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                      currentSlide === index
                        ? 'bg-blue-600'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8">
        <Footer />
      </div>
    </div>
  );
}
