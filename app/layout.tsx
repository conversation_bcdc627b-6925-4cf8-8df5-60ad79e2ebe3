import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "@/app/globals.css";
import NGDC_header from "@/app/components/NGDC/NGDC_header";
import NavBar from "@/app/components/NavBar/NavBar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "TEDD Translation Efficiency Dynamic Database",
  description: "Translation Efficiency Dynamic Database",
  icons: {
    icon: `${process.env.basePath || ''}/logo/TEDDB.png`, // 相对于 public 路径
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <NGDC_header />
        <NavBar />
        <main className="min-h-screen">
          {children}
        </main>
      </body>
    </html>
  );
}
