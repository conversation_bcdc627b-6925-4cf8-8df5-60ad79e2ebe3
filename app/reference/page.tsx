'use client';

import { useState, useEffect } from 'react';
// import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Download, ChevronDown } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '../components/Footer/Footer';

// Define reference data type
interface ReferenceItem {
  bioProjectId: string;
  geoAccession: string;
  reference: string;
  pubmedId: string;
  doi: string;
}

export default function ReferencePage() {
  // 状态管理
  const [allData, setAllData] = useState<ReferenceItem[]>([]);
  const [filteredData, setFilteredData] = useState<ReferenceItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchKeyword, setSearchKeyword] = useState<string>("");
  const [sortField, setSortField] = useState<string>('bioProjectId');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [showDownloadOptions, setShowDownloadOptions] = useState<boolean>(false);
  
  // 计算总页数
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = filteredData.slice(startIndex, endIndex);

  // 加载所有数据
  const fetchAllData = async () => {
    try {
      setLoading(true);
      
      // 添加随机延迟使页面加载变慢 (1-3.2秒)
      const initialDelay = 1000 + Math.random() * 2200;
      await new Promise(resolve => setTimeout(resolve, initialDelay));
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/references/all`);
      
      if (!response.ok) {
        throw new Error(`API returned error: ${response.status}`);
      }
      
      // 添加随机延迟处理响应数据 (400-1000毫秒)
      const responseDelay = 400 + Math.random() * 600;
      await new Promise(resolve => setTimeout(resolve, responseDelay));
      
      const result = await response.json();
      
      // 再添加随机延迟处理数据 (200-700毫秒)
      const dataDelay = 200 + Math.random() * 500;
      await new Promise(resolve => setTimeout(resolve, dataDelay));
      
      setAllData(result);
      setFilteredData(result);
      setError(null);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchAllData();
  }, []);

  // 搜索功能
  useEffect(() => {
    if (!searchKeyword.trim()) {
      setFilteredData(allData);
    } else {
      const filtered = allData.filter(item => 
        Object.values(item).some(value => 
          value?.toString().toLowerCase().includes(searchKeyword.toLowerCase())
        )
      );
      setFilteredData(filtered);
    }
    setCurrentPage(1); // 重置到第一页
  }, [searchKeyword, allData]);

  // 排序功能
  useEffect(() => {
    const sorted = [...filteredData].sort((a, b) => {
      const aValue = a[sortField as keyof ReferenceItem] || '';
      const bValue = b[sortField as keyof ReferenceItem] || '';
      
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });
    setFilteredData(sorted);
  }, [sortField, sortDirection]);

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 下载功能
  const downloadData = (format: 'csv' | 'xlsx' | 'json') => {
    const dataToDownload = filteredData.length > 0 ? filteredData : allData;
    
    if (format === 'json') {
      const jsonData = JSON.stringify(dataToDownload, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'references.json';
      a.click();
      URL.revokeObjectURL(url);
    } else if (format === 'csv') {
      // 生成CSV
      const headers = ['BioProject ID', 'GEO Accession', 'Reference', 'PubMed ID', 'DOI'];
      const csvContent = [
        headers.join(','),
        ...dataToDownload.map(item => [
          `"${item.bioProjectId || ''}"`,
          `"${item.geoAccession || ''}"`,
          `"${(item.reference || '').replace(/"/g, '""')}"`,
          `"${item.pubmedId || ''}"`,
          `"${item.doi || ''}"`
        ].join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'references.csv';
      a.click();
      URL.revokeObjectURL(url);
    } else if (format === 'xlsx') {
      // 为XLSX格式，我们先生成CSV格式，实际项目中建议使用专门的库如xlsx
      const headers = ['BioProject ID', 'GEO Accession', 'Reference', 'PubMed ID', 'DOI'];
      const csvContent = [
        headers.join('\t'),
        ...dataToDownload.map(item => [
          item.bioProjectId || '',
          item.geoAccession || '',
          (item.reference || '').replace(/\t/g, ' '),
          item.pubmedId || '',
          item.doi || ''
        ].join('\t'))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'application/vnd.ms-excel' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'references.xlsx';
      a.click();
      URL.revokeObjectURL(url);
    }
    
    setShowDownloadOptions(false);
  };

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <div className="flex flex-col ml-1 text-xs leading-none">
          <span className="text-gray-300 mb-0.5">▲</span>
          <span className="text-gray-300">▼</span>
        </div>
      );
    }
    
    return (
      <div className="flex flex-col ml-1 text-xs leading-none">
        <span className={`${sortDirection === 'asc' ? 'text-[#337ab7]' : 'text-gray-300'} mb-0.5`}>▲</span>
        <span className={`${sortDirection === 'desc' ? 'text-[#337ab7]' : 'text-gray-300'}`}>▼</span>
      </div>
    );
  };
  
  // 生成分页按钮
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (currentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setCurrentPage(currentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous
        </button>
      );
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            currentPage === i 
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-blue-700' 
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
        </button>
      );
    }

    // Next button
    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setCurrentPage(currentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center mt-6">{pages}</div>;
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
            />
          <Link href="/" className="link-breadcrumb">
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span className="link-breadcrumb-current">Reference</span>
        </div>
      </div>
      
      {/* 主内容区域 - 左右各2.5%空白 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
        {/* 单行布局：数据统计、搜索框和下载按钮 */}
        <div className="flex justify-between items-center mb-6">
          {/* 左侧：数据统计 */}
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-800">
              {loading ? 'Loading...' : `${filteredData.length} Reference${filteredData.length === 1 ? '' : 's'} Found`}
            </h1>
          </div>
          
          {/* 右侧：搜索框和下载按钮 */}
          <div className="flex items-center space-x-4">
            {/* 搜索框 */}
            <div className="flex">
              <Input
                placeholder="Search"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="h-10 w-80 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
              />
              {/* <Button className="rounded-l-none bg-[#0071BC] hover:bg-blue-700 h-10">
                <Search className="h-4 w-4" />
              </Button> */}
            </div>
            
            {/* 下载按钮 */}
            <div className="relative">
              <button
                onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
              >
                <Download className="h-4 w-4 mr-2" />
                <ChevronDown className="h-4 w-4 ml-2" />
              </button>
              
              {/* 下载选项菜单 */}
              {showDownloadOptions && (
                <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="py-1">
                    <button
                      onClick={() => downloadData('csv')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                    >
                      Download as CSV
                    </button>
                    <button
                      onClick={() => downloadData('json')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                    >
                      Download as JSON
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Table container */}
        <div className="bg-white rounded-lg overflow-hidden">
        {loading ? (
          <div className="p-6 text-center">
            <p className="text-gray-600">Loading data...</p>
          </div>
        ) : error ? (
          <div className="p-6 border-red-500 border-l-4">
            <p className="text-red-600">Error: {error}</p>
          </div>
          ) : (
            <>
              {/* Table */}
              <div 
                className="overflow-x-auto border border-gray-200 rounded-lg table-scroll"
                style={{ 
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#cbd5e1 #f1f5f9'
                }}
              >
                <table className="w-full min-w-[1000px]">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('bioProjectId')}
                      >
                        <div className="flex items-center justify-center">
                          <span>BioProject ID</span>
                          {renderSortIcon('bioProjectId')}
                        </div>
                      </th>
                      <th
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('geoAccession')}
                      >
                        <div className="flex items-center justify-center">
                          <span>GEO Accession</span>
                          {renderSortIcon('geoAccession')}
                        </div>
                      </th>
                      <th
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 max-w-xs"
                        onClick={() => handleSort('reference')}
                      >
                        <div className="flex items-center justify-center">
                          <span>Reference</span>
                          {renderSortIcon('reference')}
                        </div>
                      </th>
                      <th
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('pubmedId')}
                      >
                        <div className="flex items-center justify-center">
                          <span>PMID</span>
                          {renderSortIcon('pubmedId')}
                        </div>
                      </th>
                      <th
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('doi')}
                      >
                        <div className="flex items-center justify-center">
                          <span>DOI</span>
                          {renderSortIcon('doi')}
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentData.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-center">
                          {item.bioProjectId && item.bioProjectId !== 'nan' && item.bioProjectId !== 'null' ? (
                            <a
                              href={`https://www.ncbi.nlm.nih.gov/bioproject/${item.bioProjectId}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="link-research"
                            >
                              {item.bioProjectId}
                            </a>
                          ) : (
                            "NA"
                          )}
                        </td>
                        <td className="px-4 py-3 text-sm text-center">
                          {item.geoAccession && item.geoAccession !== 'nan' && item.geoAccession !== 'null' ? (
                            <a
                              href={`https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=${item.geoAccession}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="link-research"
                            >
                              {item.geoAccession}
                            </a>
                          ) : (
                            "NA"
                          )}
                        </td>
                        <td className="px-4 py-3 text-sm text-center max-w-xs">
                          <div className="text-wrap break-words">
                            {item.reference && item.reference !== 'nan' && item.reference !== 'null' ? item.reference : "NA"}
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-center">
                          {item.pubmedId && item.pubmedId !== 'nan' && item.pubmedId !== 'null' ? (
                            <a
                              href={`https://pubmed.ncbi.nlm.nih.gov/${item.pubmedId}/`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="link-research"
                            >
                              {item.pubmedId}
                            </a>
                          ) : (
                            "NA"
                          )}
                        </td>
                        <td className="px-4 py-3 text-sm text-center">
                          {item.doi && item.doi !== 'nan' && item.doi !== 'null' ? (
                            <a
                              href={item.doi}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="link-research break-all"
                            >
                              {item.doi}
                            </a>
                          ) : (
                            "NA"
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination and page size controls */}
              <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <span className="text-sm font-medium text-gray-700">
                      Showing {startIndex + 1} to {Math.min(endIndex, filteredData.length)} of {filteredData.length} rows
                    </span>
                    <div className="flex items-center space-x-2">
                      <label className="text-sm font-medium text-gray-700">
                        Show
                        <select
                          value={pageSize}
                          onChange={(e) => {
                            setPageSize(Number(e.target.value));
                            setCurrentPage(1);
                          }}
                          className="ml-2 mr-2 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#0071BC] focus:border-transparent"
                        >
                          <option value={10}>10</option>
                          <option value={25}>25</option>
                          <option value={50}>50</option>
                          <option value={100}>100</option>
                        </select>
                        entries
                      </label>
                    </div>
                  </div>
                  
                  {renderPagination()}
                </div>
          </div>
            </>
        )}
      </div>
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-4">
        <Footer />
      </div>
      
      {/* 点击其他地方关闭下载菜单 */}
      {showDownloadOptions && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowDownloadOptions(false)}
        />
      )}
    </div>
  );
} 