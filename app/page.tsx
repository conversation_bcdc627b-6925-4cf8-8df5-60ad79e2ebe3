"use client";

import Image from "next/image";
import Link from "next/link";
import Footer from "@/app/components/Footer/Footer";
import { useEffect, useState, useRef } from "react";
import * as echarts from 'echarts';

// ===== 新增统计图组件（柱状图/饼图轮播） =====
const DataStatisticsChart = () => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const data = [
    { label: 'RNA-seq', value: 726 },
    { label: 'Ribo-seq', value: 738 },
    { label: 'RNC-seq', value: 54 }
  ];

  // 切换图表类型
  useEffect(() => {
    // 清除旧定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      setChartType(prev => (prev === 'pie' ? 'bar' : 'pie'));
    }, 10000); // 每 10 秒切换

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [chartType]);

  // 渲染图表
  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const colors = ['#76A1F9', '#ADE492', '#AF96D8'];

    const renderPie = () => {
      chart.setOption({
        tooltip: { trigger: 'item' },
        legend: { show: false },
        series: [
          {
            name: 'Data Statistics',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            label: {
              show: true,
              formatter: '{b}: {c}'
            },
            data: data.map((d, idx) => ({ value: d.value, name: d.label, itemStyle: { color: colors[idx] } }))
          }
        ]
      });
    };

    const renderBar = () => {
      chart.setOption({
        tooltip: { trigger: 'axis' },
        grid: { left: 40, right: 20, top: 30, bottom: 30 },
        xAxis: {
          type: 'category',
          data: data.map(d => d.label),
          axisLabel: { color: '#333' }
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#333' }
        },
        series: [
          {
            type: 'bar',
            data: data.map((d, idx) => ({ value: d.value, itemStyle: { color: colors[idx] } })),
            barWidth: '40%'
          }
        ]
      });
    };

    if (chartType === 'pie') {
      renderPie();
    } else {
      renderBar();
    }

    const resizeHandler = () => chart.resize();
    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
      chart.dispose();
    };
  }, [chartType]);

  return (
    <div className="flex flex-col items-center w-full h-full">
      <div ref={chartRef} className="w-full h-full" />
      {/* 切换控制点 */}
      <div className="flex space-x-2 mt-2">
        {(['pie', 'bar'] as const).map((type) => (
          <button
            key={type}
            onClick={() => setChartType(type)}
            className={`w-3 h-3 rounded-full transition-colors ${chartType === type ? 'bg-[#0071BC]' : 'bg-gray-300 hover:bg-gray-400'}`}
          />
        ))}
      </div>
    </div>
  );
};

// ===== 结束新增组件 =====

export default function Home() {
  const cardData = [
    {
      title: "Datasets",
      count: "279",
      image: `${process.env.basePath || ''}/logo/Project.png`,
      link: "/browse/dataset",
    },
    {
      title: "Samples",
      count: "1,518",
      image: `${process.env.basePath || ''}/logo/Sample.png`,
      link: "/browse/sample",
    },
    {
      title: "Genes",
      count: "23,976",
      image: `${process.env.basePath || ''}/logo/Gene.png`,
      link: "/browse/gene",
    },
    {
      title: "Tissue/Cell Types",
      count: "24",
      image: `${process.env.basePath || ''}/logo/Tissue.png`,
      link: "#",
    },
    {
      title: "Cell Lines",
      count: "74",
      image: `${process.env.basePath || ''}/logo/Cell Line.png`,
      link: "#",
    },
    {
      title: "Conditions",
      count: "52",
      image: `${process.env.basePath || ''}/logo/Disease.png`,
      link: "#",
    },
  ];

  return (
    <div className="bg-gray-50">
      {/* Main Content */}
      <div
        className="w-full"
        style={{ paddingLeft: "2.5%", paddingRight: "2.5%" }}
      >
        <div className="py-8">
          {/* Top Section - Overview and Data Statistics */}
          <div className="flex gap-6 mb-6" style={{ minHeight: "40px" }}>
            {/* Left Side - Overview (70% width) */}
            <div className="flex flex-col" style={{ width: "70%" }}>
              {/* Overview Header - 固定高度 */}
              {/* <div 
                className="text-white text-center py-4 mb-6 rounded-lg flex-shrink-0"
                style={{ backgroundColor: 'rgba(0, 102, 204, 0.85)', height: '64px' }}
              >
                <h1 className="text-2xl font-bold">Overview</h1>
              </div> */}

              {/* Six Cards Grid - 固定高度 */}
              <div
                className="grid grid-cols-3 gap-4"
                style={{ height: "320px" }}
              >
                {cardData.map((card, index) => (
                  <Link key={index} href={card.link}>
                    <div className="bg-white rounded-lg border border-gray-200 p-2 text-center hover:shadow-lg transition-shadow cursor-pointer flex justify-center h-full">
                      <Image
                        src={card.image}
                        alt={card.title}
                        width={120}
                        height={120}
                        className="object-contain"
                      />
                      <div className="flex flex-col px-2 ml-2 justify-center min-w-[130px]">
                        <p className="text-3xl font-black group-hover:text-[#157bc6] mb-1">
                          {card.count}
                        </p>
                        <h3 className="text-[14px] text-gray-500 tracking-wider group-hover:text-[#157bc6]">
                          {card.title}
                        </h3>
                      </div>
                      {/* <div className="flex justify-center mb-2">
                        <Image
                          src={card.image}
                          alt={card.title}
                          width={120}
                          height={120}
                          className="object-contain"
                        />
                      </div>
                      <div className="text-lg font-bold text-gray-800 mb-1">
                        {card.count}
                      </div>
                      <div className="text-sm font-semibold text-gray-700">
                        {card.title}
                      </div> */}
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Right Side - Statistics and News (30% width) */}
            <div
              className="flex flex-col"
              style={{ width: "30%", height: "320px" }}
            >
              {/* Data Statistics - 占65%高度 */}
              <div
                className="bg-white rounded-lg border border-gray-200 flex-shrink-0"
                style={{ height: "100%", marginBottom: "24px" }}
              >
                <div
                  className="text-white pl-4 py-3 rounded-t-lg"
                  style={{ backgroundColor: "rgba(0, 102, 204, 0.85)" }}
                >
                  <h2 className="text-[14px] font-bold">Data Statistics</h2>
                </div>
                <div
                  className="p-4 flex flex-col justify-center"
                  style={{ height: "calc(100% - 64px)" }}
                >
                  <DataStatisticsChart />
                </div>
              </div>

              {/* News and Update - 占剩余空间 */}
              {/* <div className="bg-white rounded-lg border border-gray-200 flex-1">
                <div
                  className="text-white pl-4 py-3 rounded-t-lg"
                  style={{ backgroundColor: "rgba(0, 102, 204, 0.85)" }}
                >
                  <h2 className="text-[14px] font-bold">News and Update</h2>
                </div>
                <div
                  className="p-4 flex flex-col justify-center"
                  style={{ height: "calc(100% - 64px)" }}
                >
                  <p className="text-gray-600 text-sm text-center">
                    Latest updates and news will be displayed here.
                  </p>
                </div>
              </div> */}
            </div>
          </div>

          {/* Bottom Section - Citation and Contact (Full Width) */}
          <div className="flex gap-6">
            
            

            <div className="bg-white rounded-lg border border-gray-200" style={{ width: "70%" }}>
              <div
                className="text-white pl-4 py-3 rounded-t-lg"
                style={{ backgroundColor: "rgba(0, 102, 204, 0.85)" }}
              >
                <h2 className="text-[14px] font-bold">Citation and Contact</h2>
              </div>
              <div className="p-4">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-[14px] text-gray-700">
                      If you have any questions or would like to offer
                      suggestions, comments, or report bugs, please feel free to
                      contact us.
                    </p>
                    <p className="text-[14px]">
                      <span className="font-bold">Email:</span>{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="link-research"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200" style={{ width: "30%" }}>
              <div
                className="text-white pl-4 py-3 rounded-t-lg"
                style={{ backgroundColor: "rgba(0, 102, 204, 0.85)" }}
              >
                <h2 className="text-[14px] font-bold">News and Update</h2>
                
              </div>
              <div
                className="p-4 flex flex-col justify-center"
                style={{ height: "calc(100% - 64px)" }}
              >
                <p className="text-gray-600 text-[14px] text-left">
                <span className="font-bold">2025-06-15</span> Translation Efficiency Dynamics Database (TEDD) version 1.0 was online.
                </p>
              </div>
            </div>
          </div>

        </div>
      </div>

      {/* Footer */}
      <div className="mt-4">
        <Footer />
      </div>
    </div>
  );
}
