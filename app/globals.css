@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 隐藏原生滚动条，使用自定义滚动条 */
.table-scroll {
  /* 隐藏 Webkit 浏览器的滚动条 */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE */
}

.table-scroll::-webkit-scrollbar {
  display: none; /* Webkit 浏览器 */
}

/* 确保表格内容不会被滚动条遮挡 */
.table-scroll table {
  margin-bottom: 0;
}

/* 自定义滚动条样式 */
#custom-scrollbar-thumb:active {
  cursor: grabbing !important;
}

/* 防止文本选择 */
#custom-scrollbar-track {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 统一按钮样式 */
/* Submit按钮基础样式 */
.btn-submit {
  @apply bg-[#0071BC] hover:bg-[#2B7FFF] text-white px-6 py-2 h-10 rounded-md font-medium transition-colors;
}

/* Submit按钮禁用状态 */
.btn-submit:disabled {
  @apply bg-gray-400 cursor-not-allowed;
}

/* Submit按钮加载状态 */
.btn-submit.loading {
  @apply bg-gray-400 cursor-not-allowed;
}

/* Reset按钮基础样式 */
.btn-reset {
  @apply border border-gray-300 text-gray-700 hover:text-black rounded-md px-6 py-2 h-10 font-medium hover:bg-[#F9FAFB] transition-colors;
}

/* Submit按钮宽版本 (用于search页面) */
.btn-submit-wide {
  @apply bg-[#0071BC] hover:bg-[#2B7FFF] text-white px-8 py-2 h-10 rounded-md font-medium transition-colors;
}

/* Submit按钮占满宽度版本 (用于某些browse页面) */
.btn-submit-full {
  @apply flex-1 bg-[#0071BC] hover:bg-[#2B7FFF] text-white h-10 font-medium transition-colors;
}

/* 统一超链接样式 */
/* 主要链接样式 - 科研蓝调 */
.link-research {
  @apply text-[#337ab7] hover:underline transition-colors;
}

/* 面包屑导航链接样式 */
.link-breadcrumb {
  @apply hover:underline font-bold text-black transition-colors;
}

/* 当前页样式 */
.link-breadcrumb-current {
  @apply text-[#337ab7];
}

/* 增强悬停效果的链接样式 */
.link-hover-enhanced {
  @apply text-[#337ab7] hover:text-[#2B7FFF] hover:underline transition-all duration-200;
}
