// 样本数据类型
export interface Sample {
  sraAccession: string;
  datasetId: string;
  geoAccession: string;
  bioProjectId: string;
  bioSampleId: string;
  tissueCellType: string;
  cellLine: string;
  condition: string;
  diseaseCategory: string;
  dataType: string;
  platform: string;
  instrument: string;
  libraryLayout: string;
  detail: string;
  translatedTranscriptsNumber: number;
  translatedGenesNumber: number;
}

// 过滤器相关类型
export interface SampleFilterCounts {
  tissueTypes: Record<string, number>;
  cellLines: Record<string, number>;
  diseases: Record<string, number>;
  dataTypes: Record<string, number>;
}

export interface SampleFilterStates {
  selectedTissueTypes: string[];
  selectedCellLines: string[];
  selectedDiseases: string[];
  selectedDataTypes: string[];
  showMoreTissue: boolean;
  showMoreCellLine: boolean;
  showMoreDisease: boolean;
  showMoreDataType: boolean;
}

export class SampleFilterManager {
  // 计算过滤器计数
  static calculateFilterCounts(data: Sample[], currentFilters: SampleFilterStates): SampleFilterCounts {
    let workingData = data;

    // 根据当前选择的过滤器筛选数据
    if (currentFilters.selectedTissueTypes.length > 0) {
      workingData = workingData.filter(item => 
        currentFilters.selectedTissueTypes.includes(this.normalizeTissueType(item.tissueCellType))
      );
    }

    if (currentFilters.selectedCellLines.length > 0) {
      workingData = workingData.filter(item => 
        currentFilters.selectedCellLines.includes(this.normalizeCellLine(item.cellLine))
      );
    }

    if (currentFilters.selectedDiseases.length > 0) {
      workingData = workingData.filter(item => 
        currentFilters.selectedDiseases.includes(this.normalizeDisease(item.condition))
      );
    }

    if (currentFilters.selectedDataTypes.length > 0) {
      workingData = workingData.filter(item => 
        currentFilters.selectedDataTypes.includes(this.normalizeDataType(item.dataType))
      );
    }

    const counts: SampleFilterCounts = {
      tissueTypes: {},
      cellLines: {},
      diseases: {},
      dataTypes: {}
    };

    workingData.forEach(item => {
      const tissue = this.normalizeTissueType(item.tissueCellType);
      const cellLine = this.normalizeCellLine(item.cellLine);
      const disease = this.normalizeDisease(item.condition);
      const dataType = this.normalizeDataType(item.dataType);

      counts.tissueTypes[tissue] = (counts.tissueTypes[tissue] || 0) + 1;
      counts.cellLines[cellLine] = (counts.cellLines[cellLine] || 0) + 1;
      counts.diseases[disease] = (counts.diseases[disease] || 0) + 1;
      counts.dataTypes[dataType] = (counts.dataTypes[dataType] || 0) + 1;
    });

    return counts;
  }

  // 应用过滤器
  static applyFilters(
    allData: Sample[], 
    searchKeyword: string, 
    filterStates: SampleFilterStates
  ): Sample[] {
    let filtered = allData;

    // 文本搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(item => 
        Object.values(item).some(value => 
          value && value.toString().toLowerCase().includes(keyword)
        )
      );
    }

    // 组织/细胞类型过滤
    if (filterStates.selectedTissueTypes.length > 0) {
      filtered = filtered.filter(item => 
        filterStates.selectedTissueTypes.includes(this.normalizeTissueType(item.tissueCellType))
      );
    }

    // 细胞系过滤
    if (filterStates.selectedCellLines.length > 0) {
      filtered = filtered.filter(item => 
        filterStates.selectedCellLines.includes(this.normalizeCellLine(item.cellLine))
      );
    }

    // 疾病/条件过滤
    if (filterStates.selectedDiseases.length > 0) {
      filtered = filtered.filter(item => 
        filterStates.selectedDiseases.includes(this.normalizeDisease(item.condition))
      );
    }

    // 数据类型过滤
    if (filterStates.selectedDataTypes.length > 0) {
      filtered = filtered.filter(item => 
        filterStates.selectedDataTypes.includes(this.normalizeDataType(item.dataType))
      );
    }

    return filtered;
  }

  // 规范化组织类型
  private static normalizeTissueType(tissue: string): string {
    if (!tissue || tissue === 'nan' || tissue === 'null') return 'NA';
    return tissue.trim();
  }

  // 规范化细胞系
  private static normalizeCellLine(cellLine: string): string {
    if (!cellLine || cellLine === 'nan' || cellLine === 'null') return 'NA';
    return cellLine.trim();
  }

  // 规范化疾病/条件
  private static normalizeDisease(disease: string): string {
    if (!disease || disease === 'nan' || disease === 'null') return 'NA';
    return disease.trim();
  }

  // 规范化数据类型
  private static normalizeDataType(dataType: string): string {
    if (!dataType || dataType === 'nan' || dataType === 'null') return 'NA';
    return dataType.trim();
  }

  // 创建初始过滤器状态
  static createInitialFilterStates(): SampleFilterStates {
    return {
      selectedTissueTypes: [],
      selectedCellLines: [],
      selectedDiseases: [],
      selectedDataTypes: [],
      showMoreTissue: false,
      showMoreCellLine: false,
      showMoreDisease: false,
      showMoreDataType: false
    };
  }

  // 重置过滤器状态
  static resetFilterStates(): SampleFilterStates {
    return this.createInitialFilterStates();
  }
} 