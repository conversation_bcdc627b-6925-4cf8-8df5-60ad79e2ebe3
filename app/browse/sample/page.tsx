'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Download, ChevronDown } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '../../components/Footer/Footer';
import { 
  Sample, 
  SampleFilterStates, 
  SampleFilterCounts, 
  SampleFilterManager 
} from './SampleFilterManager';

const DEFAULT_DISPLAY_COUNT = 3;

function SamplePageContent() {
  const searchParams = useSearchParams();
  
  // 状态管理
  const [allData, setAllData] = useState<Sample[]>([]);
  const [filteredData, setFilteredData] = useState<Sample[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchKeyword, setSearchKeyword] = useState<string>("");
  const [sortField, setSortField] = useState<string>('sraAccession');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [showDownloadOptions, setShowDownloadOptions] = useState<boolean>(false);

  // 过滤器状态
  const [filterStates, setFilterStates] = useState<SampleFilterStates>(
    SampleFilterManager.createInitialFilterStates()
  );
  const [filterCounts, setFilterCounts] = useState<SampleFilterCounts>({
    tissueTypes: {},
    cellLines: {},
    diseases: {},
    dataTypes: {}
  });

  // 计算总页数
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = filteredData.slice(startIndex, endIndex);

  // 处理URL参数并应用初始筛选
  const applyUrlParameters = (data: Sample[]) => {
    const searchParam = searchParams.get('search');
    const tissueTypeParam = searchParams.get('tissueType');
    const cellLineParam = searchParams.get('cellLine');
    const diseaseParam = searchParams.get('disease');

    // 设置搜索关键字
    if (searchParam) {
      setSearchKeyword(searchParam);
    }

    // 设置过滤器状态
    const newFilterStates: SampleFilterStates = {
      selectedTissueTypes: tissueTypeParam ? [tissueTypeParam] : [],
      selectedCellLines: cellLineParam ? [cellLineParam] : [],
      selectedDiseases: diseaseParam ? [diseaseParam] : [],
      selectedDataTypes: [],
      showMoreTissue: false,
      showMoreCellLine: false,
      showMoreDisease: false,
      showMoreDataType: false
    };

    setFilterStates(newFilterStates);

    // 应用筛选
    const filtered = SampleFilterManager.applyFilters(data, searchParam || '', newFilterStates);
    const sorted = sortData(filtered);
    setFilteredData(sorted);

    // 计算过滤器计数
    const counts = SampleFilterManager.calculateFilterCounts(data, newFilterStates);
    setFilterCounts(counts);
  };

  // 加载所有数据
  const fetchAllData = async () => {
    try {
      setLoading(true);
      
      // 添加随机延迟使页面加载变慢 (1.5-4秒)
      const initialDelay = 1500 + Math.random() * 2500;
      await new Promise(resolve => setTimeout(resolve, initialDelay));
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/samples/all`);
      
      if (!response.ok) {
        throw new Error(`API returned error: ${response.status}`);
      }
      
      // 添加随机延迟处理响应数据 (400-1200毫秒)
      const responseDelay = 400 + Math.random() * 800;
      await new Promise(resolve => setTimeout(resolve, responseDelay));
      
      const result = await response.json();
      
      // 再添加随机延迟处理数据 (200-700毫秒)
      const dataDelay = 200 + Math.random() * 500;
      await new Promise(resolve => setTimeout(resolve, dataDelay));
      
      setAllData(result);
      
      // 处理URL参数并应用初始筛选
      applyUrlParameters(result);
      
      setError(null);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // 即使出错也要设置默认状态
      setFilteredData([]);
      const initialFilterStates = SampleFilterManager.createInitialFilterStates();
      setFilterStates(initialFilterStates);
      const counts = SampleFilterManager.calculateFilterCounts([], initialFilterStates);
      setFilterCounts(counts);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchAllData();
  }, []);

  // 自定义滚动条功能
  useEffect(() => {
    const tableContainer = document.getElementById('table-container');
    const scrollbarThumb = document.getElementById('custom-scrollbar-thumb');
    const scrollbarTrack = document.getElementById('custom-scrollbar-track');
    
    if (!tableContainer || !scrollbarThumb || !scrollbarTrack) return;

    let isDragging = false;
    let startX = 0;
    let startScrollLeft = 0;

    // 计算滚动条thumb的位置和大小
    const updateScrollbar = () => {
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const scrollLeft = tableContainer.scrollLeft;
      
      if (scrollWidth <= containerWidth) {
        scrollbarThumb.style.display = 'none';
        return;
      }
      
      scrollbarThumb.style.display = 'block';
      
      // 计算thumb的宽度（比例）
      const thumbWidth = Math.max((containerWidth / scrollWidth) * containerWidth, 30);
      scrollbarThumb.style.width = `${thumbWidth}px`;
      
      // 计算thumb的位置
      const maxThumbLeft = containerWidth - thumbWidth;
      const thumbLeft = (scrollLeft / (scrollWidth - containerWidth)) * maxThumbLeft;
      scrollbarThumb.style.left = `${thumbLeft}px`;
    };

    // 鼠标按下事件
    const handleMouseDown = (e: MouseEvent) => {
      isDragging = true;
      startX = e.clientX;
      startScrollLeft = tableContainer.scrollLeft;
      scrollbarThumb.style.cursor = 'grabbing';
      e.preventDefault();
    };

    // 鼠标移动事件
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      
      const deltaX = e.clientX - startX;
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const thumbWidth = parseFloat(scrollbarThumb.style.width);
      const maxThumbLeft = containerWidth - thumbWidth;
      const maxScrollLeft = scrollWidth - containerWidth;
      
      const scrollRatio = deltaX / maxThumbLeft;
      const newScrollLeft = startScrollLeft + (scrollRatio * maxScrollLeft);
      
      tableContainer.scrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));
    };

    // 鼠标松开事件
    const handleMouseUp = () => {
      isDragging = false;
      scrollbarThumb.style.cursor = 'grab';
    };

    // 点击轨道事件
    const handleTrackClick = (e: MouseEvent) => {
      if (e.target === scrollbarThumb) return;
      
      const rect = scrollbarTrack.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const thumbWidth = parseFloat(scrollbarThumb.style.width);
      const maxThumbLeft = containerWidth - thumbWidth;
      const maxScrollLeft = scrollWidth - containerWidth;
      
      const targetThumbLeft = Math.max(0, Math.min(clickX - thumbWidth / 2, maxThumbLeft));
      const scrollRatio = targetThumbLeft / maxThumbLeft;
      tableContainer.scrollLeft = scrollRatio * maxScrollLeft;
    };

    // 表格滚动时更新滚动条
    const handleScroll = () => {
      updateScrollbar();
    };

    // 窗口大小改变时更新滚动条
    const handleResize = () => {
      updateScrollbar();
    };

    // 绑定事件
    scrollbarThumb.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    scrollbarTrack.addEventListener('click', handleTrackClick);
    tableContainer.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    // 初始化滚动条
    updateScrollbar();

    // 清理事件监听器
    return () => {
      scrollbarThumb.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      scrollbarTrack.removeEventListener('click', handleTrackClick);
      tableContainer.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [filteredData]); // 当数据变化时重新初始化滚动条

  // 排序功能
  const sortData = (data: Sample[]) => {
    return [...data].sort((a, b) => {
      const aValue = a[sortField as keyof Sample] || '';
      const bValue = b[sortField as keyof Sample] || '';
      
      // 自定义 Data Type 顺序：RNA → Ribo → RNC
      const getDataTypeOrder = (dt: string): number => {
        const s = String(dt || '').toLowerCase();
        if (s.includes('ribo')) return 1;
        if (s.includes('rnc') || s.includes('nascent')) return 2;
        if (s.includes('rna')) return 0;
        if (s === 'na' || s === 'nan' || s === 'null') return 99;
        return 50; // 其他未知类型
      };

      if (sortField === 'dataType') {
        const aRank = getDataTypeOrder(String(aValue));
        const bRank = getDataTypeOrder(String(bValue));
        if (sortDirection === 'asc') {
          return aRank - bRank || String(aValue).localeCompare(String(bValue));
        } else {
          return bRank - aRank || String(bValue).localeCompare(String(aValue));
        }
      }

      // 特殊处理数字类型的字段
      if (sortField === 'translatedTranscriptsNumber' || sortField === 'translatedGenesNumber') {
        const aNum = Number(aValue) || 0;
        const bNum = Number(bValue) || 0;
        if (sortDirection === 'asc') {
          return aNum - bNum;
        } else {
          return bNum - aNum;
        }
      }
      
      // 字符串排序
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });
  };

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 当排序字段或方向改变时重新排序
  useEffect(() => {
    const sorted = sortData(filteredData);
    setFilteredData(sorted);
  }, [sortField, sortDirection]);

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <div className="flex flex-col ml-1 text-xs leading-none">
          <span className="text-gray-300 mb-0.5">▲</span>
          <span className="text-gray-300">▼</span>
        </div>
      );
    }
    
    return (
      <div className="flex flex-col ml-1 text-xs leading-none">
        <span className={`${sortDirection === 'asc' ? 'text-[#337ab7]' : 'text-gray-300'} mb-0.5`}>▲</span>
        <span className={`${sortDirection === 'desc' ? 'text-[#337ab7]' : 'text-gray-300'}`}>▼</span>
      </div>
    );
  };

  // 应用过滤器
  const applyFilters = (newFilterStates: SampleFilterStates) => {
    const filtered = SampleFilterManager.applyFilters(allData, searchKeyword, newFilterStates);
    const sorted = sortData(filtered);
    setFilteredData(sorted);
    setCurrentPage(1);

    // 重新计算计数
    const counts = SampleFilterManager.calculateFilterCounts(allData, newFilterStates);
    setFilterCounts(counts);
  };

  // 搜索功能 - 避免在初始加载时干扰URL参数处理
  useEffect(() => {
    if (allData.length > 0 && !loading) {
      applyFilters(filterStates);
    }
  }, [searchKeyword, allData, loading]);

  // 当过滤器状态改变时重新计算选项
  useEffect(() => {
    if (allData.length > 0) {
      const counts = SampleFilterManager.calculateFilterCounts(allData, filterStates);
      setFilterCounts(counts);
    }
  }, [filterStates, allData]);

  // 获取过滤后的选项（只显示有数据的选项）
  const getFilteredOptions = () => {
    // 先根据当前选择过滤数据
    let workingData = allData;

    // 根据已选择的过滤器条件筛选数据
    if (filterStates.selectedTissueTypes.length > 0) {
      workingData = workingData.filter(item => 
        filterStates.selectedTissueTypes.includes(
          item.tissueCellType === 'nan' ? 'NA' : item.tissueCellType
        )
      );
    }

    if (filterStates.selectedCellLines.length > 0) {
      workingData = workingData.filter(item => 
        filterStates.selectedCellLines.includes(
          item.cellLine === 'nan' ? 'NA' : item.cellLine
        )
      );
    }

    if (filterStates.selectedDiseases.length > 0) {
      workingData = workingData.filter(item => 
        filterStates.selectedDiseases.includes(
          item.condition === 'nan' ? 'NA' : item.condition
        )
      );
    }

    // 从过滤后的数据中获取可用选项
    const tissueTypes = new Set<string>();
    const cellLines = new Set<string>();
    const diseases = new Set<string>();
    const dataTypes = new Set<string>();

    workingData.forEach(item => {
      const tissue = item.tissueCellType === 'nan' ? 'NA' : item.tissueCellType;
      const cellLine = item.cellLine === 'nan' ? 'NA' : item.cellLine;
      const disease = item.condition === 'nan' ? 'NA' : item.condition;
      const dataType = item.dataType === 'nan' ? 'NA' : item.dataType;

      tissueTypes.add(tissue);
      cellLines.add(cellLine);
      diseases.add(disease);
      dataTypes.add(dataType);
    });

    // 如果没有选择任何过滤器，显示所有选项
    if (filterStates.selectedTissueTypes.length === 0 && 
        filterStates.selectedCellLines.length === 0 && 
        filterStates.selectedDiseases.length === 0 &&
        filterStates.selectedDataTypes.length === 0) {
      allData.forEach(item => {
        const tissue = item.tissueCellType === 'nan' ? 'NA' : item.tissueCellType;
        const cellLine = item.cellLine === 'nan' ? 'NA' : item.cellLine;
        const disease = item.condition === 'nan' ? 'NA' : item.condition;
        const dataType = item.dataType === 'nan' ? 'NA' : item.dataType;

        tissueTypes.add(tissue);
        cellLines.add(cellLine);
        diseases.add(disease);
        dataTypes.add(dataType);
      });
    }

    // 自定义 Data Type 展示顺序：RNA → Ribo → RNC（其余按字母排序，NA 在最后）
    const orderForDisplay = (a: string, b: string) => {
      const rank = (dt: string) => {
        const s = String(dt || '').toLowerCase();
        if (s === 'na' || s === 'nan' || s === 'null') return 99;
        if (s.includes('rna') && !s.includes('ribo') && !s.includes('rnc')) return 0;
        if (s.includes('ribo')) return 1;
        if (s.includes('rnc') || s.includes('nascent')) return 2;
        return 50;
      };
      const ra = rank(a);
      const rb = rank(b);
      if (ra !== rb) return ra - rb;
      return a.localeCompare(b);
    };

    return {
      tissueTypes: Array.from(tissueTypes).sort(),
      cellLines: Array.from(cellLines).sort(),
      diseases: Array.from(diseases).sort(),
      dataTypes: Array.from(dataTypes).sort(orderForDisplay)
    };
  };

  const { tissueTypes, cellLines, diseases, dataTypes } = getFilteredOptions();

  // 处理组织/细胞类型选择
  const handleTissueTypeChange = (tissueType: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      // 清空细胞系选择（互斥）
      newStates.selectedCellLines = [];
      newStates.selectedTissueTypes = [tissueType];
      
      // 清空疾病选择，让用户重新选择符合该组织类型的疾病
      newStates.selectedDiseases = [];
    } else {
      newStates.selectedTissueTypes = [];
    }
    
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 处理细胞系选择
  const handleCellLineChange = (cellLine: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      // 清空组织类型选择（互斥）
      newStates.selectedTissueTypes = [];
      newStates.selectedCellLines = [cellLine];
      
      // 清空疾病选择，让用户重新选择符合该细胞系的疾病
      newStates.selectedDiseases = [];
    } else {
      newStates.selectedCellLines = [];
    }
    
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 处理疾病选择
  const handleDiseaseChange = (disease: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      if (!newStates.selectedDiseases.includes(disease)) {
        newStates.selectedDiseases.push(disease);
      }
    } else {
      newStates.selectedDiseases = newStates.selectedDiseases.filter(d => d !== disease);
    }
    
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 处理数据类型选择
  const handleDataTypeChange = (dataType: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      if (!newStates.selectedDataTypes.includes(dataType)) {
        newStates.selectedDataTypes.push(dataType);
      }
    } else {
      newStates.selectedDataTypes = newStates.selectedDataTypes.filter(dt => dt !== dataType);
    }
    
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 重置过滤器
  const resetFilters = () => {
    const newStates = SampleFilterManager.resetFilterStates();
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 提交过滤器
  const submitFilters = () => {
    applyFilters(filterStates);
  };

  // 辅助函数：格式化数值字段显示
  // 注意：该函数暂未使用，如需在表格中显示小数格式，可调用此函数
  // 保留以便未来扩展使用，避免未使用报错

  // 辅助函数：格式化整数字段显示（带千分位分隔符）
  const formatIntegerValue = (value: number | null | undefined): string => {
    if (value === null || value === undefined || String(value) === 'nan' || String(value) === 'null' || isNaN(Number(value))) {
      return 'NA';
    }
    return Number(value).toLocaleString();
  };

  // 下载功能
  const downloadData = (format: 'csv' | 'json') => {
    const dataToDownload = filteredData.length > 0 ? filteredData : allData;
    
    if (format === 'json') {
      const jsonData = JSON.stringify(dataToDownload, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'samples.json';
      a.click();
      URL.revokeObjectURL(url);
    } else if (format === 'csv') {
      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const headers = ['SRA ACCESSION', 'BIOSAMPLE ID', 'DATASET ID', 'BIOPROJECT ID', 'GEO ACCESSION', 'TISSUE/CELL TYPE', 'CELL LINE', 'CONDITION', 'CATEGORY', 'DATA TYPE', 'PLATFORM', 'INSTRUMENT', 'LIBRARY LAYOUT', 'DETAIL', 'TRANSLATED TRANSCRIPTS NUMBER', 'TRANSLATED GENES NUMBER'];
      const csvContent = [
        headers.join(','),
        ...dataToDownload.map(item => [
          escapeCsvValue(item.sraAccession),
          escapeCsvValue(item.bioSampleId),
          escapeCsvValue(item.datasetId),
          escapeCsvValue(item.bioProjectId),
          escapeCsvValue(item.geoAccession),
          escapeCsvValue(item.tissueCellType),
          escapeCsvValue(item.cellLine),
          escapeCsvValue(item.condition),
          escapeCsvValue(item.diseaseCategory),
          escapeCsvValue(item.dataType),
          escapeCsvValue(item.platform),
          escapeCsvValue(item.instrument),
          escapeCsvValue(item.libraryLayout),
          escapeCsvValue(item.detail),
          escapeCsvValue(formatIntegerValue(item.translatedTranscriptsNumber)),
          escapeCsvValue(formatIntegerValue(item.translatedGenesNumber))
        ].join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'samples.csv';
      a.click();
      URL.revokeObjectURL(url);
    }
    
    setShowDownloadOptions(false);
  };

  // 渲染过滤器选项
  const renderFilterOptions = (
    title: string,
    options: string[],
    selectedOptions: string[],
    showMore: boolean,
    onToggleMore: () => void,
    onOptionChange: (option: string, checked: boolean) => void,
    filterType: 'tissueTypes' | 'cellLines' | 'diseases' | 'dataTypes'
  ) => {
    // 只显示有计数的选项
    const optionsWithCounts = options.filter(option => {
      const count = filterCounts[filterType][option] || 0;
      return count > 0;
    });
    
    const displayOptions = showMore ? optionsWithCounts : optionsWithCounts.slice(0, DEFAULT_DISPLAY_COUNT);
    
    return (
      <div className="mb-6">
        <h3 className="font-semibold text-gray-900 mb-4 text-base">{title}</h3>
        <div className="space-y-3">
          {displayOptions.map(option => {
            const count = filterCounts[filterType][option] || 0;
            return (
              <label key={option} className="flex items-center space-x-3 text-sm cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <input
                  type="checkbox"
                  checked={selectedOptions.includes(option)}
                  onChange={(e) => onOptionChange(option, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                />
                <span className="text-gray-800 font-medium leading-relaxed">
                  {option} <span className="text-gray-500 font-normal">({count})</span>
                </span>
              </label>
            );
          })}
          {optionsWithCounts.length > DEFAULT_DISPLAY_COUNT && (
            <button
              onClick={onToggleMore}
              className="text-sm text-[#337ab7] hover:text-[#337ab7] underline font-medium ml-2 mt-1"
            >
              {showMore ? 'Show Less' : 'More'}
            </button>
          )}
        </div>
      </div>
    );
  };

  // 生成分页按钮
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (currentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setCurrentPage(currentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous
        </button>
      );
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            currentPage === i 
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]' 
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
        </button>
      );
    }

    // Next button
    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setCurrentPage(currentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center mt-6">{pages}</div>;
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="link-breadcrumb">
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span className="link-breadcrumb-current">Browse Sample</span>
        </div>
      </div>
      
      {/* 主内容区域 - 左右各2.5%空白 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
          <div className="flex gap-6">
            {/* 左侧过滤器 */}
            <div className="w-64 bg-white border border-gray-200 rounded-lg p-5 shadow-sm">
              {/* Tissue/Cell Type */}
              {renderFilterOptions(
                'Tissue/Cell Type',
                tissueTypes,
                filterStates.selectedTissueTypes,
                filterStates.showMoreTissue,
                () => setFilterStates(prev => ({ ...prev, showMoreTissue: !prev.showMoreTissue })),
                handleTissueTypeChange,
              'tissueTypes'
              )}

              {/* Cell Line */}
              {renderFilterOptions(
                'Cell Line',
                cellLines,
                filterStates.selectedCellLines,
                filterStates.showMoreCellLine,
                () => setFilterStates(prev => ({ ...prev, showMoreCellLine: !prev.showMoreCellLine })),
                handleCellLineChange,
                'cellLines'
              )}

              {/* Disease/Condition */}
              {renderFilterOptions(
                'Condition',
                diseases,
                filterStates.selectedDiseases,
                filterStates.showMoreDisease,
                () => setFilterStates(prev => ({ ...prev, showMoreDisease: !prev.showMoreDisease })),
                handleDiseaseChange,
                'diseases'
              )}

              {/* Data Type */}
              {renderFilterOptions(
                'Data Type',
                dataTypes,
                filterStates.selectedDataTypes,
                filterStates.showMoreDataType,
                () => setFilterStates(prev => ({ ...prev, showMoreDataType: !prev.showMoreDataType })),
                handleDataTypeChange,
                'dataTypes'
              )}

              {/* 操作按钮 */}
              <div className="flex gap-3 mt-8 pt-4 border-t border-gray-200">
                <Button onClick={submitFilters} className="btn-submit-full">
                  Submit
                </Button>
                <Button onClick={resetFilters} variant="outline" className="btn-reset">
                  Reset
                </Button>
              </div>
            </div>
        
            {/* 右侧内容区域 */}
            <div className="flex-1 min-w-0 max-w-full">
              {/* 顶部搜索和下载区域 */}
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <h1 className="text-xl font-semibold text-gray-800">
                    {loading ? 'Loading...' : `${filteredData.length} Sample${filteredData.length === 1 ? '' : 's'} Found`}
                  </h1>
                </div>
                
                <div className="flex items-center space-x-4">
          <div className="flex">
            <Input
                      placeholder="Search"
              value={searchKeyword}
                      onChange={(e) => setSearchKeyword(e.target.value)}
                      className="h-10 w-80 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
            />
                  </div>
                  
                  <div className="relative">
                    <button
                      onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                      className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </button>
                    
                    {showDownloadOptions && (
                      <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="py-1">
                          <button
                            onClick={() => downloadData('csv')}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                          >
                            Download as CSV
                          </button>
                          <button
                            onClick={() => downloadData('json')}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                          >
                            Download as JSON
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
          </div>
        </div>
        
              {/* 表格容器 */}
              <div className="bg-white rounded-lg overflow-hidden w-full">
        {loading ? (
          <div className="p-6 text-center">
            <p className="text-gray-600">Loading data...</p>
          </div>
        ) : error ? (
          <div className="p-6 border-red-500 border-l-4">
            <p className="text-red-600">Error: {error}</p>
          </div>
                ) : (
                  <>
                    {/* 表格 */}
                    <div className="border border-gray-200 rounded-lg overflow-hidden">
                      <div 
                        id="table-container"
                        className="table-scroll"
                        style={{ 
                          overflowX: 'auto',
                          overflowY: 'hidden',
                          position: 'relative',
                          scrollbarWidth: 'none', // Firefox
                          msOverflowStyle: 'none' // IE
                        }}
                      >
                      <table 
                        className="w-full" 
                        style={{ 
                          minWidth: '1100px',
                          fontSize: '13px',
                          textAlign: 'center',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        <thead className="bg-gray-50 border-b border-gray-200">
                          <tr>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('bioSampleId')}
                            >
                              <div className="flex items-center justify-center">
                                <span>BIOSAMPLE ID</span>
                                {renderSortIcon('bioSampleId')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('sraAccession')}
                            >
                              <div className="flex items-center justify-center">
                                <span>SRA ACCESSION</span>
                                {renderSortIcon('sraAccession')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('datasetId')}
                            >
                              <div className="flex items-center justify-center">
                                <span>DATASET ID</span>
                                {renderSortIcon('datasetId')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('geoAccession')}
                            >
                              <div className="flex items-center justify-center">
                                <span>GEO ACCESSION</span>
                                {renderSortIcon('geoAccession')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('bioProjectId')}
                            >
                              <div className="flex items-center justify-center">
                                <span>BIOPROJECT ID</span>
                                {renderSortIcon('bioProjectId')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('translatedTranscriptsNumber')}
                            >
                              <div className="flex items-center justify-center">
                                <span>TRANSLATED TRANSCRIPTS NUMBER</span>
                                {renderSortIcon('translatedTranscriptsNumber')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('translatedGenesNumber')}
                            >
                              <div className="flex items-center justify-center">
                                <span>TRANSLATED GENES NUMBER</span>
                                {renderSortIcon('translatedGenesNumber')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[180px] cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('tissueCellType')}
                            >
                              <div className="flex items-center justify-center">
                                <span>TISSUE/CELL TYPE</span>
                                {renderSortIcon('tissueCellType')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px] cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('cellLine')}
                            >
                              <div className="flex items-center justify-center">
                                <span>CELL LINE</span>
                                {renderSortIcon('cellLine')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px] cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('condition')}
                            >
                              <div className="flex items-center justify-center">
                                <span>CONDITION</span>
                                {renderSortIcon('condition')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px] cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('diseaseCategory')}
                            >
                              <div className="flex items-center justify-center">
                                <span>CATEGORY</span>
                                {renderSortIcon('diseaseCategory')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider max-w-[120px] cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('dataType')}
                            >
                              <div className="flex items-center justify-center">
                                <span>DATA TYPE</span>
                                {renderSortIcon('dataType')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider max-w-[100px] cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('platform')}
                            >
                              <div className="flex items-center justify-center">
                                <span>PLATFORM</span>
                                {renderSortIcon('platform')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[180px] cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('instrument')}
                            >
                              <div className="flex items-center justify-center">
                                <span>INSTRUMENT</span>
                                {renderSortIcon('instrument')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('libraryLayout')}
                            >
                              <div className="flex items-center justify-center">
                                <span>LIBRARYLAYOUT</span>
                                {renderSortIcon('libraryLayout')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('detail')}
                            >
                              <div className="flex items-center justify-center">
                                <span>DETAIL</span>
                                {renderSortIcon('detail')}
                              </div>
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {currentData.map((item, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm text-center">
                                {item.bioSampleId && item.bioSampleId !== 'nan' && item.bioSampleId !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/biosample/?term=${item.bioSampleId}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.bioSampleId}
                                  </a>
                                ) : (
                                  "NA"
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.sraAccession && item.sraAccession !== 'nan' && item.sraAccession !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/sra/?term=${item.sraAccession}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.sraAccession}
                                  </a>
                                ) : (
                                  "NA"
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.datasetId && item.datasetId !== 'nan' && item.datasetId !== 'null' ? (
                                  <Link
                                    href={`/browse/dataset?search=${item.datasetId}`}
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.datasetId}
                                  </Link>
                                ) : (
                                  "NA"
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.geoAccession && item.geoAccession !== 'nan' && item.geoAccession !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=${item.geoAccession}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.geoAccession}
                                  </a>
                                ) : (
                                  "NA"
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.bioProjectId && item.bioProjectId !== 'nan' && item.bioProjectId !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/bioproject/${item.bioProjectId}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.bioProjectId}
                                  </a>
                                ) : (
                                  "NA"
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {formatIntegerValue(item.translatedTranscriptsNumber)}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {formatIntegerValue(item.translatedGenesNumber)}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                <div className="whitespace-nowrap">
                                  {item.tissueCellType && item.tissueCellType !== 'nan' && item.tissueCellType !== 'null' ? item.tissueCellType : 'NA'}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                <div className="whitespace-nowrap">
                                  {item.cellLine && item.cellLine !== 'nan' && item.cellLine !== 'null' ? item.cellLine : 'NA'}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                <div className="whitespace-nowrap">
                                  {item.condition && item.condition !== 'nan' && item.condition !== 'null' ? item.condition : 'NA'}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                <div className="whitespace-nowrap">
                                  {item.diseaseCategory && item.diseaseCategory !== 'nan' && item.diseaseCategory !== 'null' ? item.diseaseCategory : 'NA'}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center max-w-[120px]">
                                <div className="truncate" title={item.dataType}>
                                  {item.dataType && item.dataType !== 'nan' && item.dataType !== 'null' ? item.dataType : 'NA'}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center max-w-[100px]">
                                <div className="truncate" title={item.platform}>
                                  {item.platform && item.platform !== 'nan' && item.platform !== 'null' ? item.platform.toUpperCase() : 'NA'}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                <div className="whitespace-nowrap">
                                  {item.instrument && item.instrument !== 'nan' && item.instrument !== 'null' ? item.instrument : 'NA'}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.libraryLayout && item.libraryLayout !== 'nan' && item.libraryLayout !== 'null' ? item.libraryLayout : 'NA'}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                <div className="whitespace-nowrap">
                                  {item.detail && item.detail !== 'nan' && item.detail !== 'null' ? item.detail : 'NA'}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                        </table>
                      </div>
                      
                      {/* 自定义水平滚动条 - 始终显示 */}
                      <div 
                        className="bg-gray-100 border-t border-gray-200"
                        style={{
                          height: '16px',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                      >
                        <div
                          id="custom-scrollbar-track"
                          className="w-full h-full bg-gray-100 cursor-pointer"
                          style={{
                            position: 'relative'
                          }}
                        >
                          <div
                            id="custom-scrollbar-thumb"
                            className="bg-gray-400 hover:bg-gray-500 rounded"
                            style={{
                              height: '12px',
                              width: '80px',
                              position: 'absolute',
                              top: '2px',
                              left: '0px',
                              cursor: 'grab',
                              transition: 'background-color 0.2s'
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    {/* 分页和控制 */}
                    <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-6">
                          <span className="text-sm font-medium text-gray-700">
                            Showing {startIndex + 1} to {Math.min(endIndex, filteredData.length)} of {filteredData.length} rows
                          </span>
                          <div className="flex items-center space-x-2">
                            <label className="text-sm font-medium text-gray-700">
                              <select
                                value={pageSize}
                                onChange={(e) => {
                                  setPageSize(Number(e.target.value));
                                  setCurrentPage(1);
                                }}
                                className="ml-2 mr-2 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#0071BC] focus:border-transparent"
                              >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                              </select>
                              rows per page
                            </label>
                          </div>
                        </div>
                        
                        {renderPagination()}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-4">
        <Footer />
      </div>
      
      {/* 点击其他地方关闭下载菜单 */}
      {showDownloadOptions && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowDownloadOptions(false)}
        />
      )}
    </div>
  );
}

export default function SamplePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SamplePageContent />
    </Suspense>
  );
}