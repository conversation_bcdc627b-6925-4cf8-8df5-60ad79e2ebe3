'use client';

import { useParams } from 'next/navigation';
import { useEffect, useState, useMemo, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { parseApiData, calculateZScore, calculateZScoreForDataType, prepareEChartsHistogramData, DataPoint, ApiDataPoint } from '@/lib/csvUtils';
import EChartsHistogram from '@/app/components/Charts/EChartsHistogram';
import Footer from '@/app/components/Footer/Footer';
import { Download, ChevronDown } from 'lucide-react';

// 定义表格行数据类型
interface TableRowData {
  transcriptId: string;
  geneId: string;
  projectId: string;
  te: number | null;
  teZscore: number | null;
  teLevel: string | null;
  tr: number | null;
  trZscore: number | null;
  trLevel: string | null;
  evi: number | null;
  eviZscore: number | null;
  eviLevel: string | null;
}

export default function DatasetDetailPage() {
  const params = useParams();
  const datasetId = params.dataset as string;

  const [data, setData] = useState<DataPoint[]>([]);
  const [rawApiData, setRawApiData] = useState<ApiDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ---------- 表格交互状态 ----------
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);

  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        setError(null);

        // Get API data
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';
        const response = await fetch(`${apiUrl}/translation-indices/projectId/${datasetId}`);
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`HTTP error! status: ${response.status}. ${errorData.details || ''}`);
        }

        const apiData: ApiDataPoint[] = await response.json();
        setRawApiData(apiData); // 保存原始API数据


        const parsedData = parseApiData(apiData);

        if (parsedData.length === 0) {
          throw new Error('No valid data found');
        }

        const processedData = calculateZScore(parsedData);
        setData(processedData);
      } catch (err) {
        console.error('Error loading data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    }

    if (datasetId) {
      loadData();
    }
  }, [datasetId]);

  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300); // 300ms delay

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

  const calculateSkewness = useCallback((values: number[]): number => {
    const n = values.length;
    if (n < 3) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / n;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (n - 1);
    const stdDev = Math.sqrt(variance);

    if (stdDev === 0) return 0;

    const skewSum = values.reduce((sum, val) => sum + Math.pow((val - mean) / stdDev, 3), 0);
    return skewSum / n;
  }, []);

  // 使用useMemo优化数据统计和处理
  const validCounts = useMemo(() => ({
    validTECount: rawApiData.filter(d => d.te !== null && d.te !== undefined).length,
    validTRCount: rawApiData.filter(d => d.tr !== null && d.tr !== undefined).length,
    validEVICount: rawApiData.filter(d => d.evi !== null && d.evi !== undefined).length
  }), [rawApiData]);

  // 准备表格数据
  const tableData: TableRowData[] = useMemo(() => {
    if (!rawApiData.length || !data.length) return [];

    const trDataWithZScore = calculateZScoreForDataType(data, 'TR');
    const eviDataWithZScore = calculateZScoreForDataType(data, 'EVI');

    const teMap = new Map(data.map(d => [d.transcript_id, d]));
    const trMap = new Map(trDataWithZScore.map(d => [d.transcript_id, d]));
    const eviMap = new Map(eviDataWithZScore.map(d => [d.transcript_id, d]));

    return rawApiData.map(apiItem => {
      const teData = teMap.get(apiItem.transcriptId);
      const trData = trMap.get(apiItem.transcriptId);
      const eviData = eviMap.get(apiItem.transcriptId);

      return {
        transcriptId: apiItem.transcriptId,
        geneId: apiItem.geneId,
        projectId: apiItem.projectId,
        te: apiItem.te,
        teZscore: teData?.zscore ?? null,
        teLevel: teData?.quantile_group ?? null,
        tr: apiItem.tr,
        trZscore: trData?.zscore ?? null,
        trLevel: trData?.quantile_group ?? null,
        evi: apiItem.evi,
        eviZscore: eviData?.zscore ?? null,
        eviLevel: eviData?.quantile_group ?? null,
      };
    });
  }, [rawApiData, data]);

  // 过滤数据（memoize），扩展到所有列
  const filteredTableData = useMemo(() => {
    const term = debouncedSearchTerm.trim().toLowerCase();
    if (!term) return tableData;
    return tableData.filter(row =>
      Object.values(row).some(value =>
        value !== null && String(value).toLowerCase().includes(term)
      )
    );
  }, [tableData, debouncedSearchTerm]);

  // 计算分页
  const totalRows = filteredTableData.length;
  const totalPages = Math.max(1, Math.ceil(totalRows / rowsPerPage));
  const paginatedData = useMemo(() => filteredTableData.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage), [filteredTableData, currentPage, rowsPerPage]);

  // 使用useMemo优化图表数据计算
  const chartData = useMemo(() => {
    if (!rawApiData.length) return [];

    const processChartData = (field: 'te' | 'tr' | 'evi', type: 'TE' | 'TR' | 'EVI') => {
      const filteredData = rawApiData
        .filter(d => d[field] !== null && d[field] !== undefined && d[field]! > 0)
        .map(d => ({
          transcript_id: d.transcriptId,
          TR: d.tr,
          EVI: d.evi,
          TE: d.te
        }));

      if (filteredData.length === 0) return null;

      const dataWithZScore = type === 'TE'
        ? calculateZScore(filteredData)
        : calculateZScoreForDataType(filteredData, type);

      const values = dataWithZScore.map(d => d.zscore!);
      const skewness = calculateSkewness(values);
      const histogramData = prepareEChartsHistogramData(dataWithZScore);

      return {
        type,
        data: histogramData,
        count: validCounts[`valid${type}Count` as keyof typeof validCounts],
        skewness
      };
    };

    const charts = [];

    if (validCounts.validTECount > 0) {
      const teChart = processChartData('te', 'TE');
      if (teChart) charts.push(teChart);
    }

    if (validCounts.validTRCount > 0) {
      const trChart = processChartData('tr', 'TR');
      if (trChart) charts.push(trChart);
    }

    if (validCounts.validEVICount > 0) {
      const eviChart = processChartData('evi', 'EVI');
      if (eviChart) charts.push(eviChart);
    }

    return charts;
  }, [rawApiData, validCounts, calculateSkewness]);


  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(e.target.value, 10);
    setRowsPerPage(value);
    setCurrentPage(1); // 重置到第一页
  };

  // 辅助函数：格式化数值字段显示
  const formatNumericValue = (value: number | null | undefined, decimals: number = 3): string => {
    if (value === null || value === undefined || String(value) === 'nan' || String(value) === 'null' || isNaN(Number(value))) {
      return 'NA';
    }
    return Number(value).toFixed(decimals);
  };

  const downloadData = (format: 'csv' | 'json') => {
    if (!tableData.length) return;

    let blob;
    let filename;

    if (format === 'json') {
      const jsonContent = JSON.stringify(tableData, null, 2);
      blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
      filename = `${datasetId}_transcripts.json`;
    } else { // csv
      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const headers = [
        'TRANSCRIPT_ID','GENEID','DATASETID','TE','TE_ZSCORE','TE_LEVEL',
        'TR','TR_ZSCORE','TR_LEVEL','EVI','EVI_ZSCORE','EVI_LEVEL'
      ];
      const csvRows = [headers.join(',')];
      tableData.forEach(row => {
        const rowArr = [
          escapeCsvValue(row.transcriptId),
          escapeCsvValue(row.geneId),
          escapeCsvValue(row.projectId),
          escapeCsvValue(formatNumericValue(row.te)),
          escapeCsvValue(formatNumericValue(row.teZscore, 4)),
          escapeCsvValue(row.teLevel),
          escapeCsvValue(formatNumericValue(row.tr)),
          escapeCsvValue(formatNumericValue(row.trZscore, 4)),
          escapeCsvValue(row.trLevel),
          escapeCsvValue(formatNumericValue(row.evi)),
          escapeCsvValue(formatNumericValue(row.eviZscore, 4)),
          escapeCsvValue(row.eviLevel)
        ];
        csvRows.push(rowArr.join(','));
      });
      const csvContent = csvRows.join('\n');
      blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      filename = `${datasetId}_transcripts.csv`;
    }

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setShowDownloadOptions(false);
  };







  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
          <div className="text-sm flex items-center">
            <Image 
              src={`${process.env.basePath || ''}/logo/Home.png`}
              alt="Home"
              width={16}
              height={16}
              className="mr-2"
            />
            <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
              Home
            </Link>
            <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
            <Link href="/browse/dataset" className="hover:underline font-bold" style={{ color: '#000000' }}>
              Browse Dataset
            </Link>
            <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
            <span style={{ color: '#337ab7' }}>Transcriptome-wide Distribution</span>
          </div>
        </div>

                <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="text-lg">Loading...</div>
          </div>
        </div>
      </div>
  );
}

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
          <div className="text-sm flex items-center">
            <Image 
              src={`${process.env.basePath || ''}/logo/Home.png`}
              alt="Home"
              width={16}
              height={16}
              className="mr-2"
            />
            <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
              Home
            </Link>
            <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
            <Link href="/browse/dataset" className="hover:underline font-bold" style={{ color: '#000000' }}>
              Browse Dataset
            </Link>
            <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
            <span style={{ color: '#337ab7' }}>Transcriptome-wide Distribution</span>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Error: {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="hover:underline font-bold" style={{ color: '#000000' }}>
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <Link href="/browse/dataset" className="hover:underline font-bold" style={{ color: '#000000' }}>
            Browse Dataset
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span style={{ color: '#337ab7' }}>Transcriptome-wide Distribution</span>
        </div>
      </div>

      {/* Title area with rounded corners */}
      <div style={{ margin: '0 2.5%', width: '95%' }} className="pt-6">
        <div style={{ backgroundColor: 'rgba(0, 102, 204, 0.85)' }} className="text-white py-3 rounded-lg">
          <h1 className="text-2xl font-bold text-center">Transcriptome-wide Distribution</h1>
        </div>
      </div>

      <div style={{ margin: '0 2.5%', width: '95%' }} className="py-6">
        {/* Description text */}
        <div className="mb-6 text-gray-700 leading-relaxed">
          <p>
          The following figure shows the <strong>distributions of TE, TR and EVI across all transcripts </strong>in the selected dataset. 
          By calculating the z-scores for each transcript, all transcripts were divided into four equal intervals (from 
          the first to the fourth quartile) based on their z-scores. <strong>Frequency </strong>represents the number of transcripts within 
          each z-score range, while n denotes the total number of transcripts. <strong>Skewness</strong> indicates the degree of asymmetry 
          in the distribution, with a skewness value of less than 0 suggesting that, in this dataset, the transcriptome-wide 
          distribution of TE (TR, EVI) was skewed towards inefficiently translated transcripts.
          </p>
        </div>

        {/* Chart area with fixed layout */}
        <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">

          <div
            style={{
              display: 'flex',
              justifyContent: chartData.length === 1 ? 'center' : 'space-between',
              gap: '3.5%'
            }}
          >
            {chartData.map((chart) => (
              <div
                key={chart.type}
                style={{
                  flex: '0 0 31%',
                  height: '28rem',
                  border: '1px solid #e5e7eb',
                  borderRadius: '0.25rem',
                  position: 'relative'
                }}
                className={chartData.length === 1 ? 'mx-auto' : ''}
              >


                <div
                  data-chart-type={chart.type}
                  className="w-full h-full"
                >
                  <EChartsHistogram
                    data={chart.data}
                    title={`Transcriptome-wide distribution of ${chart.type} of ${datasetId}`}
                    skewness={chart.skewness}
                    totalCount={chart.count}
                    chartId={`${datasetId}_${chart.type}`}
                    xAxisLabel={`log₂ ${chart.type} (z-score)`}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Data Table */}
        <div className="mt-8 bg-white rounded-lg border border-gray-200 shadow-sm">
          {/* 表头工具栏 */}
          <div className="p-4 flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{filteredTableData.length.toLocaleString()} {filteredTableData.length === 1 ? 'Transcript' : 'Transcripts'} Found</h2>
            </div>
            <div className="flex items-center space-x-2">
              {/* 搜索框 */}
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                className="border border-gray-300 rounded px-3 py-1 text-sm w-72 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              {/* 下载按钮 */}
              <div className="relative">
                <button
                  onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                  className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  <ChevronDown className="h-4 w-4 ml-2" />
                </button>

                {showDownloadOptions && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="py-1">
                      <button
                        onClick={() => downloadData('csv')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                      >
                        Download as CSV
                      </button>
                      <button
                        onClick={() => downloadData('json')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                      >
                        Download as JSON
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Transcript ID
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gene ID
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dataset ID
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TE
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TE Z-score
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TE Level
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TR
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TR Z-score
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TR Level
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    EVI
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    EVI Z-score
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    EVI Level
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedData.map((row, index) => (
                  <tr key={row.transcriptId} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-4 py-3 text-sm text-center text-gray-900 font-medium">
                      {row.transcriptId && row.transcriptId !== 'nan' && row.transcriptId !== 'null' ? row.transcriptId : 'NA'}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {row.geneId && row.geneId !== 'nan' && row.geneId !== 'null' ? row.geneId : 'NA'}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {row.projectId && row.projectId !== 'nan' && row.projectId !== 'null' ? row.projectId : 'NA'}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {formatNumericValue(row.te)}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {formatNumericValue(row.teZscore)}
                    </td>
                    <td className="px-4 py-3 text-sm text-center">
                      {row.teLevel ? (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.teLevel === 'Q1' ? 'bg-blue-100 text-blue-600' :
                          row.teLevel === 'Q2' ? 'bg-sky-100 text-sky-600' :
                          row.teLevel === 'Q3' ? 'bg-orange-100 text-orange-600' :
                          'bg-red-100 text-red-600'
                        }`}>
                          {row.teLevel}
                        </span>
                      ) : (
                        'NA'
                      )}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {formatNumericValue(row.tr)}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {formatNumericValue(row.trZscore)}
                    </td>
                    <td className="px-4 py-3 text-sm text-center">
                      {row.trLevel ? (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.trLevel === 'Q1' ? 'bg-red-100 text-red-800' :
                          row.trLevel === 'Q2' ? 'bg-orange-100 text-orange-800' :
                          row.trLevel === 'Q3' ? 'bg-sky-100 text-sky-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {row.trLevel}
                        </span>
                      ) : (
                        'NA'
                      )}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {formatNumericValue(row.evi)}
                    </td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">
                      {formatNumericValue(row.eviZscore)}
                    </td>
                    <td className="px-4 py-3 text-sm text-center">
                      {row.eviLevel ? (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.eviLevel === 'Q1' ? 'bg-red-100 text-red-800' :
                          row.eviLevel === 'Q2' ? 'bg-orange-100 text-orange-800' :
                          row.eviLevel === 'Q3' ? 'bg-sky-100 text-sky-800' :
                          'bg-[#e6f2ff] text-[#337ab7]'
                        }`}>
                          {row.eviLevel}
                        </span>
                      ) : (
                        'NA'
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredTableData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              暂无数据
            </div>
          )}

          {/* 分页和控制 */}
          <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <span className="text-sm font-medium text-gray-700">
                  Showing {((currentPage - 1) * rowsPerPage + 1).toLocaleString()} to {Math.min(currentPage * rowsPerPage, totalRows).toLocaleString()} of {totalRows.toLocaleString()} rows
                </span>
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">
                    <select
                      value={rowsPerPage}
                      onChange={handleRowsPerPageChange}
                      className="border border-gray-300 rounded px-2 py-1 text-sm ml-2 mr-2"
                    >
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>
                    rows per page
                  </label>
                </div>
              </div>

              {/* 分页按钮 */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center">
                  {currentPage > 1 && (
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Previous
                    </button>
                  )}

                  {/* 页码按钮 */}
                  {(() => {
                    const pages = [];
                    const maxVisiblePages = 5;

                    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                    if (endPage - startPage + 1 < maxVisiblePages) {
                      startPage = Math.max(1, endPage - maxVisiblePages + 1);
                    }

                    for (let i = startPage; i <= endPage; i++) {
                      pages.push(
                        <button
                          key={i}
                          onClick={() => setCurrentPage(i)}
                          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
                            currentPage === i
                              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]'
                              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
                          }`}
                        >
                          {i}
                        </button>
                      );
                    }
                    return pages;
                  })()}

                  {currentPage < totalPages && (
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Next
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-4">
        <Footer />
      </div>
    </div>
  );
} 