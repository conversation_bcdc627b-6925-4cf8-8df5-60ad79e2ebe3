'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Download, ChevronDown, Info } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '../../components/Footer/Footer';
import { 
  Project, 
  FilterStates, 
  FilterCounts, 
  ProjectFilterManager 
} from './FilterManager';


const DEFAULT_DISPLAY_COUNT = 3;

// 添加CSS样式来隐藏默认滚动条
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    .table-scroll::-webkit-scrollbar {
      display: none;
    }
    .table-scroll {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  `;
  document.head.appendChild(style);
}

function ProjectPageContent() {
  const searchParams = useSearchParams();
  
  // 状态管理
  const [allData, setAllData] = useState<Project[]>([]);
  const [filteredData, setFilteredData] = useState<Project[]>([]);
  const [baseFilteredData, setBaseFilteredData] = useState<Project[]>([]); // 新增：保存基础过滤后的数据（不含搜索）
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchKeyword, setSearchKeyword] = useState<string>("");
  const [sortField, setSortField] = useState<string>('projectId');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [showDownloadOptions, setShowDownloadOptions] = useState<boolean>(false);

  // 过滤器状态
  const [filterStates, setFilterStates] = useState<FilterStates>(
    ProjectFilterManager.createInitialFilterStates()
  );
  const [filterCounts, setFilterCounts] = useState<FilterCounts>({
    tissueTypes: {},
    cellLines: {},
    diseases: {}
  });


  // 计算总页数
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = filteredData.slice(startIndex, endIndex);

  // 处理URL参数并应用初始筛选
  const applyUrlParameters = (data: Project[]) => {
    const searchParam = searchParams.get('search');
    const tissueTypeParam = searchParams.get('tissueType');
    const cellLineParam = searchParams.get('cellLine');
    const diseaseParam = searchParams.get('disease');

    // 设置搜索关键字
    if (searchParam) {
      setSearchKeyword(searchParam);
    }

    // 设置过滤器状态
    const newFilterStates: FilterStates = {
      selectedTissueTypes: tissueTypeParam ? [tissueTypeParam] : [],
      selectedCellLines: cellLineParam ? [cellLineParam] : [],
      selectedDiseases: diseaseParam ? [diseaseParam] : [],
      showMoreTissue: false,
      showMoreCellLine: false,
      showMoreDisease: false
    };

    setFilterStates(newFilterStates);

    // 应用筛选（包含搜索，保持URL参数处理的兼容性）
    const filtered = ProjectFilterManager.applyFilters(data, searchParam || '', newFilterStates);
    const sorted = sortData(filtered);
    setFilteredData(sorted);
    
    // 同时设置基础过滤数据
    const baseFiltered = ProjectFilterManager.applyFilterOnly(data, newFilterStates);
    setBaseFilteredData(baseFiltered);

    // 计算过滤器计数
    const counts = ProjectFilterManager.calculateFilterCounts(data, newFilterStates);
    setFilterCounts(counts);
  };

  // 加载所有数据
  const fetchAllData = async () => {
    try {
      setLoading(true);
      
      // 添加随机延迟使页面加载变慢 (2-5秒)
      const initialDelay = 2000 + Math.random() * 3000;
      await new Promise(resolve => setTimeout(resolve, initialDelay));
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/all`);
      if (!response.ok) {
        throw new Error(`API returned error: ${response.status}`);
      }
      
      // 添加随机延迟处理响应数据 (500-1500毫秒)
      const responseDelay = 500 + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, responseDelay));
      
      const result = await response.json();
      
      // 再添加随机延迟处理数据 (300-800毫秒)
      const dataDelay = 300 + Math.random() * 500;
      await new Promise(resolve => setTimeout(resolve, dataDelay));
      
      setAllData(result);
      
      // 处理URL参数并应用初始筛选
      applyUrlParameters(result);
      
      setError(null);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // 即使出错也要设置默认状态
      setFilteredData([]);
      const initialFilterStates = ProjectFilterManager.createInitialFilterStates();
      setFilterStates(initialFilterStates);
      const counts = ProjectFilterManager.calculateFilterCounts([], initialFilterStates);
      setFilterCounts(counts);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchAllData();
  }, []);

  // 应用搜索功能（仅搜索表格中显示的字段）
  const applySearch = (data: Project[], keyword: string): Project[] => {
    if (!keyword.trim()) return data;
    
    // 定义表格中实际显示的字段
    const searchableFields: (keyof Project)[] = [
      'projectId',
      'bioProjectId', 
      'geoAccession',
      'translatedTranscriptsNumber',
      'translatedGenesNumber',
      'strategy',
      'tissueOrCellType',
      'cellLine',
      'disease',
      'diseaseCategory',
      'srrNumber',
      'pmid'
    ];
    
    return data.filter(item =>
      searchableFields.some(field => {
        const value = item[field];
        return value?.toString().toLowerCase().includes(keyword.toLowerCase());
      })
    );
  };

  // 应用过滤器（不含搜索）
  const applyFilters = (newFilterStates: FilterStates) => {
    const baseFiltered = ProjectFilterManager.applyFilterOnly(allData, newFilterStates);
    setBaseFilteredData(baseFiltered);
    
    // 应用搜索到基础过滤结果上
    const searchFiltered = applySearch(baseFiltered, searchKeyword);
    const sorted = sortData(searchFiltered);
    setFilteredData(sorted);
    setCurrentPage(1);

    // 重新计算计数
    const counts = ProjectFilterManager.calculateFilterCounts(allData, newFilterStates);
    setFilterCounts(counts);
  };

  // 搜索功能 - 基于当前表格数据进行搜索
  useEffect(() => {
    if (baseFilteredData.length > 0) {
      const searchFiltered = applySearch(baseFilteredData, searchKeyword);
      const sorted = sortData(searchFiltered);
      setFilteredData(sorted);
      setCurrentPage(1);
    }
  }, [searchKeyword, baseFilteredData]); // 移除 sortData 依赖，因为它是稳定的函数

  // 自定义滚动条功能
  useEffect(() => {
    const tableContainer = document.getElementById('table-container');
    const scrollbarThumb = document.getElementById('custom-scrollbar-thumb');
    const scrollbarTrack = document.getElementById('custom-scrollbar-track');

    if (!tableContainer || !scrollbarThumb || !scrollbarTrack) return;

    let isDragging = false;
    let startX = 0;
    let startScrollLeft = 0;

    // 计算滚动条thumb的位置和大小
    const updateScrollbar = () => {
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const scrollLeft = tableContainer.scrollLeft;

      if (scrollWidth <= containerWidth) {
        scrollbarThumb.style.display = 'none';
        return;
      }

      scrollbarThumb.style.display = 'block';

      // 计算thumb的宽度（比例）
      const thumbWidth = Math.max((containerWidth / scrollWidth) * containerWidth, 30);
      scrollbarThumb.style.width = `${thumbWidth}px`;

      // 计算thumb的位置
      const maxThumbLeft = containerWidth - thumbWidth;
      const thumbLeft = (scrollLeft / (scrollWidth - containerWidth)) * maxThumbLeft;
      scrollbarThumb.style.left = `${thumbLeft}px`;
    };

    // 鼠标按下事件
    const handleMouseDown = (e: MouseEvent) => {
      isDragging = true;
      startX = e.clientX;
      startScrollLeft = tableContainer.scrollLeft;
      scrollbarThumb.style.cursor = 'grabbing';
      e.preventDefault();
    };

    // 鼠标移动事件
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const thumbWidth = parseFloat(scrollbarThumb.style.width);
      const maxThumbLeft = containerWidth - thumbWidth;
      const maxScrollLeft = scrollWidth - containerWidth;

      const scrollRatio = deltaX / maxThumbLeft;
      const newScrollLeft = startScrollLeft + (scrollRatio * maxScrollLeft);

      tableContainer.scrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));
    };

    // 鼠标松开事件
    const handleMouseUp = () => {
      isDragging = false;
      scrollbarThumb.style.cursor = 'grab';
    };

    // 点击轨道事件
    const handleTrackClick = (e: MouseEvent) => {
      if (e.target === scrollbarThumb) return;

      const rect = scrollbarTrack.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const thumbWidth = parseFloat(scrollbarThumb.style.width);
      const maxThumbLeft = containerWidth - thumbWidth;
      const maxScrollLeft = scrollWidth - containerWidth;

      const targetThumbLeft = Math.max(0, Math.min(clickX - thumbWidth / 2, maxThumbLeft));
      const scrollRatio = targetThumbLeft / maxThumbLeft;
      tableContainer.scrollLeft = scrollRatio * maxScrollLeft;
    };

    // 表格滚动时更新滚动条
    const handleScroll = () => {
      updateScrollbar();
    };

    // 窗口大小改变时更新滚动条
    const handleResize = () => {
      updateScrollbar();
    };

    // 绑定事件
    scrollbarThumb.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    scrollbarTrack.addEventListener('click', handleTrackClick);
    tableContainer.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    // 初始化滚动条
    updateScrollbar();

    // 清理事件监听器
    return () => {
      scrollbarThumb.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      scrollbarTrack.removeEventListener('click', handleTrackClick);
      tableContainer.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [filteredData]); // 当数据变化时重新初始化滚动条

  // 排序功能
  const sortData = (data: Project[]) => {
    return [...data].sort((a, b) => {
      const aValue = a[sortField as keyof Project] || '';
      const bValue = b[sortField as keyof Project] || '';
      
      // 特殊处理数字类型的字段
      if (sortField === 'translatedTranscriptsNumber' || sortField === 'translatedGenesNumber' || sortField === 'srrNumber') {
        const aNum = Number(aValue) || 0;
        const bNum = Number(bValue) || 0;
        if (sortDirection === 'asc') {
          return aNum - bNum;
        } else {
          return bNum - aNum;
        }
      }
      
      // 字符串排序
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });
  };

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 当排序字段或方向改变时重新排序
  useEffect(() => {
    const sorted = sortData(filteredData);
    setFilteredData(sorted);
  }, [sortField, sortDirection]);

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
          return (
              <div className="flex flex-col ml-1 text-xs leading-none">
          <span className="text-gray-300 mb-0.5">▲</span>
          <span className="text-gray-300">▼</span>
            </div>
          );
          }
          
          return (
      <div className="flex flex-col ml-1 text-xs leading-none">
        <span className={`${sortDirection === 'asc' ? 'text-[#337ab7]' : 'text-gray-300'} mb-0.5`}>▲</span>
        <span className={`${sortDirection === 'desc' ? 'text-[#337ab7]' : 'text-gray-300'}`}>▼</span>
              </div>
    );
  };

  // 当过滤器状态改变时重新计算选项
  useEffect(() => {
    if (allData.length > 0) {
      const counts = ProjectFilterManager.calculateFilterCounts(allData, filterStates);
      setFilterCounts(counts);
    }
  }, [filterStates, allData]);

  // 处理组织/细胞类型选择
  const handleTissueTypeChange = (tissueType: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      // 清空细胞系选择（互斥）
      newStates.selectedCellLines = [];
      newStates.selectedTissueTypes = [tissueType];
      
      // 清空疾病选择，让用户重新选择符合该组织类型的疾病
      newStates.selectedDiseases = [];
    } else {
      newStates.selectedTissueTypes = [];
    }
    
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 处理细胞系选择
  const handleCellLineChange = (cellLine: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      // 清空组织类型选择（互斥）
      newStates.selectedTissueTypes = [];
      newStates.selectedCellLines = [cellLine];
      
      // 清空疾病选择，让用户重新选择符合该细胞系的疾病
      newStates.selectedDiseases = [];
    } else {
      newStates.selectedCellLines = [];
    }
    
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 处理疾病选择
  const handleDiseaseChange = (disease: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      if (!newStates.selectedDiseases.includes(disease)) {
        newStates.selectedDiseases.push(disease);
      }
    } else {
      newStates.selectedDiseases = newStates.selectedDiseases.filter(d => d !== disease);
    }
    
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 重置过滤器
  const resetFilters = () => {
    const newStates = ProjectFilterManager.resetFilterStates();
    setFilterStates(newStates);
    applyFilters(newStates);
  };

  // 提交过滤器
  const submitFilters = () => {
    applyFilters(filterStates);
  };

  // 下载功能
  const downloadData = (format: 'csv' | 'xlsx' | 'json') => {
    const dataToDownload = filteredData.length > 0 ? filteredData : allData;
    
    if (format === 'json') {
      const jsonData = JSON.stringify(dataToDownload, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'projects.json';
      a.click();
      URL.revokeObjectURL(url);
    } else if (format === 'csv') {
      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const headers = ['DATASET ID', 'BIOPROJECT ID', 'GEO ACCESSION', 'DATE TYPE', 'TISSUE/CELL TYPE', 'CELL LINE', 'CONDITION', 'CATEGORY', 'SAMPLE NUMBER', 'PMID', 'TRANSLATED TRANSCRIPTS NUMBER', 'TRANSLATED GENES NUMBER'];
      const csvContent = [
        headers.join(','),
        ...dataToDownload.map(item => [
          escapeCsvValue(item.projectId),
          escapeCsvValue(item.bioProjectId),
          escapeCsvValue(item.geoAccession),
          escapeCsvValue(item.strategy),
          escapeCsvValue(item.tissueOrCellType),
          escapeCsvValue(item.cellLine),
          escapeCsvValue(item.disease),
          escapeCsvValue(item.diseaseCategory),
          escapeCsvValue(item.srrNumber),
          escapeCsvValue(item.pmid),
          escapeCsvValue(item.translatedTranscriptsNumber),
          escapeCsvValue(item.translatedGenesNumber)
        ].join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'projects.csv';
      a.click();
      URL.revokeObjectURL(url);
    }
    
    setShowDownloadOptions(false);
  };

  // 渲染过滤器选项
  const renderFilterOptions = (
    title: string,
    options: string[],
    selectedOptions: string[],
    showMore: boolean,
    onToggleMore: () => void,
    onOptionChange: (option: string, checked: boolean) => void,
    filterType: 'tissueTypes' | 'cellLines' | 'diseases'
  ) => {
    // 只显示有计数的选项
    const optionsWithCounts = options.filter(option => {
      const count = filterCounts[filterType][option] || 0;
      return count > 0;
    });

    const displayOptions = showMore ? optionsWithCounts : optionsWithCounts.slice(0, DEFAULT_DISPLAY_COUNT);

    return (
      <div className="mb-6">
        <h3 className="font-semibold text-gray-900 mb-4 text-base">{title}</h3>
        <div className="space-y-3">
          {displayOptions.map(option => {
            const count = filterCounts[filterType][option] || 0;
          return (
              <label key={option} className="flex items-center space-x-3 text-sm cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <input
                  type="checkbox"
                  checked={selectedOptions.includes(option)}
                  onChange={(e) => onOptionChange(option, e.target.checked)}
                  className="rounded border-gray-300 text-[#337ab7] focus:ring-[#337ab7] w-4 h-4"
                />
                <span className="text-gray-800 font-medium leading-relaxed">
                  {option} <span className="text-gray-500 font-normal">({count})</span>
                </span>
              </label>
            );
          })}
          {optionsWithCounts.length > DEFAULT_DISPLAY_COUNT && (
            <button
              onClick={onToggleMore}
              className="text-sm text-[#337ab7] hover:text-[#337ab7] underline font-medium ml-2 mt-1"
            >
              {showMore ? 'Show Less' : 'More'}
            </button>
          )}
              </div>
            </div>
          );
  };



  // 生成分页按钮
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
          
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (currentPage > 1) {
      pages.push(
                  <button 
          key="prev"
          onClick={() => setCurrentPage(currentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-[#0071BC] rounded-lg hover:bg-[#2B7FFF] transition-colors"
                  >
          Previous
                  </button>
      );
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
                  <button 
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            currentPage === i 
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]' 
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
                  </button>
          );
    }

    // Next button
    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setCurrentPage(currentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center mt-6">{pages}</div>;
  };

  // 获取过滤后的选项（只显示有数据的选项）
  const getFilteredOptions = () => {
    // 先根据当前选择过滤数据
    let workingData = allData;
  
    // 根据已选择的过滤器条件筛选数据
    if (filterStates.selectedTissueTypes.length > 0) {
      workingData = workingData.filter(item => 
        filterStates.selectedTissueTypes.includes(
          item.tissueOrCellType === 'nan' ? 'NA' : item.tissueOrCellType
        )
      );
    }

    if (filterStates.selectedCellLines.length > 0) {
      workingData = workingData.filter(item => 
        filterStates.selectedCellLines.includes(
          item.cellLine === 'nan' ? 'NA' : item.cellLine
        )
      );
    }

    if (filterStates.selectedDiseases.length > 0) {
      workingData = workingData.filter(item => 
        filterStates.selectedDiseases.includes(item.disease)
      );
    }

    // 从过滤后的数据中获取可用选项
    const tissueTypes = new Set<string>();
    const cellLines = new Set<string>();
    const diseases = new Set<string>();

    workingData.forEach(item => {
      const tissue = item.tissueOrCellType === 'nan' ? 'NA' : item.tissueOrCellType;
      const cellLine = item.cellLine === 'nan' ? 'NA' : item.cellLine;
      const disease = item.disease;

      tissueTypes.add(tissue);
      cellLines.add(cellLine);
      diseases.add(disease);
    });

    // 如果没有选择任何过滤器，显示所有选项
    if (filterStates.selectedTissueTypes.length === 0 && 
        filterStates.selectedCellLines.length === 0 && 
        filterStates.selectedDiseases.length === 0) {
      allData.forEach(item => {
        const tissue = item.tissueOrCellType === 'nan' ? 'NA' : item.tissueOrCellType;
        const cellLine = item.cellLine === 'nan' ? 'NA' : item.cellLine;
        const disease = item.disease;

        tissueTypes.add(tissue);
        cellLines.add(cellLine);
        diseases.add(disease);
      });
    }

    return {
      availableTissueTypes: Array.from(tissueTypes).sort(),
      availableCellLines: Array.from(cellLines).sort(),
      availableDiseases: Array.from(diseases).sort()
    };
  };

  const { availableTissueTypes, availableCellLines, availableDiseases } = getFilteredOptions();

  return (
    <div className="min-h-screen flex flex-col">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="link-breadcrumb">
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span className="link-breadcrumb-current">Browse Dataset</span>
        </div>
      </div>
      
      {/* 主内容区域 - 左右各2.5%空白 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
          <div className="flex gap-6">
            {/* 左侧过滤器 */}
            <div className="w-64 bg-white border border-gray-200 rounded-lg p-5 shadow-sm">
              {/* Tissue/Cell Type */}
              {renderFilterOptions(
                'Tissue/Cell Type',
                availableTissueTypes,
                filterStates.selectedTissueTypes,
                filterStates.showMoreTissue,
                () => setFilterStates(prev => ({ ...prev, showMoreTissue: !prev.showMoreTissue })),
                handleTissueTypeChange,
                'tissueTypes'
              )}

              {/* Cell Line */}
              {renderFilterOptions(
                'Cell Line',
                availableCellLines,
                filterStates.selectedCellLines,
                filterStates.showMoreCellLine,
                () => setFilterStates(prev => ({ ...prev, showMoreCellLine: !prev.showMoreCellLine })),
                handleCellLineChange,
                'cellLines'
              )}

              {/* Disease */}
              {renderFilterOptions(
                'Condition',
                availableDiseases,
                filterStates.selectedDiseases,
                filterStates.showMoreDisease,
                () => setFilterStates(prev => ({ ...prev, showMoreDisease: !prev.showMoreDisease })),
                handleDiseaseChange,
                'diseases'
              )}

              {/* 操作按钮 */}
              <div className="flex gap-3 mt-8 pt-4 border-t border-gray-200">
                <Button onClick={submitFilters} className="btn-submit-full">
                  Submit
                </Button>
                <Button onClick={resetFilters} variant="outline" className="btn-reset">
                  Reset
                </Button>
              </div>


            </div>
        
            {/* 右侧内容区域 */}
            <div className="flex-1" style={{ maxWidth: 'calc(100vw - 300px)', overflow: 'hidden' }}>
              {/* 顶部搜索和下载区域 */}
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <h1 className="text-xl font-semibold text-gray-800">
                  {loading ? 'Loading...' : `${filteredData.length} ${filteredData.length === 1 ? 'Dataset' : 'Datasets'} Found`}
                  </h1>
                </div>
                
                <div className="flex items-center space-x-4">
          <div className="flex">
            <Input
                      placeholder="Search"
              value={searchKeyword}
                      onChange={(e) => setSearchKeyword(e.target.value)}
                      className="h-10 w-80 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
            />
                  </div>
                  
                  <div className="relative">
                  <button
                    onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                    className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                    
                    {showDownloadOptions && (
                      <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="py-1">
                          <button
                            onClick={() => downloadData('csv')}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                          >
                            Download as CSV
                          </button>
                          <button
                            onClick={() => downloadData('json')}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                          >
                            Download as JSON
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
          </div>
        </div>
        
              {/* 表格容器 */}
              <div className="bg-white border border-gray-200 rounded-lg overflow-hidden" style={{ maxWidth: '100%' }}>
        {loading ? (
          <div className="p-6 text-center">
            <p className="text-gray-600">Loading data...</p>
          </div>
        ) : error ? (
          <div className="p-6 border-red-500 border-l-4">
            <p className="text-red-600">Error: {error}</p>
          </div>
                ) : (
                  <>
                    {/* 表格滚动容器 */}
                    <div
                      id="table-container"
                      className="table-scroll"
                      style={{
                        overflowX: 'auto',
                        overflowY: 'hidden',
                        position: 'relative',
                        scrollbarWidth: 'none', // Firefox
                        msOverflowStyle: 'none', // IE
                      }}
                    >
                      <table
                        className="w-full"
                        style={{
                          minWidth: '2400px', // 增加最小宽度确保所有13列都能显示
                          fontSize: '13px',
                          textAlign: 'center',
                          whiteSpace: 'nowrap',
                          tableLayout: 'auto' // 自动表格布局
                        }}
                      >
                        <thead className="bg-gray-50 border-b border-gray-200">
                          <tr>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('projectId')}
                              style={{ width: '120px', minWidth: '120px' }}
                            >
                              <div className="flex items-center justify-center">
                                <span>DATASET ID</span>
                                {renderSortIcon('projectId')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('bioProjectId')}
                            >
                              <div className="flex items-center justify-center">
                                <span>BioProject ID</span>
                                {renderSortIcon('bioProjectId')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('geoAccession')}
                            >
                              <div className="flex items-center justify-center">
                                <span>GEO Accession</span>
                                {renderSortIcon('geoAccession')}
                              </div>
                            </th>

                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('translatedTranscriptsNumber')}
                            >
                              <div className="flex items-center justify-center space-x-1">
                                <span>Translated Transcripts Number</span>
                                <div
                                  className="relative group"
                                  onMouseEnter={(e) => e.stopPropagation()}
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <Info className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600 cursor-help" />
                                  <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 px-2 py-1.5 bg-white border border-gray-200 text-gray-700 text-xs rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap normal-case">
                                    Click to enter the &lsquo;Transcriptome-wide distribution&rsquo; page
                                    <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-200" style={{ transform: 'translateY(1px)'}}></div>
                                    <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-white"></div>
                                  </div>
                                </div>
                                {renderSortIcon('translatedTranscriptsNumber')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('translatedGenesNumber')}
                            >
                              <div className="flex items-center justify-center space-x-1">
                                <span>Translated Genes Number</span>
                                <div
                                  className="relative group"
                                  onMouseEnter={(e) => e.stopPropagation()}
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <Info className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600 cursor-help" />
                                  <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 px-2 py-1.5 bg-white border border-gray-200 text-gray-700 text-xs rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap normal-case">
                                  Click to enter the &lsquo;Genome-wide distribution&rsquo; page
                                    <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-200" style={{ transform: 'translateY(1px)'}}></div>
                                    <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-white"></div>
                                  </div>
                                </div>
                                {renderSortIcon('translatedGenesNumber')}
                              </div>
                            </th>
                            
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('strategy')}
                            >
                              <div className="flex items-center justify-center">
                                <span>Date Type</span>
                                {renderSortIcon('strategy')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('tissueOrCellType')}
                            >
                              <div className="flex items-center justify-center">
                                <span>Tissue/Cell Type</span>
                                {renderSortIcon('tissueOrCellType')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('cellLine')}
                            >
                              <div className="flex items-center justify-center">
                                <span>Cell line</span>
                                {renderSortIcon('cellLine')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('disease')}
                            >
                              <div className="flex items-center justify-center">
                                <span>Condition</span>
                                {renderSortIcon('disease')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('diseaseCategory')}
                            >
                              <div className="flex items-center justify-center">
                                <span>CATEGORY</span>
                                {renderSortIcon('diseaseCategory')}
                              </div>
                            </th>
                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('srrNumber')}
                            >
                              <div className="flex items-center justify-center">
                                <span>Sample Number</span>
                                {renderSortIcon('srrNumber')}
                              </div>
                            </th>

                            <th
                              className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => handleSort('pmid')}
                            >
                              <div className="flex items-center justify-center">
                                <span>PMID</span>
                                {renderSortIcon('pmid')}
                              </div>
                            </th>

                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {currentData.map((item, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm text-center">
                                {item.projectId && item.projectId !== 'nan' && item.projectId !== 'null' ? item.projectId : 'NA'}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.bioProjectId && item.bioProjectId !== 'nan' && item.bioProjectId !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/bioproject/${item.bioProjectId}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.bioProjectId}
                                  </a>
                                ) : (
                                  'NA'
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.geoAccession && item.geoAccession !== 'nan' && item.geoAccession !== 'null' ? (
                                  <a
                                    href={`https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=${item.geoAccession}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.geoAccession}
                                  </a>
                                ) : (
                                  'NA'
                                )}
                              </td>

                              <td className="px-4 py-3 text-sm text-center">
                                {item.translatedTranscriptsNumber && !isNaN(item.translatedTranscriptsNumber) && item.translatedTranscriptsNumber !== null ? (
                                  <Link
                                    href={`/browse/dataset/transcript-analysis/${item.projectId}`}
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.translatedTranscriptsNumber.toLocaleString()}
                                  </Link>
                                ) : (
                                  'NA'
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.translatedGenesNumber && !isNaN(item.translatedGenesNumber) && item.translatedGenesNumber !== null ? (
                                  <Link
                                    href={`/browse/dataset/gene-analysis/${item.projectId}`}
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.translatedGenesNumber.toLocaleString()}
                                  </Link>
                                ) : (
                                  'NA'
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm text-center">{item.strategy && item.strategy !== 'nan' && item.strategy !== 'null' ? item.strategy : 'NA'}</td>
                              <td className="px-4 py-3 text-sm text-center">{item.tissueOrCellType && item.tissueOrCellType !== 'nan' && item.tissueOrCellType !== 'null' ? item.tissueOrCellType : 'NA'}</td>
                              <td className="px-4 py-3 text-sm text-center">{item.cellLine && item.cellLine !== 'nan' && item.cellLine !== 'null' ? item.cellLine : 'NA'}</td>
                              <td className="px-4 py-3 text-sm text-center">{item.disease && item.disease !== 'nan' && item.disease !== 'null' ? item.disease : 'NA'}</td>
                              <td className="px-4 py-3 text-sm text-center">{item.diseaseCategory && item.diseaseCategory !== 'nan' && item.diseaseCategory !== 'null' ? item.diseaseCategory : 'NA'}</td>
                              <td className="px-4 py-3 text-sm text-center">{item.srrNumber && String(item.srrNumber) !== 'nan' && String(item.srrNumber) !== 'null' ? item.srrNumber : 'NA'}</td>
                              <td className="px-4 py-3 text-sm text-center">
                                {item.pmid && String(item.pmid) !== 'nan' && String(item.pmid) !== 'null' ? (
                                  <a
                                    href={`https://pubmed.ncbi.nlm.nih.gov/${item.pmid}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                    style={{ color: '#337ab7' }}
                                  >
                                    {item.pmid}
                                  </a>
                                ) : 'NA'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      </div>

                      {/* 自定义水平滚动条 - 始终显示 */}
                      <div
                        className="bg-gray-100 border-t border-gray-200"
                        style={{
                          height: '16px',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                      >
                        <div
                          id="custom-scrollbar-track"
                          className="w-full h-full bg-gray-100 cursor-pointer"
                          style={{
                            position: 'relative'
                          }}
                        >
                          <div
                            id="custom-scrollbar-thumb"
                            className="bg-gray-400 hover:bg-gray-500 rounded"
                            style={{
                              height: '12px',
                              width: '80px',
                              position: 'absolute',
                              top: '2px',
                              left: '0px',
                              cursor: 'grab',
                              transition: 'background-color 0.2s'
                            }}
                          ></div>
                        </div>
                      </div>

                    {/* 分页和控制 */}
                    <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-6">
                          <span className="text-sm font-medium text-gray-700">
                            Showing {startIndex + 1} to {Math.min(endIndex, filteredData.length)} of {filteredData.length} rows
                          </span>
                          <div className="flex items-center space-x-2">
                            <label className="text-sm font-medium text-gray-700">
                              <select
                                value={pageSize}
                                onChange={(e) => {
                                  setPageSize(Number(e.target.value));
                                  setCurrentPage(1);
                                }}
                                className="ml-2 mr-2 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#0071BC] focus:border-transparent"
                              >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                              </select>
                              rows per page
                            </label>
                          </div>
                        </div>
                        
                        {renderPagination()}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-4">
        <Footer />
      </div>
      
      {/* 点击其他地方关闭下载菜单 */}
      {showDownloadOptions && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowDownloadOptions(false)}
        />
      )}
    </div>
  );
}

export default function ProjectPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProjectPageContent />
    </Suspense>
  );
}