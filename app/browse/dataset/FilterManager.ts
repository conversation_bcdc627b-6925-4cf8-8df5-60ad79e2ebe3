// 项目数据类型
export interface Project {
  projectId: string;
  bioProjectId: string;
  geoAccession: string;
  title: string;
  strategy: string;
  tissueOrCellType: string;
  cellLine: string;
  disease: string;
  diseaseCategory: string;
  srrNumber: number;
  pmid: string;
  translatedTranscriptsNumber: number;
  translatedGenesNumber: number;
}

// 过滤器相关类型
export interface FilterCounts {
  tissueTypes: Record<string, number>;
  cellLines: Record<string, number>;
  diseases: Record<string, number>;
}

export interface FilterRelations {
  tissueToDisease: Record<string, string[]>;
  cellLineToDisease: Record<string, string[]>;
  diseaseToTissue: Record<string, string[]>;
  diseaseToCellLine: Record<string, string[]>;
}

export interface FilterStates {
  selectedTissueTypes: string[];
  selectedCellLines: string[];
  selectedDiseases: string[];
  showMoreTissue: boolean;
  showMoreCellLine: boolean;
  showMoreDisease: boolean;
}

export class ProjectFilterManager {
  private static readonly STORAGE_KEY = 'project_filter_relations';

  // 从本地存储加载关系数据
  static loadFilterRelationsFromStorage(): FilterRelations | null {
    try {
      if (typeof window === 'undefined') return null;
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  // 保存关系数据到本地存储
  static saveFilterRelationsToStorage(relations: FilterRelations): void {
    try {
      if (typeof window === 'undefined') return;
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(relations));
    } catch (error) {
      console.error('Failed to save filter relations to localStorage:', error);
    }
  }

  // 构建过滤器关系数据
  static buildFilterRelations(data: Project[]): FilterRelations {
    const relations: FilterRelations = {
      tissueToDisease: {},
      cellLineToDisease: {},
      diseaseToTissue: {},
      diseaseToCellLine: {}
    };

    data.forEach(item => {
      const tissue = this.normalizeTissueType(item.tissueOrCellType);
      const cellLine = this.normalizeCellLine(item.cellLine);
      const disease = this.normalizeDisease(item.disease);

      // Tissue to Disease
      if (!relations.tissueToDisease[tissue]) {
        relations.tissueToDisease[tissue] = [];
      }
      if (!relations.tissueToDisease[tissue].includes(disease)) {
        relations.tissueToDisease[tissue].push(disease);
      }

      // CellLine to Disease
      if (!relations.cellLineToDisease[cellLine]) {
        relations.cellLineToDisease[cellLine] = [];
      }
      if (!relations.cellLineToDisease[cellLine].includes(disease)) {
        relations.cellLineToDisease[cellLine].push(disease);
      }

      // Disease to Tissue
      if (!relations.diseaseToTissue[disease]) {
        relations.diseaseToTissue[disease] = [];
      }
      if (!relations.diseaseToTissue[disease].includes(tissue)) {
        relations.diseaseToTissue[disease].push(tissue);
      }

      // Disease to CellLine
      if (!relations.diseaseToCellLine[disease]) {
        relations.diseaseToCellLine[disease] = [];
      }
      if (!relations.diseaseToCellLine[disease].includes(cellLine)) {
        relations.diseaseToCellLine[disease].push(cellLine);
      }
    });

    // 排序所有数组
    Object.keys(relations.tissueToDisease).forEach(key => {
      relations.tissueToDisease[key].sort();
    });
    Object.keys(relations.cellLineToDisease).forEach(key => {
      relations.cellLineToDisease[key].sort();
    });
    Object.keys(relations.diseaseToTissue).forEach(key => {
      relations.diseaseToTissue[key].sort();
    });
    Object.keys(relations.diseaseToCellLine).forEach(key => {
      relations.diseaseToCellLine[key].sort();
    });

    return relations;
  }

  // 计算过滤器计数
  static calculateFilterCounts(data: Project[], currentFilters: FilterStates): FilterCounts {
    let workingData = data;

    // 根据当前选择的过滤器筛选数据
    if (currentFilters.selectedTissueTypes.length > 0) {
      workingData = workingData.filter(item => 
        currentFilters.selectedTissueTypes.includes(this.normalizeTissueType(item.tissueOrCellType))
      );
    }

    if (currentFilters.selectedCellLines.length > 0) {
      workingData = workingData.filter(item => 
        currentFilters.selectedCellLines.includes(this.normalizeCellLine(item.cellLine))
      );
    }

    if (currentFilters.selectedDiseases.length > 0) {
      workingData = workingData.filter(item => 
        currentFilters.selectedDiseases.includes(this.normalizeDisease(item.disease))
      );
    }

    const counts: FilterCounts = {
      tissueTypes: {},
      cellLines: {},
      diseases: {}
    };

    workingData.forEach(item => {
      const tissue = this.normalizeTissueType(item.tissueOrCellType);
      const cellLine = this.normalizeCellLine(item.cellLine);
      const disease = this.normalizeDisease(item.disease);

      counts.tissueTypes[tissue] = (counts.tissueTypes[tissue] || 0) + 1;
      counts.cellLines[cellLine] = (counts.cellLines[cellLine] || 0) + 1;
      counts.diseases[disease] = (counts.diseases[disease] || 0) + 1;
    });

    return counts;
  }

  // 获取可用的过滤器选项
  static getAvailableOptions(
    filterCounts: FilterCounts, 
    filterStates: FilterStates, 
    filterRelations: FilterRelations
  ) {
    let availableTissueTypes = Object.keys(filterCounts.tissueTypes).sort();
    let availableCellLines = Object.keys(filterCounts.cellLines).sort();
    let availableDiseases = Object.keys(filterCounts.diseases).sort();

    // 如果选择了疾病，限制组织类型和细胞系选项
    if (filterStates.selectedDiseases.length > 0) {
      const relatedTissues = new Set<string>();
      const relatedCellLines = new Set<string>();
      
      filterStates.selectedDiseases.forEach(disease => {
        (filterRelations.diseaseToTissue[disease] || []).forEach(tissue => relatedTissues.add(tissue));
        (filterRelations.diseaseToCellLine[disease] || []).forEach(cellLine => relatedCellLines.add(cellLine));
      });
      
      availableTissueTypes = Array.from(relatedTissues).sort();
      availableCellLines = Array.from(relatedCellLines).sort();
    }

    // 如果选择了组织类型，限制疾病选项
    if (filterStates.selectedTissueTypes.length > 0) {
      const relatedDiseases = new Set<string>();
      filterStates.selectedTissueTypes.forEach(tissue => {
        (filterRelations.tissueToDisease[tissue] || []).forEach(disease => relatedDiseases.add(disease));
      });
      availableDiseases = Array.from(relatedDiseases).sort();
    }

    // 如果选择了细胞系，限制疾病选项
    if (filterStates.selectedCellLines.length > 0) {
      const relatedDiseases = new Set<string>();
      filterStates.selectedCellLines.forEach(cellLine => {
        (filterRelations.cellLineToDisease[cellLine] || []).forEach(disease => relatedDiseases.add(disease));
      });
      availableDiseases = Array.from(relatedDiseases).sort();
    }

    return { availableTissueTypes, availableCellLines, availableDiseases };
  }

  // 应用过滤器（不包含搜索）
  static applyFilterOnly(
    allData: Project[], 
    filterStates: FilterStates
  ): Project[] {
    let filtered = allData;

    // 应用过滤器
    if (filterStates.selectedTissueTypes.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedTissueTypes.includes(this.normalizeTissueType(item.tissueOrCellType))
      );
    }

    if (filterStates.selectedCellLines.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedCellLines.includes(this.normalizeCellLine(item.cellLine))
      );
    }

    if (filterStates.selectedDiseases.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedDiseases.includes(this.normalizeDisease(item.disease))
      );
    }

    return filtered;
  }

  // 应用过滤器（包含搜索）
  static applyFilters(
    allData: Project[], 
    searchKeyword: string, 
    filterStates: FilterStates
  ): Project[] {
    let filtered = allData;

    // 应用搜索（仅搜索表格中显示的字段）
    if (searchKeyword.trim()) {
      // 定义表格中实际显示的字段
      const searchableFields: (keyof Project)[] = [
        'projectId',
        'bioProjectId', 
        'geoAccession',
        'translatedTranscriptsNumber',
        'translatedGenesNumber',
        'strategy',
        'tissueOrCellType',
        'cellLine',
        'disease',
        'diseaseCategory',
        'srrNumber',
        'pmid'
      ];
      
      filtered = filtered.filter(item =>
        searchableFields.some(field => {
          const value = item[field];
          return value?.toString().toLowerCase().includes(searchKeyword.toLowerCase());
        })
      );
    }

    // 应用过滤器
    if (filterStates.selectedTissueTypes.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedTissueTypes.includes(this.normalizeTissueType(item.tissueOrCellType))
      );
    }

    if (filterStates.selectedCellLines.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedCellLines.includes(this.normalizeCellLine(item.cellLine))
      );
    }

    if (filterStates.selectedDiseases.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedDiseases.includes(this.normalizeDisease(item.disease))
      );
    }

    return filtered;
  }

  // 标准化组织类型
  private static normalizeTissueType(tissue: string): string {
    if (!tissue || tissue === 'nan' || tissue === 'null' || tissue === 'undefined') {
      return 'NA';
    }
    return tissue.trim();
  }

  // 标准化细胞系
  private static normalizeCellLine(cellLine: string): string {
    if (!cellLine || cellLine === 'nan' || cellLine === 'null' || cellLine === 'undefined') {
      return 'NA';
    }
    return cellLine.trim();
  }

  // 标准化疾病
  private static normalizeDisease(disease: string): string {
    if (!disease || disease === 'nan' || disease === 'null' || disease === 'undefined') {
      return 'Normal';
    }
    return disease.trim();
  }

  // 创建初始过滤器状态
  static createInitialFilterStates(): FilterStates {
    return {
      selectedTissueTypes: [],
      selectedCellLines: [],
      selectedDiseases: [],
      showMoreTissue: false,
      showMoreCellLine: false,
      showMoreDisease: false
    };
  }

  // 重置过滤器状态
  static resetFilterStates(): FilterStates {
    return this.createInitialFilterStates();
  }
} 