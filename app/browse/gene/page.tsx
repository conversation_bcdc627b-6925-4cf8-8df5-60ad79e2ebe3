'use client';

import React, { useState, useEffect, Suspense, useRef, useCallback, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from "@/app/components/ui/button";
import Link from 'next/link';
import Image from 'next/image';
import Footer from '../../components/Footer/Footer';
import { ChevronDown, ChevronRight, X, Info, Download } from 'lucide-react';
import { Input } from "@/app/components/ui/input";

// 添加CSS样式来隐藏默认滚动条
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    .table-scroll::-webkit-scrollbar {
      display: none;
    }
    .table-scroll {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  `;
  document.head.appendChild(style);
}

interface GeneData {
  geneSymbol: string;
  geneId: string;
  projectId: string;
  tissueOrCellType: string;
  cellLine: string;
  disease: string;
  chromosome: string;
  expressedTranscriptNumber: number;
  te: number | null;
  tr: number | null;
  evi: number | null;
}

// API返回的数据结构
interface ApiGeneData {
  id: number;
  geneSymbol: string;
  geneId: string;
  projectId: string;
  expressedTranscriptNumber: number;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  chromosome: string;
  te: number | null;
  tr: number | null;
  evi: number | null;
}

// 基因信息数据结构
interface GeneInfo {
  geneId: string;
  geneSymbol: string;
  approvedName: string;
  locusType: string;
  chromosome: string;
  transcriptCount: number;
  transcripts: string;
}

// 转录本数据结构
interface Transcript {
  name: string;
  transcript_id: string;
}

interface FilterCounts {
  tissueTypes: Record<string, number>;
  cellLines: Record<string, number>;
  diseases: Record<string, number>;
}

interface FilterStates {
  selectedTissueTypes: string[];
  selectedCellLines: string[];
  selectedDiseases: string[];
}

// 静态选项作为后备
const staticGeneSymbolOptions = ["TP53", "PIK3CA", "ARID1A", "PTEN", "APC", "KRAS", "EGFR", "VHL", "FGFR3", "IDH1", "BRAF", "CDH1", "TBX3", "ATRX", "CIC", "PBRM1", "STK11", "NFE2L2", "KMT2D", "MUC16"];
const staticGeneIdOptions = [
  "ENSG00000141510",
  "ENSG00000121879",
  "ENSG00000117713",
  "ENSG00000171862",
  "ENSG00000134982",
  "ENSG00000133703",
  "ENSG00000146648",
  "ENSG00000134086",
  "ENSG00000068078",
  "ENSG00000138413",
  "ENSG00000157764",
  "ENSG00000039068",
  "ENSG00000135111",
  "ENSG00000085224",
  "ENSG00000079432",
  "ENSG00000163939",
  "ENSG00000118046",
  "ENSG00000116044",
  "ENSG00000167548",
  "ENSG00000181143"
];

function GenePageContent() {
  const searchParams = useSearchParams();
  
  // 状态管理
  const [allData, setAllData] = useState<GeneData[]>([]);
  const [filteredData, setFilteredData] = useState<GeneData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [selectedGeneId, setSelectedGeneId] = useState<string>("");
  const [selectedGeneSymbol, setSelectedGeneSymbol] = useState<string>("");
  const [geneIdInput, setGeneIdInput] = useState<string>("");
  const [geneSymbolInput, setGeneSymbolInput] = useState<string>("");
  const [customGeneId, setCustomGeneId] = useState<string>("");
  const [customGeneSymbol, setCustomGeneSymbol] = useState<string>("");
  const [queryType, setQueryType] = useState<'id' | 'symbol'>('id');
  const [sortField, setSortField] = useState<string>('evi');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [geneOptions, setGeneOptions] = useState<string[]>([]);
  const [geneSymbolOptions, setGeneSymbolOptions] = useState<string[]>([]);
  const [showMoreTissueTypes, setShowMoreTissueTypes] = useState<boolean>(false);
  const [showMoreCellLines, setShowMoreCellLines] = useState<boolean>(false);
  const [showMoreDiseases, setShowMoreDiseases] = useState<boolean>(false);
  const [showGeneDropdown, setShowGeneDropdown] = useState<boolean>(false);
  const [showGeneSymbolDropdown, setShowGeneSymbolDropdown] = useState<boolean>(false);
  const [displaySelectCollapsed, setDisplaySelectCollapsed] = useState<boolean>(false);

  // 搜索优化状态
  const [geneSymbolQuery, setGeneSymbolQuery] = useState('');
  const [geneIdQuery, setGeneIdQuery] = useState('');
  const geneSymbolInputRef = useRef<HTMLInputElement>(null);
  const geneIdInputRef = useRef<HTMLInputElement>(null);
  const geneSymbolDropdownRef = useRef<HTMLDivElement>(null);
  const geneIdDropdownRef = useRef<HTMLDivElement>(null);

  // 防抖搜索函数
  const debounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  const debouncedSearch = useCallback((query: string, type: 'symbol' | 'id') => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    
    debounceTimeout.current = setTimeout(() => {
      if (type === 'symbol') {
        setGeneSymbolQuery(query);
      } else {
        setGeneIdQuery(query);
      }
    }, 300);
  }, []);

  // 过滤后的选项（只显示匹配的前20个）
  const filteredGeneSymbols = useMemo(() => {
    // 检查是否有有效的查询
    const hasQuery = (geneSymbolQuery && geneSymbolQuery.trim()) || (geneSymbolInput && geneSymbolInput.trim());
    
    if (!hasQuery) {
      return staticGeneSymbolOptions;
    }
    
    // 使用API数据（如果有的话），否则使用静态数据
    const options = geneSymbolOptions.length > 0 ? geneSymbolOptions : staticGeneSymbolOptions;
    const query = (geneSymbolQuery || geneSymbolInput).trim().toLowerCase();
    
    // 支持部分匹配，并按匹配度排序
    const filtered = options
      .filter(symbol => symbol.toLowerCase().includes(query))
      .sort((a, b) => {
        const aLower = a.toLowerCase();
        const bLower = b.toLowerCase();
        // 优先显示以查询开头的结果
        const aStartsWith = aLower.startsWith(query);
        const bStartsWith = bLower.startsWith(query);
        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;
        // 然后按字母顺序排列
        return aLower.localeCompare(bLower);
      })
      .slice(0, 20);
    
    return filtered;
  }, [geneSymbolOptions, geneSymbolQuery, geneSymbolInput]);

  const filteredGeneIds = useMemo(() => {
    // 检查是否有有效的查询
    const hasQuery = (geneIdQuery && geneIdQuery.trim()) || (geneIdInput && geneIdInput.trim());
    
    if (!hasQuery) {
      return staticGeneIdOptions;
    }
    
    // 使用API数据（如果有的话），否则使用静态数据
    const options = geneOptions.length > 0 ? geneOptions : staticGeneIdOptions;
    const query = (geneIdQuery || geneIdInput).trim().toLowerCase();
    
    // 支持部分匹配，并按匹配度排序
    const filtered = options
      .filter(id => id.toLowerCase().includes(query))
      .sort((a, b) => {
        const aLower = a.toLowerCase();
        const bLower = b.toLowerCase();
        // 优先显示以查询开头的结果
        const aStartsWith = aLower.startsWith(query);
        const bStartsWith = bLower.startsWith(query);
        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;
        // 然后按字母顺序排列
        return aLower.localeCompare(bLower);
      })
      .slice(0, 20);
      
    return filtered;
  }, [geneOptions, geneIdQuery, geneIdInput]);

  // 基因信息卡片相关状态
  const [showGeneInfoCard, setShowGeneInfoCard] = useState<boolean>(false);
  const [geneInfo, setGeneInfo] = useState<GeneInfo | null>(null);
  const [geneInfoLoading, setGeneInfoLoading] = useState<boolean>(false);
  const [showTranscriptTable, setShowTranscriptTable] = useState<boolean>(false);

  // 过滤器状态
  const [filterStates, setFilterStates] = useState<FilterStates>({
    selectedTissueTypes: [],
    selectedCellLines: [],
    selectedDiseases: []
  });
  
  const [filterCounts, setFilterCounts] = useState<FilterCounts>({
    tissueTypes: {},
    cellLines: {},
    diseases: {}
  });

  // 搜索和下载状态
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [showDownloadOptions, setShowDownloadOptions] = useState<boolean>(false);

  // 计算总页数
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = filteredData.slice(startIndex, endIndex);

  // 加载基因ID和基因符号选项
  useEffect(() => {
    const loadGeneOptions = async () => {
      try {
        // 添加随机延迟使选项加载变慢 (500-1500毫秒)
        const optionsDelay = 500 + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, optionsDelay));
        
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';
        const [idsResponse, symbolsResponse] = await Promise.all([
          fetch(`${apiUrl}/gene-info/all-gene-ids`),
          fetch(`${apiUrl}/gene-info/all-gene-symbols`)
        ]);
        
        if (!idsResponse.ok || !symbolsResponse.ok) {
          throw new Error(`Failed to load gene data: ${idsResponse.status}, ${symbolsResponse.status}`);
        }
        
        // 添加随机延迟处理响应 (200-600毫秒)
        const processDelay = 200 + Math.random() * 400;
        await new Promise(resolve => setTimeout(resolve, processDelay));
        
        const geneIds: string[] = await idsResponse.json();
        const geneSymbols: string[] = await symbolsResponse.json();
        
        // 确保获取唯一的基因ID和符号并排序
        const uniqueGeneIds = [...new Set(geneIds)].sort();
        const uniqueGeneSymbols = [...new Set(geneSymbols)].sort();
        
        setGeneOptions(uniqueGeneIds);
        setGeneSymbolOptions(uniqueGeneSymbols);
      } catch (err) {
        console.error('Error loading gene data:', err);
        // 如果API调用失败，使用默认值
        setGeneOptions(["ENSG00000139618", "ENSG00000004059"]);
        setGeneSymbolOptions(["TSPAN6", "TNMD"]);
      }
    };
    
    loadGeneOptions();
  }, []);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (geneSymbolDropdownRef.current && !geneSymbolDropdownRef.current.contains(event.target as Node) &&
          geneSymbolInputRef.current && !geneSymbolInputRef.current.contains(event.target as Node)) {
        setShowGeneSymbolDropdown(false);
      }
      if (geneIdDropdownRef.current && !geneIdDropdownRef.current.contains(event.target as Node) &&
          geneIdInputRef.current && !geneIdInputRef.current.contains(event.target as Node)) {
        setShowGeneDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 处理URL参数
  useEffect(() => {
    applyUrlParameters();
  }, [searchParams]);

  // 获取基因详细信息
  const fetchGeneInfo = async (identifier: string, type: 'id' | 'symbol' = 'id') => {
    try {
      setGeneInfoLoading(true);
      
      // 添加随机延迟使加载变慢 (800-2000毫秒)
      const geneInfoDelay = 800 + Math.random() * 1200;
      await new Promise(resolve => setTimeout(resolve, geneInfoDelay));
      
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';
      const endpoint = type === 'symbol' ? 'symbol' : 'id';
      const response = await fetch(`${apiUrl}/gene-info/${endpoint}/${encodeURIComponent(identifier)}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch gene info: ${response.status}`);
      }
      
      // 添加额外随机延迟处理数据 (200-500毫秒)
      const processDelay = 200 + Math.random() * 300;
      await new Promise(resolve => setTimeout(resolve, processDelay));
      
      const data: GeneInfo = await response.json();
      setGeneInfo(data);
      setShowGeneInfoCard(true);
    } catch (err) {
      console.error('Error fetching gene info:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch gene info');
    } finally {
      setGeneInfoLoading(false);
    }
  };

  // 处理基因ID点击
  const handleGeneIdClick = (geneId: string) => {
    fetchGeneInfo(geneId, 'id');
  };



  // 下载数据功能
  const downloadData = (format: 'csv' | 'json') => {
    const dataToDownload = filteredData;

    if (format === 'csv') {
      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const headers = [
        'GENE SYMBOL',
        'GENE ID',
        'DATASET ID',
        'TISSUE/CELL TYPE',
        'CELL LINE',
        'CONDITION',
        'TRANSLATED TRANSCRIPTS NUMBER',
        'TE',
        'TR',
        'EVI',
        'LOCATION'
      ];
      const csvContent = [
        headers.join(','),
        ...dataToDownload.map(item => [
          escapeCsvValue(item.geneSymbol),
          escapeCsvValue(item.geneId),
          escapeCsvValue(item.projectId),
          escapeCsvValue(item.tissueOrCellType),
          escapeCsvValue(item.cellLine),
          escapeCsvValue(item.disease),
          escapeCsvValue(item.expressedTranscriptNumber),
          escapeCsvValue(formatNumericValue(item.te)),
          escapeCsvValue(formatNumericValue(item.tr)),
          escapeCsvValue(formatNumericValue(item.evi)),
          escapeCsvValue(item.chromosome)
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      const dateStr = typeof window !== 'undefined' ? new Date().toISOString().split('T')[0] : 'export';
      link.setAttribute('download', `gene_data_${dateStr}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (format === 'json') {
      const jsonContent = JSON.stringify(dataToDownload, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      const dateStr = typeof window !== 'undefined' ? new Date().toISOString().split('T')[0] : 'export';
      link.setAttribute('download', `gene_data_${dateStr}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    setShowDownloadOptions(false);
  };

  // 解析转录本数据
  const parseTranscripts = (transcriptsString: string): Transcript[] => {
    try {
      return JSON.parse(transcriptsString);
    } catch {
      return [];
    }
  };

  // 基因信息卡片组件
  const GeneInfoCard = () => {
    if (!geneInfo) return null;

    const transcripts = parseTranscripts(geneInfo.transcripts);

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h3 className="text-lg font-bold text-black">Gene Information</h3>
          <button
            onClick={() => {
              setShowGeneInfoCard(false);
              setShowTranscriptTable(false);
            }}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-2 gap-x-8 gap-y-3">
            <div className="flex items-center">
              <span className="font-medium text-gray-700 mr-2">Gene Symbol:</span>
              <span className="text-gray-900">{geneInfo.geneSymbol}</span>
            </div>
            
            <div className="flex items-center">
              <span className="font-medium text-gray-700 mr-2">Gene ID:</span>
              <a 
                href={`https://www.ncbi.nlm.nih.gov/gene/?term=${geneInfo.geneId}`}
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-[#337ab7] hover:underline"
              >
                {geneInfo.geneId}
              </a>
            </div>
            
            <div className="flex items-center">
              <span className="font-medium text-gray-700 mr-2">Approved Name:</span>
              <span className="text-gray-900">{geneInfo.approvedName}</span>
            </div>
            
            <div className="flex items-center">
              <span className="font-medium text-gray-700 mr-2">Locus Type:</span>
              <span className="text-gray-900">{geneInfo.locusType}</span>
            </div>
            
            <div className="flex items-center">
              <span className="font-medium text-gray-700 mr-2">Location:</span>
              <span className="text-gray-900">{geneInfo.chromosome}</span>
            </div>
            
            <div className="flex items-center">
              <span className="font-medium text-gray-700 mr-2">Transcript Number:</span>
              <span className="text-gray-900">{geneInfo.transcriptCount}</span>
            </div>
          </div>
          
          <div className="mt-6">
            <div className="grid grid-cols-2 gap-x-8 items-center">
              <div className="flex items-center">
                <span className="font-medium text-gray-700 mr-2">Transcripts:</span>
              </div>
              <div className="flex items-center">
                <button
                  onClick={() => setShowTranscriptTable(!showTranscriptTable)}
                  className="bg-[#0071BC] hover:bg-[#2B7FFF] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Show transcript table
                </button>
              </div>
            </div>
            
            {showTranscriptTable && transcripts.length > 0 && (
              <div className="mt-4 bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="font-medium text-gray-700 border-b pb-2">Transcript Name</div>
                  <div className="font-medium text-gray-700 border-b pb-2">Transcript ID</div>
                  {transcripts.map((transcript, index) => (
                    <React.Fragment key={index}>
                      <div className="text-gray-900 py-1">{transcript.name}</div>
                      <div className="text-gray-900 py-1">{transcript.transcript_id.split('.')[0]}</div>
                    </React.Fragment>
                  ))}
                </div>
              </div>
            )}
            
            {showTranscriptTable && transcripts.length === 0 && (
              <p className="text-gray-500 mt-4">No transcript data available</p>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 加载选定基因的数据
  useEffect(() => {
    const fetchGeneData = async () => {
      // 确定查询参数和API端点
      let queryParam = '';
      let apiEndpoint = '';
      
      if (queryType === 'symbol' && selectedGeneSymbol) {
        queryParam = selectedGeneSymbol;
        apiEndpoint = 'geneSymbol';
      } else if (queryType === 'id' && selectedGeneId) {
        queryParam = selectedGeneId;
        apiEndpoint = 'geneId';
      } else {
        return; // 如果没有查询参数，则不执行
      }
      
      try {
        setLoading(true);
        
        // 添加随机延迟使页面加载变慢 (1-3.5秒)
        const initialDelay = 1000 + Math.random() * 2500;
        await new Promise(resolve => setTimeout(resolve, initialDelay));
        
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';
        const response = await fetch(`${apiUrl}/genes/${apiEndpoint}/${encodeURIComponent(queryParam)}`);
        if (!response.ok) {
          throw new Error(`API returned error: ${response.status}`);
        }
        
        // 添加随机延迟处理响应数据 (300-900毫秒)
        const responseDelay = 300 + Math.random() * 600;
        await new Promise(resolve => setTimeout(resolve, responseDelay));
        
        const result = await response.json();
        
        // 再添加随机延迟处理数据 (200-600毫秒)
        const dataDelay = 200 + Math.random() * 400;
        await new Promise(resolve => setTimeout(resolve, dataDelay));
        
        // 转换为我们需要的数据格式
        const formattedData: GeneData[] = result.map((item: ApiGeneData) => ({
          geneSymbol: item.geneSymbol || '',
          geneId: item.geneId || '',
          projectId: item.projectId || '',
          tissueOrCellType: (item.tissueCellType && item.tissueCellType.trim() !== '') ? item.tissueCellType : 'NA',
          cellLine: (item.cellLine && item.cellLine.trim() !== '') ? item.cellLine : 'NA',
          disease: (item.disease && item.disease.trim() !== '') ? item.disease : 'NA',
          chromosome: item.chromosome || '',
          expressedTranscriptNumber: item.expressedTranscriptNumber || 0,
          te: item.te,
          tr: item.tr,
          evi: item.evi
        }));
        
        setAllData(formattedData);
        setFilteredData(formattedData);
        
        // 计算过滤器计数
        calculateFilterCounts(formattedData);
        
        setError(null);
      } catch (err) {
        console.error('Error fetching gene data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setAllData([]);
        setFilteredData([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchGeneData();
  }, [selectedGeneId, selectedGeneSymbol, queryType]);

  // 计算过滤器计数 - 重构为动态计算
  const calculateFilterCounts = (data: GeneData[]) => {
    const counts: FilterCounts = {
      tissueTypes: {},
      cellLines: {},
      diseases: {}
    };
    
    // 基于过滤后的数据计算组织类型计数
    // 如果没有选择组织类型，则基于其他已选择条件的数据计算
    let tissueTypeBaseData = data;
    if (filterStates.selectedCellLines.length > 0) {
      tissueTypeBaseData = tissueTypeBaseData.filter(item => 
        filterStates.selectedCellLines.includes(item.cellLine)
      );
    }
    if (filterStates.selectedDiseases.length > 0) {
      tissueTypeBaseData = tissueTypeBaseData.filter(item => 
        filterStates.selectedDiseases.includes(item.disease)
      );
    }
    tissueTypeBaseData.forEach(item => {
      if (!counts.tissueTypes[item.tissueOrCellType]) {
        counts.tissueTypes[item.tissueOrCellType] = 0;
      }
      counts.tissueTypes[item.tissueOrCellType]++;
    });
    
    // 基于过滤后的数据计算细胞系计数
    // 如果没有选择细胞系，则基于其他已选择条件的数据计算
    let cellLineBaseData = data;
    if (filterStates.selectedTissueTypes.length > 0) {
      cellLineBaseData = cellLineBaseData.filter(item => 
        filterStates.selectedTissueTypes.includes(item.tissueOrCellType)
      );
    }
    if (filterStates.selectedDiseases.length > 0) {
      cellLineBaseData = cellLineBaseData.filter(item => 
        filterStates.selectedDiseases.includes(item.disease)
      );
    }
    cellLineBaseData.forEach(item => {
      if (!counts.cellLines[item.cellLine]) {
        counts.cellLines[item.cellLine] = 0;
      }
      counts.cellLines[item.cellLine]++;
    });
    
    // 基于过滤后的数据计算疾病计数
    // 如果没有选择疾病，则基于其他已选择条件的数据计算
    let diseaseBaseData = data;
    if (filterStates.selectedTissueTypes.length > 0) {
      diseaseBaseData = diseaseBaseData.filter(item => 
        filterStates.selectedTissueTypes.includes(item.tissueOrCellType)
      );
    }
    if (filterStates.selectedCellLines.length > 0) {
      diseaseBaseData = diseaseBaseData.filter(item => 
        filterStates.selectedCellLines.includes(item.cellLine)
      );
    }
    diseaseBaseData.forEach(item => {
      if (!counts.diseases[item.disease]) {
        counts.diseases[item.disease] = 0;
      }
      counts.diseases[item.disease]++;
    });
    
    setFilterCounts(counts);
  };

  // 排序功能
  const sortData = (data: GeneData[]) => {
    return [...data].sort((a, b) => {
      const aValue = a[sortField as keyof GeneData] || '';
      const bValue = b[sortField as keyof GeneData] || '';
      
      // 特殊处理数字类型的字段
      if (sortField === 'expressedTranscriptNumber' || sortField === 'te' || sortField === 'tr' || sortField === 'evi') {
        // 对于 TE、TR、EVI 字段，null 值排在最后
        const aNum = aValue !== null && aValue !== undefined ? Number(aValue) : (sortDirection === 'asc' ? Infinity : -Infinity);
        const bNum = bValue !== null && bValue !== undefined ? Number(bValue) : (sortDirection === 'asc' ? Infinity : -Infinity);

        if (sortDirection === 'asc') {
          return aNum - bNum;
        } else {
          return bNum - aNum;
        }
      }
      
      // 字符串排序
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });
  };

  // 数学公式组件
  const MathFormula = ({ formula }: { formula: string }) => {
    if (formula === 'TE') {
      return (
        <span className="inline-block align-middle mx-1">
          <span className="block text-center text-xs leading-tight">Ribo(TPM)</span>
          <span className="block border-t border-gray-600 text-center text-xs leading-tight mt-0.5 pt-0.5">RNA(TPM)</span>
        </span>
      );
    }
    if (formula === 'TR') {
      return (
        <span className="inline-block align-middle mx-1">
          <span className="block text-center text-xs leading-tight">RNC(TPM)</span>
          <span className="block border-t border-gray-600 text-center text-xs leading-tight mt-0.5 pt-0.5">RNA(TPM)</span>
        </span>
      );
    }
    if (formula === 'EVI') {
      return (
        <span className="inline-block align-middle mx-1">
          <span className="block text-center text-xs leading-tight">RNC(TPM)<sup>2</sup></span>
          <span className="block border-t border-gray-600 text-center text-xs leading-tight mt-0.5 pt-0.5">RNA(TPM) × Ribo(TPM)</span>
        </span>
      );
    }
    return null;
  };

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 当排序字段或方向改变时重新排序
  useEffect(() => {
    const sorted = sortData(filteredData);
    setFilteredData(sorted);
  }, [sortField, sortDirection]);

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <div className="flex flex-col ml-1 text-xs leading-none">
          <span className="text-gray-300 mb-0.5">▲</span>
          <span className="text-gray-300">▼</span>
        </div>
      );
    }
    
    return (
      <div className="flex flex-col ml-1 text-xs leading-none">
        <span className={`${sortDirection === 'asc' ? 'text-[#337ab7]' : 'text-gray-300'} mb-0.5`}>▲</span>
        <span className={`${sortDirection === 'desc' ? 'text-[#337ab7]' : 'text-gray-300'}`}>▼</span>
      </div>
    );
  };

  // 应用过滤器
  const applyFilters = () => {
    let filtered = [...allData];

    // 应用搜索过滤（支持部分匹配和多关键词搜索）
    if (searchKeyword.trim()) {
      const keywords = searchKeyword.toLowerCase().trim().split(/\s+/); // 支持多个关键词
      filtered = filtered.filter(item => {
        // 将所有字段值合并为一个字符串进行搜索
        const searchableText = Object.values(item)
          .map(value => value !== null && value !== undefined ? String(value).toLowerCase() : '')
          .join(' ');
        
        // 所有关键词都必须匹配（AND逻辑）
        return keywords.every(keyword => 
          keyword.length > 0 && searchableText.includes(keyword)
        );
      });
    }

    // 应用组织类型过滤
    if (filterStates.selectedTissueTypes.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedTissueTypes.includes(item.tissueOrCellType)
      );
    }

    // 应用细胞系过滤
    if (filterStates.selectedCellLines.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedCellLines.includes(item.cellLine)
      );
    }

    // 应用疾病过滤
    if (filterStates.selectedDiseases.length > 0) {
      filtered = filtered.filter(item =>
        filterStates.selectedDiseases.includes(item.disease)
      );
    }

    const sorted = sortData(filtered);
    setFilteredData(sorted);
    setCurrentPage(1);
  };

  // 处理组织/细胞类型选择 - 修改为支持多选但与细胞系互斥
  const handleTissueTypeChange = (tissueType: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      // 清空细胞系选择（互斥）
      newStates.selectedCellLines = [];
      newStates.selectedTissueTypes = [tissueType];
    } else {
      newStates.selectedTissueTypes = [];
    }
    
    setFilterStates(newStates);
  };

  // 处理细胞系选择 - 修改为支持多选但与组织类型互斥
  const handleCellLineChange = (cellLine: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      // 清空组织类型选择（互斥）
      newStates.selectedTissueTypes = [];
      newStates.selectedCellLines = [cellLine];
    } else {
      newStates.selectedCellLines = [];
    }
    
    setFilterStates(newStates);
  };

  // 处理疾病选择 - 支持多选
  const handleDiseaseChange = (disease: string, checked: boolean) => {
    const newStates = { ...filterStates };
    
    if (checked) {
      if (!newStates.selectedDiseases.includes(disease)) {
        newStates.selectedDiseases.push(disease);
      }
    } else {
      newStates.selectedDiseases = newStates.selectedDiseases.filter(d => d !== disease);
    }
    
    setFilterStates(newStates);
  };

  // 重置过滤器
  const resetFilters = () => {
    setSelectedGeneId("ENSG00000171862");
    setGeneIdInput("ENSG00000171862");
    setCustomGeneId("");
    setSelectedGeneSymbol("");
    setGeneSymbolInput("");
    setCustomGeneSymbol("");
    setQueryType('id');
    setShowGeneDropdown(false);
    setShowGeneSymbolDropdown(false);
    setGeneSymbolQuery('');
    setGeneIdQuery('');
    setFilterStates({
      selectedTissueTypes: [],
      selectedCellLines: [],
      selectedDiseases: []
    });
    setFilteredData(allData);
    calculateFilterCounts(allData);
  };

  // 提交过滤器
  const submitFilters = () => {
    // 确定使用哪个基因标识符
    if (customGeneSymbol || selectedGeneSymbol || geneSymbolInput) {
      const finalGeneSymbol = customGeneSymbol || selectedGeneSymbol || geneSymbolInput.trim();
      setSelectedGeneSymbol(finalGeneSymbol);
      setQueryType('symbol');
      // 清空基因ID相关状态，确保不会同时查询
      setSelectedGeneId('');
    } else if (customGeneId || selectedGeneId || geneIdInput) {
      const finalGeneId = customGeneId || selectedGeneId || geneIdInput.trim();
      setSelectedGeneId(finalGeneId);
      setQueryType('id');
      // 清空基因符号相关状态，确保不会同时查询
      setSelectedGeneSymbol('');
    }
  };

  // 当过滤器状态改变时重新计算选项和应用过滤器
  useEffect(() => {
    if (allData.length > 0) {
      calculateFilterCounts(allData);
      applyFilters();
    }
  }, [filterStates, allData]);

  // 当搜索关键词改变时应用过滤器
  useEffect(() => {
    if (allData.length > 0) {
      applyFilters();
    }
  }, [searchKeyword]);

  // 自定义滚动条功能
  useEffect(() => {
    const tableContainer = document.getElementById('table-container');
    const scrollbarThumb = document.getElementById('custom-scrollbar-thumb');
    const scrollbarTrack = document.getElementById('custom-scrollbar-track');

    if (!tableContainer || !scrollbarThumb || !scrollbarTrack) return;

    let isDragging = false;
    let startX = 0;
    let startScrollLeft = 0;

    // 计算滚动条thumb的位置和大小
    const updateScrollbar = () => {
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const scrollLeft = tableContainer.scrollLeft;

      if (scrollWidth <= containerWidth) {
        scrollbarThumb.style.display = 'none';
        return;
      }

      scrollbarThumb.style.display = 'block';

      // 计算thumb的宽度（比例）
      const thumbWidth = Math.max((containerWidth / scrollWidth) * containerWidth, 30);
      scrollbarThumb.style.width = `${thumbWidth}px`;

      // 计算thumb的位置
      const maxThumbLeft = containerWidth - thumbWidth;
      const thumbLeft = (scrollLeft / (scrollWidth - containerWidth)) * maxThumbLeft;
      scrollbarThumb.style.left = `${thumbLeft}px`;
    };

    // 鼠标按下事件
    const handleMouseDown = (e: MouseEvent) => {
      isDragging = true;
      startX = e.clientX;
      startScrollLeft = tableContainer.scrollLeft;
      scrollbarThumb.style.cursor = 'grabbing';
      e.preventDefault();
    };

    // 鼠标移动事件
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const thumbWidth = parseFloat(scrollbarThumb.style.width);
      const maxThumbLeft = containerWidth - thumbWidth;
      const maxScrollLeft = scrollWidth - containerWidth;

      const scrollRatio = deltaX / maxThumbLeft;
      const newScrollLeft = startScrollLeft + (scrollRatio * maxScrollLeft);

      tableContainer.scrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));
    };

    // 鼠标松开事件
    const handleMouseUp = () => {
      isDragging = false;
      scrollbarThumb.style.cursor = 'grab';
    };

    // 点击轨道事件
    const handleTrackClick = (e: MouseEvent) => {
      if (e.target === scrollbarThumb) return;

      const rect = scrollbarTrack.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const containerWidth = tableContainer.clientWidth;
      const scrollWidth = tableContainer.scrollWidth;
      const thumbWidth = parseFloat(scrollbarThumb.style.width);
      const maxThumbLeft = containerWidth - thumbWidth;
      const maxScrollLeft = scrollWidth - containerWidth;

      const targetThumbLeft = Math.max(0, Math.min(clickX - thumbWidth / 2, maxThumbLeft));
      const scrollRatio = targetThumbLeft / maxThumbLeft;
      tableContainer.scrollLeft = scrollRatio * maxScrollLeft;
    };

    // 表格滚动时更新滚动条
    const handleScroll = () => {
      updateScrollbar();
    };

    // 窗口大小改变时更新滚动条
    const handleResize = () => {
      updateScrollbar();
    };

    // 绑定事件
    scrollbarThumb.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    scrollbarTrack.addEventListener('click', handleTrackClick);
    tableContainer.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    // 初始化滚动条
    updateScrollbar();

    // 清理事件监听器
    return () => {
      scrollbarThumb.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      scrollbarTrack.removeEventListener('click', handleTrackClick);
      tableContainer.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [filteredData]); // 当数据变化时重新初始化滚动条

  // 处理基因ID下拉选择
  const handleGeneIdSelect = (geneId: string) => {
    setSelectedGeneId(geneId);
    setCustomGeneId(geneId);
    setGeneIdInput(geneId);
    setShowGeneDropdown(false);
    // 清空基因符号相关状态
    setSelectedGeneSymbol('');
    setCustomGeneSymbol('');
    setGeneSymbolInput('');
  };

  // 处理基因ID输入
  const handleGeneIdInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setGeneIdInput(value);
    setCustomGeneId(value);
    setSelectedGeneId('');
    
    if (value) {
      // 清空基因符号相关状态
      setSelectedGeneSymbol('');
      setCustomGeneSymbol('');
      setGeneSymbolInput('');
      debouncedSearch(value, 'id');
      setShowGeneDropdown(true);
    } else {
      setShowGeneDropdown(false);
    }
  };

  // 处理基因符号下拉选择
  const handleGeneSymbolSelect = (geneSymbol: string) => {
    setSelectedGeneSymbol(geneSymbol);
    setCustomGeneSymbol(geneSymbol);
    setGeneSymbolInput(geneSymbol);
    setShowGeneSymbolDropdown(false);
    // 清空基因ID相关状态
    setSelectedGeneId('');
    setCustomGeneId('');
    setGeneIdInput('');
  };

  // 处理基因符号输入
  const handleGeneSymbolInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setGeneSymbolInput(value);
    setCustomGeneSymbol(value);
    setSelectedGeneSymbol('');
    
    if (value) {
      // 清空基因ID相关状态
      setSelectedGeneId('');
      setCustomGeneId('');
      setGeneIdInput('');
      debouncedSearch(value, 'symbol');
      setShowGeneSymbolDropdown(true);
    } else {
      setShowGeneSymbolDropdown(false);
    }
  };

  // 排序选项：NA放在最后
  const sortOptions = (options: string[]) => {
    return options.sort((a, b) => {
      if (a === 'NA') return 1;
      if (b === 'NA') return -1;
      return a.localeCompare(b);
    });
  };

  // 渲染分页（参考sample页面）
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (currentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setCurrentPage(currentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous
        </button>
      );
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            currentPage === i 
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[#2B7FFF]' 
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
        </button>
      );
    }

    // Next button
    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setCurrentPage(currentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center">{pages}</div>;
  };

  // 获取过滤选项
  const getFilteredOptions = () => {
    const tissueTypes = sortOptions(Object.keys(filterCounts.tissueTypes));
    const cellLines = sortOptions(Object.keys(filterCounts.cellLines));
    const diseases = sortOptions(Object.keys(filterCounts.diseases));
    
    return { tissueTypes, cellLines, diseases };
  };

  const { tissueTypes, cellLines, diseases } = getFilteredOptions();

  // 处理URL参数并应用初始设置
  const applyUrlParameters = () => {
    const geneIdParam = searchParams.get('geneId');
    const geneSymbolParam = searchParams.get('geneSymbol');
    const searchParam = searchParams.get('search'); // 添加对search参数的支持
    const tissueTypeParam = searchParams.get('tissueType');
    const cellLineParam = searchParams.get('cellLine');
    const diseaseParam = searchParams.get('disease');

    // 确定使用哪种基因标识符 - 优先级：geneSymbol > search > geneId
    if (geneSymbolParam) {
      setSelectedGeneSymbol(geneSymbolParam);
      setGeneSymbolInput(geneSymbolParam);
      setQueryType('symbol');
    } else if (searchParam || geneIdParam) {
      const geneIdToUse = searchParam || geneIdParam;
      if (geneIdToUse) {
        setSelectedGeneId(geneIdToUse);
        setGeneIdInput(geneIdToUse);
        setQueryType('id');
      }
    } else {
      // 如果没有URL参数，设置默认基因ID
      setSelectedGeneId("ENSG00000171862");
      setGeneIdInput("ENSG00000171862");
      setQueryType('id');
    }

    // 设置过滤器状态
    const newFilterStates: FilterStates = {
      selectedTissueTypes: tissueTypeParam ? [tissueTypeParam] : [],
      selectedCellLines: cellLineParam ? [cellLineParam] : [],
      selectedDiseases: diseaseParam ? [diseaseParam] : []
    };

    setFilterStates(newFilterStates);
  };

  // 计算唯一的 Gene ID 数量
  const uniqueGeneIdCount = useMemo(() => {
    const uniqueGeneIds = new Set(filteredData.map(item => item.geneId));
    return uniqueGeneIds.size;
  }, [filteredData]);

  // 辅助函数：格式化数值字段显示
  const formatNumericValue = (value: number | null | undefined, decimals: number = 3): string => {
    if (value === null || value === undefined || String(value) === 'nan' || String(value) === 'null' || isNaN(Number(value))) {
      return 'NA';
    }
    return Number(value).toFixed(decimals);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="link-breadcrumb">
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span className="link-breadcrumb-current">Browse Gene</span>
        </div>
      </div>

      {/* 主内容区域 - 左右各2.5%空白 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
        {/* Display Select 部分 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div 
            className="p-4 border-b border-gray-200 bg-gray-50 cursor-pointer hover:bg-gray-100"
            onClick={() => setDisplaySelectCollapsed(!displaySelectCollapsed)}
          >
            <h2 className="text-lg font-bold flex items-center">
              {displaySelectCollapsed ? (
                <ChevronRight className="mr-2 h-5 w-5" />
              ) : (
                <ChevronDown className="mr-2 h-5 w-5" />
              )}
              Display Select
            </h2>
          </div>

          {!displaySelectCollapsed && (
            <div className="p-4">
              {/* 基因选择器和按钮 */}
              <div className="mb-4 border-b border-gray-200 pb-4">
                <div className="relative flex flex-wrap items-center justify-center">
                  
                  {/* 中间区块 */}
                  <div className="flex flex-wrap items-center space-x-6 pr-24">
                    {/* GENE SYMBOL */}
                    <div className="flex items-center space-x-2">
                      <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                      Gene Symbol:
                      </label>
                      <div className="relative">
                        <input
                          ref={geneSymbolInputRef}
                          type="text"
                          placeholder="Gene Symbol"
                          value={customGeneSymbol || selectedGeneSymbol || geneSymbolInput}
                          onChange={handleGeneSymbolInputChange}
                          onFocus={() =>
                            (customGeneSymbol || selectedGeneSymbol || geneSymbolInput) &&
                            setShowGeneSymbolDropdown(true)
                          }
                          disabled={!!selectedGeneId || !!customGeneId || !!geneIdInput}
                          className="w-64 h-10 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
                        />
                        {/* 下拉触发图标 */}
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 flex items-center pr-2 cursor-pointer"
                          onClick={() => {
                            geneSymbolInputRef.current?.focus();
                            setShowGeneSymbolDropdown(true);
                          }}
                        >
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        </button>

                        {showGeneSymbolDropdown && filteredGeneSymbols.length > 0 && (
                          <div
                            ref={geneSymbolDropdownRef}
                            className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
                          >
                            {filteredGeneSymbols.map((symbol) => (
                              <div
                                key={symbol}
                                className="px-3 py-2 hover:bg-[#f0f4f8] cursor-pointer text-sm"
                                onClick={() => {
                                  handleGeneSymbolSelect(symbol);
                                  setShowGeneSymbolDropdown(false);
                                }}
                              >
                                {symbol}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* GENE ID */}
                    <div className="flex items-center space-x-2">
                      <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                        Gene ID:
                      </label>
                      <div className="relative">
                        <input
                          ref={geneIdInputRef}
                          type="text"
                          placeholder="Gene ID"
                          value={customGeneId || selectedGeneId || geneIdInput}
                          onChange={handleGeneIdInputChange}
                          onFocus={() =>
                            (customGeneId || selectedGeneId || geneIdInput) && setShowGeneDropdown(true)
                          }
                          disabled={!!selectedGeneSymbol || !!customGeneSymbol || !!geneSymbolInput}
                          className="w-64 h-10 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
                        />
                        {/* 下拉触发图标 */}
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 flex items-center pr-2 cursor-pointer"
                          onClick={() => {
                            geneIdInputRef.current?.focus();
                            setShowGeneDropdown(true);
                          }}
                        >
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        </button>

                        {showGeneDropdown && filteredGeneIds.length > 0 && (
                          <div
                            ref={geneIdDropdownRef}
                            className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
                          >
                            {filteredGeneIds.map((id) => (
                              <div
                                key={id}
                                className="px-3 py-2 hover:bg-[#f0f4f8] cursor-pointer text-sm"
                                onClick={() => {
                                  handleGeneIdSelect(id);
                                  setShowGeneDropdown(false);
                                }}
                              >
                                {id}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Submit / Reset */}
                    <div className="flex gap-3">
                      <Button
                        onClick={submitFilters}
                        disabled={loading}
                        className={`btn-submit ${loading ? 'loading' : ''}`}
                      >
                        {loading ? 'Loading…' : 'Submit'}
                      </Button>
                      <Button
                        onClick={resetFilters}
                        variant="outline"
                        className="btn-reset"
                      >
                        Reset
                      </Button>
                    </div>
                  </div>
                  
                </div>
              </div>

              {/* Tissue/Cell Type 过滤器 */}
              <div className="mb-4 border-b border-gray-200 pb-4">
                <div className="flex">
                  <div className="w-36 font-bold text-gray-800 whitespace-nowrap">Tissue/Cell Type:</div>
                  <div className="flex-1">
                    <div className="grid grid-cols-4 gap-4">
                      {tissueTypes.slice(0, showMoreTissueTypes ? undefined : 4).map(type => (
                        <label key={type} className="flex items-start group cursor-pointer">
                          <input
                            type="checkbox"
                            checked={filterStates.selectedTissueTypes.includes(type)}
                            onChange={(e) => handleTissueTypeChange(type, e.target.checked)}
                            className="mr-2 h-4 w-4 mt-0.5 flex-shrink-0"
                          />
                          <span 
                            className="text-sm leading-relaxed break-words" 
                            title={`${type} (${filterCounts.tissueTypes[type] || 0})`}
                          >
                            {type} ({filterCounts.tissueTypes[type] || 0})
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div className="w-20 flex justify-end">
                    {tissueTypes.length > 4 && (
                      <button 
                        onClick={() => setShowMoreTissueTypes(!showMoreTissueTypes)}
                        className="text-[#337ab7] hover:text-[#337ab7] text-sm"
                      >
                        {showMoreTissueTypes ? "- less" : "+ more"}
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Cell Line 过滤器 */}
              <div className="mb-4 border-b border-gray-200 pb-4">
                <div className="flex">
                  <div className="w-36 font-bold text-gray-800">Cell Line:</div>
                  <div className="flex-1">
                    <div className="grid grid-cols-4 gap-4">
                      {cellLines.slice(0, showMoreCellLines ? undefined : 4).map(line => (
                        <label key={line} className="flex items-start group cursor-pointer">
                          <input
                            type="checkbox"
                            checked={filterStates.selectedCellLines.includes(line)}
                            onChange={(e) => handleCellLineChange(line, e.target.checked)}
                            className="mr-2 h-4 w-4 mt-0.5 flex-shrink-0"
                          />
                          <span 
                            className="text-sm leading-relaxed break-words" 
                            title={`${line} (${filterCounts.cellLines[line] || 0})`}
                          >
                            {line} ({filterCounts.cellLines[line] || 0})
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div className="w-20 flex justify-end">
                    {cellLines.length > 4 && (
                      <button 
                        onClick={() => setShowMoreCellLines(!showMoreCellLines)}
                        className="text-[#337ab7] hover:text-[#337ab7] text-sm"
                      >
                        {showMoreCellLines ? "- less" : "+ more"}
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Disease 过滤器 */}
              <div className="mb-2">
                <div className="flex">
                  <div className="w-36 font-bold text-gray-800">Condition:</div>
                  <div className="flex-1">
                    <div className="grid grid-cols-4 gap-4">
                      {diseases.slice(0, showMoreDiseases ? undefined : 4).map(disease => (
                        <label key={disease} className="flex items-start group cursor-pointer">
                          <input
                            type="checkbox"
                            checked={filterStates.selectedDiseases.includes(disease)}
                            onChange={(e) => handleDiseaseChange(disease, e.target.checked)}
                            className="mr-2 h-4 w-4 mt-0.5 flex-shrink-0"
                          />
                          <span 
                            className="text-sm leading-relaxed break-words" 
                            title={`${disease} (${filterCounts.diseases[disease] || 0})`}
                          >
                            {disease} ({filterCounts.diseases[disease] || 0})
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div className="w-20 flex justify-end">
                    {diseases.length > 4 && (
                      <button 
                        onClick={() => setShowMoreDiseases(!showMoreDiseases)}
                        className="text-[#337ab7] hover:text-[#337ab7] text-sm"
                      >
                        {showMoreDiseases ? "- less" : "+ more"}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 顶部搜索和下载区域 */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-800">
              {loading ? 'Loading...' : `${uniqueGeneIdCount} Gene ID${uniqueGeneIdCount === 1 ? '' : 's'} Found`}
            </h1>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex">
              <Input
                placeholder="Search"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="h-10 w-80 px-3 pr-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#337ab7] focus:border-[#337ab7] bg-white"
              />
            </div>

            <div className="relative">
              <button
                onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
              >
                <Download className="h-4 w-4 mr-2" />
                <ChevronDown className="h-4 w-4 ml-2" />
              </button>

              {showDownloadOptions && (
                <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="py-1">
                    <button
                      onClick={() => downloadData('csv')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                    >
                      Download as CSV
                    </button>
                    <button
                      onClick={() => downloadData('json')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                    >
                      Download as JSON
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 数据表格 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <p className="text-gray-600">Loading data...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <p className="text-red-600">Error: {error}</p>
            </div>
          ) : (
            <>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div
                  id="table-container"
                  className="table-scroll"
                  style={{
                    overflowX: 'auto',
                    overflowY: 'hidden',
                    position: 'relative',
                    scrollbarWidth: 'none', // Firefox
                    msOverflowStyle: 'none' // IE
                  }}
                >
                <table
                  className="w-full divide-y divide-gray-200"
                  style={{
                    minWidth: '1100px',
                    fontSize: '13px',
                    textAlign: 'center',
                    whiteSpace: 'nowrap'
                  }}
                >
                  <thead className="bg-gray-100" style={{position: 'relative', zIndex: 1}}>
                    <tr>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('geneSymbol')}
                      >
                        <div className="flex items-center justify-center">
                          <span>GENE SYMBOL</span>
                          {renderSortIcon('geneSymbol')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('geneId')}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <span>GENE ID</span>
                          <div
                            className="relative group"
                            onMouseEnter={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Info className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600 cursor-help" />
                            <div className="absolute top-full right-0 mt-1 px-2 py-1.5 bg-white border border-gray-200 text-gray-700 text-xs rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 max-w-xs normal-case">
                              Click Gene ID for details
                              <div className="absolute bottom-full right-2 w-0 h-0 border-l-2 border-r-2 border-b-2 border-transparent border-b-white"></div>
                              <div className="absolute bottom-full right-2 w-0 h-0 border-l-2 border-r-2 border-b-2 border-transparent border-b-gray-200" style={{marginBottom: '-1px'}}></div>
                            </div>
                          </div>
                          {renderSortIcon('geneId')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('projectId')}
                      >
                        <div className="flex items-center justify-center">
                          <span>Dataset id</span>
                          {renderSortIcon('projectId')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('expressedTranscriptNumber')}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <span>Translated Transcripts Number</span>
                          <div
                            className="relative group"
                            onMouseEnter={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Info className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600 cursor-help" />
                            <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 px-2 py-1.5 bg-white border border-gray-200 text-gray-700 text-xs rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap normal-case">
                              Click to enter the &lsquo;Translated Transcripts of Gene&rsquo; page
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-200" style={{ transform: 'translateY(1px)'}}></div>
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-white"></div>
                            </div>
                          </div>
                          {renderSortIcon('expressedTranscriptNumber')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('tissueOrCellType')}
                      >
                        <div className="flex items-center justify-center">
                          <span>Tissue/Cell Type</span>
                          {renderSortIcon('tissueOrCellType')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('cellLine')}
                      >
                        <div className="flex items-center justify-center">
                          <span>Cell line</span>
                          {renderSortIcon('cellLine')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('disease')}
                      >
                        <div className="flex items-center justify-center">
                          <span>Condition</span>
                          {renderSortIcon('disease')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('te')}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <span>TE</span>
                          <div
                            className="relative group"
                            onMouseEnter={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Info className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600 cursor-help" />
                              <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 px-3 py-2 bg-white border border-gray-300 text-gray-700 text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-normal normal-case text-left w-72">
                              <div>
                                Translation Efficiency, a measure of ribosome density of a gene, is equal to{' '}
                                <span className="inline-block align-middle"><MathFormula formula="TE" /></span>
                              </div>
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-300" style={{ transform: 'translateY(1px)'}}></div>
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-white"></div>
                            </div>
                          </div>
                          {renderSortIcon('te')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('tr')}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <span>TR</span>
                          <div
                            className="relative group"
                            onMouseEnter={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Info className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600 cursor-help" />
                              <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 px-3 py-2 bg-white border border-gray-300 text-gray-700 text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-normal normal-case text-left w-72">
                              <div>
                                Translation Ratio, a measure of the translation initiation efficiency of gene, is equal to{' '}
                                <span className="inline-block align-middle"><MathFormula formula="TR" /></span>
                              </div>
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-300" style={{ transform: 'translateY(1px)'}}></div>
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-white"></div>
                            </div>
                          </div>
                          {renderSortIcon('tr')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('evi')}
                      >
                        <div className="flex items-center justify-center space-x-1">
                          <span>EVI</span>
                          <div
                            className="relative group"
                            onMouseEnter={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Info className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600 cursor-help" />
                              <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 px-3 py-2 bg-white border border-gray-300 text-gray-700 text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-normal normal-case text-left w-72">
                              <div>
                                Elongation Velocity Index, a measure of the translation elongating speed of gene, is equal to{' '}
                                <span className="inline-block align-middle"><MathFormula formula="EVI" /></span>
                              </div>
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-300" style={{ transform: 'translateY(1px)'}}></div>
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-white"></div>
                            </div>
                          </div>
                          {renderSortIcon('evi')}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('chromosome')}
                      >
                        <div className="flex items-center justify-center">
                          <span>Location</span>
                          {renderSortIcon('chromosome')}
                        </div>
                      </th>

                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentData.length > 0 ? (
                      currentData.map((item, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                            {item.geneSymbol && item.geneSymbol !== 'nan' && item.geneSymbol !== 'null' ? item.geneSymbol : 'NA'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-[#337ab7] hover:text-[#337ab7]">
                            {item.geneId && item.geneId !== 'nan' && item.geneId !== 'null' ? (
                              <button
                                onClick={() => handleGeneIdClick(item.geneId)}
                                className="hover:underline cursor-pointer"
                                disabled={geneInfoLoading}
                              >
                                {item.geneId}
                              </button>
                            ) : (
                              <span className="text-gray-900">NA</span>
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                            {item.projectId && item.projectId !== 'nan' && item.projectId !== 'null' ? (
                              <Link
                                href={`/browse/dataset?search=${item.projectId}`}
                                className="text-[#337ab7] hover:text-[#337ab7] hover:underline"
                              >
                                {item.projectId}
                              </Link>
                            ) : (
                              <span className="text-gray-900">NA</span>
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {item.expressedTranscriptNumber && String(item.expressedTranscriptNumber) !== 'nan' && String(item.expressedTranscriptNumber) !== 'null' ? (
                              <Link
                                href={`/browse/gene/${item.geneId}/${item.projectId}`}
                                className="text-[#337ab7] hover:text-[#337ab7] hover:underline"
                              >
                                {item.expressedTranscriptNumber}
                              </Link>
                            ) : (
                              'NA'
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {item.tissueOrCellType && item.tissueOrCellType !== 'nan' && item.tissueOrCellType !== 'null' ? item.tissueOrCellType : 'NA'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {item.cellLine && item.cellLine !== 'nan' && item.cellLine !== 'null' ? item.cellLine : 'NA'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {item.disease && item.disease !== 'nan' && item.disease !== 'null' ? item.disease : 'NA'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {formatNumericValue(item.te)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {formatNumericValue(item.tr)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {formatNumericValue(item.evi)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                            {item.chromosome && item.chromosome !== 'nan' && item.chromosome !== 'null' ? item.chromosome : 'NA'}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={11} className="px-4 py-8 text-center text-gray-500">
                          No data found for the selected filters.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
                </div>

                {/* 自定义水平滚动条 - 始终显示 */}
                <div
                  className="bg-gray-100 border-t border-gray-200"
                  style={{
                    height: '16px',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  <div
                    id="custom-scrollbar-track"
                    className="w-full h-full bg-gray-100 cursor-pointer"
                    style={{
                      position: 'relative'
                    }}
                  >
                    <div
                      id="custom-scrollbar-thumb"
                      className="bg-gray-400 hover:bg-gray-500 rounded"
                      style={{
                        height: '12px',
                        width: '80px',
                        position: 'absolute',
                        top: '2px',
                        left: '0px',
                        cursor: 'grab',
                        transition: 'background-color 0.2s'
                      }}
                    ></div>
                  </div>
                </div>
              </div>
              
              {/* 分页和控制 */}
              <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <span className="text-sm font-medium text-gray-700">
                      Showing {startIndex + 1} to {Math.min(endIndex, filteredData.length)} of {filteredData.length} rows
                    </span>
                    <div className="flex items-center space-x-2">
                      <label className="text-sm font-medium text-gray-700">
                        <select
                          value={pageSize}
                          onChange={(e) => {
                            setPageSize(Number(e.target.value));
                            setCurrentPage(1);
                          }}
                          className="ml-2 mr-2 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#0071BC] focus:border-transparent"
                        >
                          <option value={10}>10</option>
                          <option value={25}>25</option>
                          <option value={50}>50</option>
                          <option value={100}>100</option>
                        </select>
                        rows per page
                      </label>
                    </div>
                  </div>
                  
                  {renderPagination()}
                </div>
              </div>
            </>
          )}
        </div>
        
        {/* 基因信息卡片 */}
        {showGeneInfoCard && <GeneInfoCard />}
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-8">
        <Footer />
      </div>
      
      {/* 点击其他地方关闭下拉菜单 */}
      {showGeneDropdown && (
        <div 
          className="fixed inset-0 z-5" 
          onClick={() => {
            setShowGeneDropdown(false);
          }}
        />
      )}
    </div>
  );
}

export default function GenePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <GenePageContent />
    </Suspense>
  );
}