'use client';

import React, { useState, useEffect, use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
// import { Button } from "@/app/components/ui/button";
import { Download, ChevronDown } from 'lucide-react';
import Footer from '../../../../components/Footer/Footer';

interface TranscriptData {
  id: number;
  transcriptId: string;
  projectId: string;
  bioprojectId: string;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  tr: number | null;
  evi: number | null;
  te: number;
  geneId: string;
  geneSymbol: string;
  threeUtrComp: string;
  fiveUtrComp: string;
}

interface UTRComponents {
  miRNAs: string;
  'PolyA Sites': string;
  Repeats: string;
  'Rfam motifs': string;
  uORFs: string;
}

interface TranscriptDetailData {
  transcriptId: string;
  geneId: string;
  geneSymbol: string;
  threeUtrEntryName: string;
  threeUtrLength: number;
  threeUtrUrl: string;
  threeUtrMiRnas: string;
  threeUtrPolyaSites: string;
  threeUtrRfamMotifs: string;
  threeUtrRepeats: string;
  threeUtrUorfs: string;
  fiveUtrEntryName: string;
  fiveUtrLength: number;
  fiveUtrUrl: string;
  fiveUtrMiRnas: string;
  fiveUtrPolyaSites: string;
  fiveUtrRfamMotifs: string;
  fiveUtrRepeats: string;
  fiveUtrUorfs: string;
  fiveUtrIres: string;
  threeUtrIres: string;
  transcriptLocation: string;
}

interface PageProps {
  params: Promise<{
    geneId: string;
    projectId: string;
  }>;
}

export default function GeneTranscriptDetailPage({ params }: PageProps) {
  const [data, setData] = useState<TranscriptData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortField, setSortField] = useState<string>('transcriptId');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedTranscript, setSelectedTranscript] = useState<TranscriptDetailData | null>(null);
  const [showDownloadOptions, setShowDownloadOptions] = useState<boolean>(false);

  const { geneId, projectId } = use(params);

  // 获取转录本详情
  const fetchTranscriptDetail = async (transcriptId: string) => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const fullUrl = `${apiUrl}/transcriptinfo/id/${transcriptId}`;
      
      const response = await fetch(fullUrl);
      
      if (!response.ok) {
        console.error(`API returned error: ${response.status} for URL: ${fullUrl}`);
        throw new Error(`API returned error: ${response.status}`);
      }
      
      const result = await response.json();
      setSelectedTranscript(result);
    } catch (err) {
      console.error('Error fetching transcript detail:', err);
      alert(`获取转录本详情失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  // 获取数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/utrdb/api';
        const response = await fetch(`${apiUrl}/translation-indices/gene-project?geneId=${geneId}&projectId=${projectId}`);
        
        if (!response.ok) {
          throw new Error(`API returned error: ${response.status}`);
        }
        
        const result = await response.json();
        setData(result);
        setError(null);
      } catch (err) {
        console.error('Error fetching transcript data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setData([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [geneId, projectId]);

  // 下载功能
  const downloadData = (format: 'csv' | 'json') => {
    const dataToDownload = sortedData;
    
    if (format === 'json') {
      const jsonData = JSON.stringify(dataToDownload, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transcripts_${geneId}_${projectId}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } else if (format === 'csv') {
      // CSV转义函数：处理包含逗号、引号、换行符的值
      const escapeCsvValue = (value: string | number | null | undefined): string => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        // 如果包含逗号、引号或换行符，需要用双引号包围，并转义内部的双引号
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      const headers = [
        'TRANSCRIPT ID',
        'DATASET ID',
        'TE',
        'TR',
        'EVI',
        '5\' UTR ELEMENTS',
        '3\' UTR ELEMENTS'
      ];
      const csvContent = [
        headers.join(','),
        ...dataToDownload.map(item => [
          escapeCsvValue(item.transcriptId),
          escapeCsvValue(item.projectId),
          escapeCsvValue(item.te),
          escapeCsvValue(item.tr),
          escapeCsvValue(item.evi),
          escapeCsvValue(item.fiveUtrComp),
          escapeCsvValue(item.threeUtrComp)
        ].join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transcripts_${geneId}_${projectId}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    }
    
    setShowDownloadOptions(false);
  };

  // 排序功能
  const sortData = (data: TranscriptData[]) => {
    return [...data].sort((a, b) => {
      const aValue = a[sortField as keyof TranscriptData] || '';
      const bValue = b[sortField as keyof TranscriptData] || '';
      
      if (sortField === 'te' || sortField === 'tr' || sortField === 'evi') {
        const aNum = Number(aValue) || 0;
        const bNum = Number(bValue) || 0;
        if (sortDirection === 'asc') {
          return aNum - bNum;
        } else {
          return bNum - aNum;
        }
      }
      
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });
  };

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <div className="flex flex-col ml-1 text-xs leading-none">
          <span className="text-gray-300 mb-0.5">▲</span>
          <span className="text-gray-300">▼</span>
        </div>
      );
    }
    
    return (
      <div className="flex flex-col ml-1 text-xs leading-none">
        <span className={`${sortDirection === 'asc' ? 'text-[#337ab7]' : 'text-gray-300'} mb-0.5`}>▲</span>
        <span className={`${sortDirection === 'desc' ? 'text-[#337ab7]' : 'text-gray-300'}`}>▼</span>
      </div>
    );
  };

  // 解析UTR组件
  const parseUTRComponents = (componentsString: string): UTRComponents => {
    try {
      return JSON.parse(componentsString);
    } catch {
      return {
        miRNAs: 'no',
        'PolyA Sites': 'no',
        Repeats: 'no',
        'Rfam motifs': 'no',
        uORFs: 'no'
      };
    }
  };

  // 渲染UTR组件标签
  const renderUTRTags = (componentsString: string): React.ReactElement[] => {
    const components = parseUTRComponents(componentsString);
    const tags: React.ReactElement[] = [];

    Object.entries(components).forEach(([key, value]) => {
      if (value === 'yes') {
        let tagClass = 'px-2 py-1 rounded text-xs font-medium';
        let displayName = key;

        switch (key) {
          case 'miRNAs':
            tagClass += ' bg-blue-100 text-blue-800';
            break;
          case 'PolyA Sites':
            tagClass += ' bg-gray-100 text-gray-800';
            displayName = 'PolyA site';
            break;
          case 'Repeats':
            tagClass += ' bg-orange-100 text-orange-800';
            displayName = 'Repeats';
            break;
          case 'Rfam motifs':
            tagClass += ' bg-green-100 text-green-800';
            break;
          case 'uORFs':
            tagClass += ' bg-purple-100 text-purple-800';
            break;
          default:
            tagClass += ' bg-gray-100 text-gray-800';
        }

        tags.push(
          <span key={key} className={tagClass}>
            {displayName}
          </span>
        );
      }
    });

    return tags;
  };

  // 解析UTR元素数据
  const parseUTRElementData = (dataString: string) => {
    if (!dataString) return { hasValue: false, count: 0, proportion: 0, avgLength: 0 };

    const countMatch = dataString.match(/count:\s*(\d+)/);
    const proportionMatch = dataString.match(/Proportion:\s*([\d.]+)%/);
    const avgLengthMatch = dataString.match(/avg_length:\s*([\d.]+)/);

    return {
      hasValue: true,
      count: countMatch ? parseInt(countMatch[1]) : 0,
      proportion: proportionMatch ? parseFloat(proportionMatch[1]) : 0,
      avgLength: avgLengthMatch ? parseFloat(avgLengthMatch[1]) : 0
    };
  };

  // 渲染UTR元素行
  const renderUTRElementRow = (label: string, data: string) => {
    const parsed = parseUTRElementData(data);
    const maxProportion = 100;
    const displayWidth = parsed.proportion > 0 ? Math.max(Math.min(parsed.proportion, maxProportion), 10) : 0;
    const displayProportion = Math.min(parsed.proportion, maxProportion);

    return (
      <tr key={label} className="border-b border-gray-200">
        <td className="px-4 py-2 font-medium text-gray-700">{label}</td>
        <td className="px-4 py-2 text-center">
          <div className={`w-3 h-3 rounded-full mx-auto ${parsed.hasValue ? 'bg-red-500' : 'bg-green-500'}`}></div>
        </td>
        <td className="px-4 py-2 text-center text-gray-900">{parsed.count}</td>
        <td className="px-4 py-2">
          {parsed.proportion > 0 && (
            <div className="flex items-center">
              <div className="flex-1 bg-gray-200 rounded-full h-4 mr-2">
                <div
                  className="bg-blue-500 h-4 rounded-full"
                  style={{ width: `${displayWidth}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-900 min-w-0">{displayProportion.toFixed(2)}%</span>
            </div>
          )}
        </td>
        <td className="px-4 py-2 text-center text-gray-900">
          {parsed.avgLength > 0 ? parsed.avgLength.toFixed(1) : ''}
        </td>
      </tr>
    );
  };

  // 数学公式组件
  const MathFormula = ({ formula }: { formula: string }) => {
    if (formula === 'TE') {
      return (
        <span className="inline-block align-middle mx-1">
          <span className="block text-center text-xs leading-tight">Ribo(TPM)</span>
          <span className="block border-t border-gray-600 text-center text-xs leading-tight mt-0.5 pt-0.5">RNA(TPM)</span>
        </span>
      );
    }
    if (formula === 'TR') {
      return (
        <span className="inline-block align-middle mx-1">
          <span className="block text-center text-xs leading-tight">RNC(TPM)</span>
          <span className="block border-t border-gray-600 text-center text-xs leading-tight mt-0.5 pt-0.5">RNA(TPM)</span>
        </span>
      );
    }
    if (formula === 'EVI') {
      return (
        <span className="inline-block align-middle mx-1">
          <span className="block text-center text-xs leading-tight">RNC(TPM)<sup>2</sup></span>
          <span className="block border-t border-gray-600 text-center text-xs leading-tight mt-0.5 pt-0.5">RNA(TPM) × Ribo(TPM)</span>
        </span>
      );
    }
    return null;
  };

  // 渲染带提示的问号图标
  const renderInfoIcon = (title: string, description: string | React.ReactNode, formula?: string) => {
    return (
      <div className="ml-1 relative group">
        <div className="text-gray-400 cursor-help hover:text-gray-600">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-white border border-gray-300 text-gray-800 text-sm rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-[9999] w-64 normal-case">
          {title && <div className="font-medium text-gray-900">{title}</div>}
          <div className="text-gray-700 whitespace-normal">
            {formula ? (
              <span className="inline-flex items-center align-middle">
                <span className="mr-1">{formula}=</span>
                <MathFormula formula={formula} />
              </span>
            ) : (
              description
            )}
          </div>
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-white"></div>
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-px border-4 border-transparent border-t-gray-300"></div>
        </div>
      </div>
    );
  };

  // 获取排序后的数据和分页信息
  const sortedData = sortData(data);
  const totalPages = Math.ceil(sortedData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = sortedData.slice(startIndex, endIndex);

  // 渲染分页
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (currentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setCurrentPage(currentPage - 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous
        </button>
      );
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`px-4 py-2 mx-1 text-sm font-medium border rounded-lg transition-colors ${
            currentPage === i 
              ? 'bg-[#0071BC] text-white border-[#0071BC] hover:bg-[blue-700]' 
              : 'border-gray-300 hover:bg-gray-50 text-gray-700'
          }`}
        >
          {i}
        </button>
      );
    }

    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setCurrentPage(currentPage + 1)}
          className="px-4 py-2 mx-1 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Next
        </button>
      );
    }

    return <div className="flex justify-center items-center">{pages}</div>;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="w-full bg-slate-100 border-b border-slate-200 shadow-sm py-3 px-6" style={{ padding: '12px 2.5%' }}>
        <div className="text-sm flex items-center">
          <Image 
            src={`${process.env.basePath || ''}/logo/Home.png`}
            alt="Home"
            width={16}
            height={16}
            className="mr-2"
          />
          <Link href="/" className="link-breadcrumb">
            Home
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <Link href="/browse/gene" className="link-breadcrumb-current">
            Browse Gene
          </Link>
          <span className="mx-2" style={{ color: '#000000' }}>&gt;</span>
          <span style={{ color: '#337ab7' }}>Translated Transcripts of Gene</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1" style={{ margin: '0 2.5%' }}>
        <div className="py-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200" style={{ overflow: 'visible' }}>
            {/* 页面标题和下载按钮 */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold text-gray-900">
                  Transcripts of {geneId} in {projectId}
                </h1>
                
                {/* 下载按钮 */}
                <div className="relative">
                  <button
                    onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                    className="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 h-10 rounded-md text-sm font-medium flex items-center shadow-sm"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  
                  {showDownloadOptions && (
                    <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                      <div className="py-1">
                        <button
                          onClick={() => downloadData('csv')}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                        >
                          Download as CSV
                        </button>
                        <button
                          onClick={() => downloadData('json')}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-[#F9FAFB]"
                        >
                          Download as JSON
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* 数据表格内容 */}
            {loading ? (
              <div className="p-8 text-center">
                <p className="text-gray-600">Loading data...</p>
              </div>
            ) : error ? (
              <div className="p-8 text-center">
                <p className="text-red-600">Error: {error}</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto" style={{ overflow: 'visible' }}>
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                      <tr>
                        <th
                          scope="col"
                          className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort('transcriptId')}
                        >
                          <div className="flex items-center justify-center space-x-1">
                            <span>Transcript ID</span>
                            {renderInfoIcon('', 'Click Transcript ID for details')}
                            {renderSortIcon('transcriptId')}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort('projectId')}
                        >
                          <div className="flex items-center justify-center">
                            <span>Dataset id</span>
                            {renderSortIcon('projectId')}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort('te')}
                        >
                          <div className="flex items-center justify-center">
                            <span>TE</span>
                            {renderInfoIcon('', '', 'TE')}
                            {renderSortIcon('te')}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort('tr')}
                        >
                          <div className="flex items-center justify-center">
                            <span>TR</span>
                            {renderInfoIcon('', '', 'TR')}
                            {renderSortIcon('tr')}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={() => handleSort('evi')}
                        >
                          <div className="flex items-center justify-center">
                            <span>EVI</span>
                            {renderInfoIcon('', '', 'EVI')}
                            {renderSortIcon('evi')}
                          </div>
                        </th>
                        <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <span>5&apos; UTR element</span>
                        </th>
                        <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <span>3&apos; UTR element</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {currentData.length > 0 ? (
                        currentData.map((item: TranscriptData) => (
                          <tr key={item.id} className="hover:bg-gray-50">
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-[#337ab7] hover:text-[#337ab7]">
                              {item.transcriptId && item.transcriptId !== 'nan' && item.transcriptId !== 'null' ? (
                                <button
                                  onClick={() => fetchTranscriptDetail(item.transcriptId)}
                                  className="hover:underline cursor-pointer"
                                >
                                  {item.transcriptId}
                                </button>
                              ) : (
                                <span className="text-gray-900">NA</span>
                              )}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-[#337ab7] hover:text-[#337ab7]">
                              {item.projectId && item.projectId !== 'nan' && item.projectId !== 'null' ? (
                                <Link
                                  href={`/browse/dataset?search=${item.projectId}`}
                                  className="hover:underline"
                                >
                                  {item.projectId}
                                </Link>
                              ) : (
                                <span className="text-gray-900">NA</span>
                              )}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                              {item.te ? item.te.toFixed(3) : 'NA'}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                              {item.tr ? item.tr.toFixed(3) : 'NA'}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900">
                              {item.evi ? item.evi.toFixed(3) : 'NA'}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                              <div className="flex flex-wrap gap-1 justify-center">
                                {renderUTRTags(item.fiveUtrComp)}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-center">
                              <div className="flex flex-wrap gap-1 justify-center">
                                {renderUTRTags(item.threeUtrComp)}
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                            No transcript data found.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
                
                {/* 分页和控制 */}
                <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-6">
                      <span className="text-sm font-medium text-gray-700">
                        Showing {startIndex + 1} to {Math.min(endIndex, sortedData.length)} of {sortedData.length} rows
                      </span>
                      <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-700">
                          <select
                            value={pageSize}
                            onChange={(e) => {
                              setPageSize(Number(e.target.value));
                              setCurrentPage(1);
                            }}
                            className="ml-2 mr-2 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#0071BC] focus:border-transparent"
                          >
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                          </select>
                          rows per page
                        </label>
                      </div>
                    </div>
                    
                    {renderPagination()}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* 转录本详情区域 - 显示在表格和footer之间 */}
      {selectedTranscript && (
        <div className="flex-1" style={{ margin: '0 2.5%' }}>
          <div className="pb-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {/* 详情头部 */}
              <div className="flex justify-between items-center p-6 border-b border-gray-200">
                <h2 className="text-lg font-bold text-black">Transcript Information</h2>
                <button
                  onClick={() => setSelectedTranscript(null)}
                  className="text-gray-400 hover:text-gray-600 p-2"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 详情内容 */}
              <div className="p-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-2 gap-8 mb-8">
                  {/* 左侧信息 */}
                  <div className="space-y-4">
                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-gray-700 flex-shrink-0">Transcript ID</div>
                      <div className="text-gray-900">{selectedTranscript.transcriptId}</div>
                    </div>
                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-gray-700 flex-shrink-0">5&apos; UTR ID</div>
                      <div className="text-[#337ab7] hover:text-[#337ab7]">
                        <a 
                          href={selectedTranscript.fiveUtrUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {selectedTranscript.fiveUtrEntryName}
                        </a>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-gray-700 flex-shrink-0">Location</div>
                      <div className="text-gray-900">{selectedTranscript.transcriptLocation}</div>
                    </div>
                  </div>
                  
                  {/* 右侧信息 */}
                  <div className="space-y-4">
                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-gray-700 flex-shrink-0">Gene Symbol</div>
                      <div className="text-[#337ab7] hover:text-[#337ab7]">
                        <a
                          href={`https://www.ncbi.nlm.nih.gov/gene/?term=${encodeURIComponent(selectedTranscript.geneSymbol)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {selectedTranscript.geneSymbol}
                        </a>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-gray-700 flex-shrink-0">3&apos; UTR ID</div>
                      <div className="text-[#337ab7] hover:text-[#337ab7]">
                        <a 
                          href={selectedTranscript.threeUtrUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {selectedTranscript.threeUtrEntryName}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-lg font-bold text-gray-900 mb-4">UTR element Details</div>

                {/* UTR详情表格 */}
                <div className="grid grid-cols-2 gap-8">
                  {/* 5' UTR */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">5&apos; UTR</h3>
                    <table className="min-w-full border border-gray-300 rounded-lg">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Element</th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">
                            <div className="flex items-center justify-center">
                              <span>Yes OR No</span>
                              {renderInfoIcon('',
                                <>
                                  <span style={{color: 'red'}}>Red</span> represents YES, <span style={{color: 'green'}}>Green</span> represents NO
                                </>
                              )}
                            </div>
                          </th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Number</th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Proportion</th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Length</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white">
                        {renderUTRElementRow('IRES', selectedTranscript.fiveUtrIres)}
                        {renderUTRElementRow('miRNAs', selectedTranscript.fiveUtrMiRnas)}
                        {renderUTRElementRow('PolyA sites', selectedTranscript.fiveUtrPolyaSites)}
                        {renderUTRElementRow('Repeats', selectedTranscript.fiveUtrRepeats)}
                        {renderUTRElementRow('Rfam motif', selectedTranscript.fiveUtrRfamMotifs)}
                        {renderUTRElementRow('uORFs', selectedTranscript.fiveUtrUorfs)}
                      </tbody>
                    </table>
                  </div>

                  {/* 3' UTR */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">3&apos; UTR</h3>
                    <table className="min-w-full border border-gray-300 rounded-lg">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Element</th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">
                            <div className="flex items-center justify-center">
                              <span>Yes OR No</span>
                              {renderInfoIcon('',
                                <>
                                  <span style={{color: 'red'}}>Red</span> represents YES, <span style={{color: 'green'}}>Green</span> represents NO
                                </>
                              )}
                            </div>
                          </th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Number</th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Proportion</th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Length</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white">
                        {renderUTRElementRow('IRES', selectedTranscript.threeUtrIres)}
                        {renderUTRElementRow('miRNAs', selectedTranscript.threeUtrMiRnas)}
                        {renderUTRElementRow('PolyA sites', selectedTranscript.threeUtrPolyaSites)}
                        {renderUTRElementRow('Repeats', selectedTranscript.threeUtrRepeats)}
                        {renderUTRElementRow('Rfam motif', selectedTranscript.threeUtrRfamMotifs)}
                        {renderUTRElementRow('uORFs', selectedTranscript.threeUtrUorfs)}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Footer */}
      <div className="mt-8">
        <Footer />
      </div>
      
      {/* 点击其他地方关闭下载菜单 */}
      {showDownloadOptions && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowDownloadOptions(false)}
        />
      )}
    </div>
  );
} 