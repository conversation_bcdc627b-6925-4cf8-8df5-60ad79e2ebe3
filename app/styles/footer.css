.bigd-footer {
  font-family: Arial, serif;
  background-color: #f9f9f9;
  margin-bottom: 0;
  padding: 0 15px 10px 15px;
}

.bigd-panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0,0,0,.05);
}

.bigd-panel-default {
  border-color: #ddd;
}

.bigd-panel-body {
  padding: 15px;
}

.bigd-container-fluid {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.bigd-row {
  margin-right: -15px;
  margin-left: -15px;
  display: flex;
  flex-wrap: wrap;
}

.bigd-col-md-2 {
  width: 16.66%;
  padding: 0 15px;
}

.bigd-col-md-3 {
  width: 25%;
  padding: 0 15px;
}

.bigd-col-md-8 {
  width: 66.66%;
  padding: 0 15px;
}

.bigd-col-md-9 {
  width: 75%;
  padding: 0 15px;
  display: flex;
  flex-wrap: wrap;
}

.bigd-col-lg-offset-1 {
  margin-left: 8.33%;
}

.bigd-center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}

.bigd-list-unstyled {
  padding-left: 0;
  list-style: none;
}

.bigd-img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}

.bigd-text-primary {
  color: #337ab7;
}

.bigd-text-muted {
  color: #777;
}

.footer-hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eee;
}

@media (max-width: 768px) {
  .bigd-col-md-2, .bigd-col-md-3, .bigd-col-md-8, .bigd-col-md-9 {
    width: 100%;
    margin-bottom: 15px;
  }
  
  .bigd-col-lg-offset-1 {
    margin-left: 0;
  }
} 