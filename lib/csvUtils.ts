export interface ApiDataPoint {
  id: number;
  transcriptId: string;
  projectId: string;
  bioprojectId: string;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  tr: number | null;
  evi: number | null;
  te: number;
  geneId: string;
  geneSymbol: string;
}

export interface GeneApiDataPoint {
  id: number;
  geneSymbol: string;
  geneId: string;
  projectId: string;
  expressedTranscriptNumber: number;
  tissueCellType: string;
  cellLine: string;
  disease: string;
  chromosome: string;
  tr: number | null;
  evi: number | null;
  te: number;
}

export interface DataPoint {
  transcript_id: string;
  TR: number | null;
  EVI: number | null;
  TE: number;
  log2TE?: number;
  zscore?: number;
  quantile_group?: string;
}

export interface GeneDataPoint {
  gene_id: string;
  gene_symbol: string;
  TR: number | null;
  EVI: number | null;
  TE: number;
  log2TE?: number;
  zscore?: number;
  quantile_group?: string;
}

export function parseApiData(apiData: ApiDataPoint[]): DataPoint[] {
  const data: DataPoint[] = [];

  for (const item of apiData) {
    // 保留所有记录，让每个指标独立计算其非空值
    data.push({
      transcript_id: item.transcriptId,
      TR: item.tr,
      EVI: item.evi,
      TE: item.te
    });
  }

  return data;
}

export function parseGeneApiData(apiData: GeneApiDataPoint[]): GeneDataPoint[] {
  const data: GeneDataPoint[] = [];

  for (const item of apiData) {
    // 保留所有记录，让每个指标独立计算其非空值
    data.push({
      gene_id: item.geneId,
      gene_symbol: item.geneSymbol,
      TR: item.tr,
      EVI: item.evi,
      TE: item.te
    });
  }

  return data;
}

export function calculateZScore(data: DataPoint[]): DataPoint[] {
  // 过滤出有效的TE数据并计算log2TE
  const validData = data.filter(d => d.TE > 0).map(d => ({
    ...d,
    log2TE: Math.log2(d.TE)
  }));
  
  if (validData.length === 0) {
    return [];
  }
  
  // 计算z-score（使用样本标准差，与R语言scale函数一致）
  const log2TEValues = validData.map(d => d.log2TE!);
  const mean = log2TEValues.reduce((sum, val) => sum + val, 0) / log2TEValues.length;
  const variance = log2TEValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (log2TEValues.length - 1);
  const stdDev = Math.sqrt(variance);
  
  if (stdDev === 0) {
    // 如果标准差为0，所有值都相同
    return validData.map(d => ({
      ...d,
      zscore: 0,
      quantile_group: 'Q1'
    }));
  }
  
  // 计算分位数（使用Python方法，与您的代码一致）
  const zscores = validData.map(d => (d.log2TE! - mean) / stdDev);
  const sortedZscores = [...zscores].sort((a, b) => a - b);

  // 使用Python方式计算分位数
  const n = sortedZscores.length;
  const q1_idx = Math.floor(n * 0.25);
  const q2_idx = Math.floor(n * 0.5);
  const q3_idx = Math.floor(n * 0.75);

  const q1 = sortedZscores[q1_idx];
  const q2 = sortedZscores[q2_idx];
  const q3 = sortedZscores[q3_idx];

  return validData.map((d, index) => {
    const zscore = zscores[index];

    // 根据z-score分配分位数等级（与Python代码一致）
    let quantile_group = 'Q1';
    if (zscore <= q1) {
      quantile_group = 'Q1';
    } else if (zscore <= q2) {
      quantile_group = 'Q2';
    } else if (zscore <= q3) {
      quantile_group = 'Q3';
    } else {
      quantile_group = 'Q4';
    }

    return {
      ...d,
      zscore,
      quantile_group
    };
  });
}

export function calculateZScoreForDataType(data: DataPoint[], dataType: 'TE' | 'TR' | 'EVI'): DataPoint[] {
  let validData: (DataPoint & { log2Value?: number })[];
  
  if (dataType === 'TE') {
    validData = data.filter(d => d.TE !== null && d.TE !== undefined).map(d => ({
      ...d,
      log2Value: Math.log2(d.TE)
    }));
  } else if (dataType === 'TR') {
    validData = data.filter(d => d.TR !== null && d.TR !== undefined && d.TR! > 0).map(d => ({
      ...d,
      log2Value: Math.log2(d.TR!)
    }));
  } else { // EVI
    validData = data.filter(d => d.EVI !== null && d.EVI !== undefined).map(d => ({
      ...d,
      log2Value: Math.log2(d.EVI!)
    }));
  }
  
  if (validData.length === 0) {
    return [];
  }
  
  // 计算z-score（使用样本标准差，与R语言scale函数一致）
  const log2Values = validData.map(d => d.log2Value!);
  const mean = log2Values.reduce((sum, val) => sum + val, 0) / log2Values.length;
  const variance = log2Values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (log2Values.length - 1);
  const stdDev = Math.sqrt(variance);
  
  if (stdDev === 0) {
    // 如果标准差为0，所有值都相同
    return validData.map(d => ({
      ...d,
      zscore: 0,
      quantile_group: 'Q1'
    }));
  }
  
  // 计算分位数（使用Python方法，与您的代码一致）
  const zscores = validData.map(d => (d.log2Value! - mean) / stdDev);
  const sortedZscores = [...zscores].sort((a, b) => a - b);

  // 使用Python方式计算分位数
  const n = sortedZscores.length;
  const q1_idx = Math.floor(n * 0.25);
  const q2_idx = Math.floor(n * 0.5);
  const q3_idx = Math.floor(n * 0.75);

  const q1 = sortedZscores[q1_idx];
  const q2 = sortedZscores[q2_idx];
  const q3 = sortedZscores[q3_idx];
  
  return validData.map((d, index) => {
    const zscore = zscores[index];

    // 根据z-score分配分位数等级（与Python代码一致）
    let quantile_group = 'Q1';
    if (zscore <= q1) {
      quantile_group = 'Q1';
    } else if (zscore <= q2) {
      quantile_group = 'Q2';
    } else if (zscore <= q3) {
      quantile_group = 'Q3';
    } else {
      quantile_group = 'Q4';
    }

    return {
      ...d,
      zscore,
      quantile_group
    };
  });
}

export function prepareHistogramData(data: DataPoint[], binWidth: number = 0.2) {
  if (!data.length) return [];
  
  const minZ = Math.min(...data.map(d => d.zscore!));
  const maxZ = Math.max(...data.map(d => d.zscore!));
  
  const bins: { [key: string]: { count: number; Q1: number; Q2: number; Q3: number; Q4: number } } = {};
  
  // 创建bins
  for (let z = Math.floor(minZ / binWidth) * binWidth; z <= Math.ceil(maxZ / binWidth) * binWidth; z += binWidth) {
    const binKey = z.toFixed(1);
    bins[binKey] = { count: 0, Q1: 0, Q2: 0, Q3: 0, Q4: 0 };
  }
  
  // 填充数据
  data.forEach(d => {
    const binKey = (Math.floor(d.zscore! / binWidth) * binWidth).toFixed(1);
    if (bins[binKey]) {
      bins[binKey].count++;
      bins[binKey][d.quantile_group as keyof typeof bins[string]]++;
    }
  });
  
  return Object.entries(bins).map(([bin, counts]) => ({
    bin: parseFloat(bin),
    ...counts
  })).filter(d => d.count > 0);
}

// 为ECharts准备柱状图数据
export function prepareEChartsHistogramData(data: DataPoint[], binWidth: number = 0.2) {
  if (!data.length) return [];

  const minZ = Math.min(...data.map(d => d.zscore!));
  const maxZ = Math.max(...data.map(d => d.zscore!));

  const bins: { [key: string]: { count: number; Q1: number; Q2: number; Q3: number; Q4: number } } = {};

  // 创建bins
  for (let z = Math.floor(minZ / binWidth) * binWidth; z <= Math.ceil(maxZ / binWidth) * binWidth; z += binWidth) {
    const binKey = z.toFixed(1);
    bins[binKey] = { count: 0, Q1: 0, Q2: 0, Q3: 0, Q4: 0 };
  }

  // 填充数据
  data.forEach(d => {
    const binKey = (Math.floor(d.zscore! / binWidth) * binWidth).toFixed(1);
    if (bins[binKey]) {
      bins[binKey].count++;
      bins[binKey][d.quantile_group as keyof typeof bins[string]]++;
    }
  });

  // 转换为ECharts需要的格式
  const result: Array<{ bin: string; frequency: number; quantile: string }> = [];

  Object.entries(bins).forEach(([bin, counts]) => {
    if (counts.count > 0) {
      // 为每个quantile创建一个数据点
      (['Q1', 'Q2', 'Q3', 'Q4'] as const).forEach(quantile => {
        if (counts[quantile] > 0) {
          result.push({
            bin: bin,
            frequency: counts[quantile],
            quantile: quantile
          });
        }
      });
    }
  });

  return result.sort((a, b) => parseFloat(a.bin) - parseFloat(b.bin));
}

// 基因数据的Z-Score计算函数
export function calculateGeneZScore(data: GeneDataPoint[]): GeneDataPoint[] {
  // 过滤出有效的TE数据并计算log2TE
  const validData = data.filter(d => d.TE > 0).map(d => ({
    ...d,
    log2TE: Math.log2(d.TE)
  }));
  
  if (validData.length === 0) {
    return [];
  }
  
  // 计算z-score（使用样本标准差，与R语言scale函数一致）
  const log2TEValues = validData.map(d => d.log2TE!);
  const mean = log2TEValues.reduce((sum, val) => sum + val, 0) / log2TEValues.length;
  const variance = log2TEValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (log2TEValues.length - 1);
  const stdDev = Math.sqrt(variance);
  
  if (stdDev === 0) {
    // 如果标准差为0，所有值都相同
    return validData.map(d => ({
      ...d,
      zscore: 0,
      quantile_group: 'Q1'
    }));
  }
  
  // 计算分位数（使用Python方法，与您的代码一致）
  const zscores = validData.map(d => (d.log2TE! - mean) / stdDev);
  const sortedZscores = [...zscores].sort((a, b) => a - b);

  // 使用Python方式计算分位数
  const n = sortedZscores.length;
  const q1_idx = Math.floor(n * 0.25);
  const q2_idx = Math.floor(n * 0.5);
  const q3_idx = Math.floor(n * 0.75);

  const q1 = sortedZscores[q1_idx];
  const q2 = sortedZscores[q2_idx];
  const q3 = sortedZscores[q3_idx];

  return validData.map((d, index) => {
    const zscore = zscores[index];

    // 根据z-score分配分位数等级（与Python代码一致）
    let quantile_group = 'Q1';
    if (zscore <= q1) {
      quantile_group = 'Q1';
    } else if (zscore <= q2) {
      quantile_group = 'Q2';
    } else if (zscore <= q3) {
      quantile_group = 'Q3';
    } else {
      quantile_group = 'Q4';
    }

    return {
      ...d,
      zscore,
      quantile_group
    };
  });
}

export function calculateGeneZScoreForDataType(data: GeneDataPoint[], dataType: 'TE' | 'TR' | 'EVI'): GeneDataPoint[] {
  let validData: (GeneDataPoint & { log2Value?: number })[];
  
  if (dataType === 'TE') {
    validData = data.filter(d => d.TE !== null && d.TE !== undefined).map(d => ({
      ...d,
      log2Value: Math.log2(d.TE)
    }));
  } else if (dataType === 'TR') {
    validData = data.filter(d => d.TR !== null && d.TR !== undefined && d.TR! > 0).map(d => ({
      ...d,
      log2Value: Math.log2(d.TR!)
    }));
  } else { // EVI
    validData = data.filter(d => d.EVI !== null && d.EVI !== undefined).map(d => ({
      ...d,
      log2Value: Math.log2(d.EVI!)
    }));
  }
  
  if (validData.length === 0) {
    return [];
  }
  
  // 计算z-score（使用样本标准差，与R语言scale函数一致）
  const log2Values = validData.map(d => d.log2Value!);
  const mean = log2Values.reduce((sum, val) => sum + val, 0) / log2Values.length;
  const variance = log2Values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (log2Values.length - 1);
  const stdDev = Math.sqrt(variance);
  
  if (stdDev === 0) {
    // 如果标准差为0，所有值都相同
    return validData.map(d => ({
      ...d,
      zscore: 0,
      quantile_group: 'Q1'
    }));
  }
  
  // 计算分位数（使用Python方法，与您的代码一致）
  const zscores = validData.map(d => (d.log2Value! - mean) / stdDev);
  const sortedZscores = [...zscores].sort((a, b) => a - b);

  // 使用Python方式计算分位数
  const n = sortedZscores.length;
  const q1_idx = Math.floor(n * 0.25);
  const q2_idx = Math.floor(n * 0.5);
  const q3_idx = Math.floor(n * 0.75);

  const q1 = sortedZscores[q1_idx];
  const q2 = sortedZscores[q2_idx];
  const q3 = sortedZscores[q3_idx];
  
  return validData.map((d, index) => {
    const zscore = zscores[index];

    // 根据z-score分配分位数等级（与Python代码一致）
    let quantile_group = 'Q1';
    if (zscore <= q1) {
      quantile_group = 'Q1';
    } else if (zscore <= q2) {
      quantile_group = 'Q2';
    } else if (zscore <= q3) {
      quantile_group = 'Q3';
    } else {
      quantile_group = 'Q4';
    }

    return {
      ...d,
      zscore,
      quantile_group
    };
  });
}

// 为基因数据准备ECharts柱状图数据
export function prepareGeneEChartsHistogramData(data: GeneDataPoint[], binWidth: number = 0.2) {
  if (!data.length) return [];

  const minZ = Math.min(...data.map(d => d.zscore!));
  const maxZ = Math.max(...data.map(d => d.zscore!));

  const bins: { [key: string]: { count: number; Q1: number; Q2: number; Q3: number; Q4: number } } = {};

  // 创建bins
  for (let z = Math.floor(minZ / binWidth) * binWidth; z <= Math.ceil(maxZ / binWidth) * binWidth; z += binWidth) {
    const binKey = z.toFixed(1);
    bins[binKey] = { count: 0, Q1: 0, Q2: 0, Q3: 0, Q4: 0 };
  }

  // 填充数据
  data.forEach(d => {
    const binKey = (Math.floor(d.zscore! / binWidth) * binWidth).toFixed(1);
    if (bins[binKey]) {
      bins[binKey].count++;
      bins[binKey][d.quantile_group as keyof typeof bins[string]]++;
    }
  });

  // 转换为ECharts需要的格式
  const result: Array<{ bin: string; frequency: number; quantile: string }> = [];

  Object.entries(bins).forEach(([bin, counts]) => {
    if (counts.count > 0) {
      // 为每个quantile创建一个数据点
      (['Q1', 'Q2', 'Q3', 'Q4'] as const).forEach(quantile => {
        if (counts[quantile] > 0) {
          result.push({
            bin: bin,
            frequency: counts[quantile],
            quantile: quantile
          });
        }
      });
    }
  });

  return result.sort((a, b) => parseFloat(a.bin) - parseFloat(b.bin));
}

/**
 * CSV转义函数：处理包含特殊字符的值，确保符合RFC 4180标准
 * @param value 需要转义的值
 * @returns 转义后的字符串
 */
export function escapeCsvValue(value: string | number | null | undefined): string {
  if (value === null || value === undefined) return '';
  const str = String(value);

  // 如果包含逗号、双引号或换行符，需要用双引号包围，并转义内部的双引号
  if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
    return `"${str.replace(/"/g, '""')}"`;
  }

  return str;
}

/**
 * 导出数据为CSV格式
 * @param data 要导出的数据数组
 * @param headers CSV文件的表头
 * @param filename 文件名（不包含扩展名）
 * @param getRowData 从数据项获取行数据的函数
 */
export function exportToCsv<T>(
  data: T[],
  headers: string[],
  filename: string,
  getRowData: (item: T) => (string | number | null | undefined)[]
): void {
  const csvContent = [
    headers.join(','),
    ...data.map(item => getRowData(item).map(escapeCsvValue).join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');

  link.href = url;
  link.download = `${filename}.csv`;
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}