{"name": "utr_ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@tanstack/react-table": "^8.21.3", "@types/d3": "^7.4.3", "@types/html2canvas": "^1.0.0", "@types/plotly.js": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "echarts": "^5.6.0", "framer-motion": "^12.19.2", "html2canvas": "^1.4.1", "lucide-react": "^0.525.0", "next": "^15.3.4", "plotly.js-dist-min": "^3.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-heatmap-grid": "^0.9.1", "react-plotly.js": "^2.6.0", "recharts": "^2.15.4", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-plotly.js": "^2.6.3", "eslint": "^9.30.0", "eslint-config-next": "^15.3.4", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}