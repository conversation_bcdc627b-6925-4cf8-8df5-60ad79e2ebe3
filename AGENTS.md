# Repository Guidelines

## Project Structure & Module Organization
- app/: Next.js App Router (routes, pages, layouts, global styles). Examples: app/page.tsx, app/analysis/page.tsx, app/components/.
- lib/: Shared utilities (e.g., lib/csvUtils.ts, lib/utils.ts).
- public/: Static assets (e.g., public/logo/...).
- scripts/: One-off tools (e.g., scripts/updateProjectMapping.js).
- .next/, out/: Build output; do not commit manual changes.

## Build, Test, and Development Commands
- npm run dev: Start the dev server with Turbopack. App serves under base path "/tedd".
- npm run build: Create a production build.
- npm run start: Run the production server (after build).
- npm run lint: Lint with Next + TypeScript rules.

## Coding Style & Naming Conventions
- Language: TypeScript + React (function components).
- Linting: ESLint extends next/core-web-vitals and next/typescript. Fix issues before committing.
- TailwindCSS: Prefer utility classes over custom CSS; global styles in app/globals.css.
- Indentation: 2 spaces; max 120 columns recommended.
- Naming: PascalCase for components (app/components/MyWidget.tsx); camelCase for functions/variables; UPPER_SNAKE_CASE for constants.
- Routes: In app/, use lowercase-kebab-case for folder names (e.g., app/search/page.tsx). Avoid hardcoding "/tedd"; use relative links (e.g., <Link href="/analysis" />).

## Testing Guidelines
- Current status: No test runner configured. If adding tests, prefer Vitest + React Testing Library for unit tests and Playwright for E2E.
- Suggested layout: Place unit tests alongside code using .test.ts(x) or in __tests__ directories.
- Coverage: Target meaningful coverage for utilities in lib/ and critical UI logic.

## Commit & Pull Request Guidelines
- Commits: History shows brief Chinese summaries. Adopt a concise conventional style: type(scope): summary.
  - Examples: feat(analysis): add heatmap legend; fix(export): correct CSV header order.
- PRs: Include purpose/changes, linked issues, screenshots for UI updates, and steps to validate.
- Quality gates: Run npm run lint and ensure npm run build succeeds before requesting review.

## Security & Configuration Tips
- Env/base path: Project uses basePath "/tedd" and NEXT_PUBLIC_* vars in next.config.ts. Do not hardcode host/paths; prefer relative URLs and envs.
- Secrets: Store in .env.local; never commit secrets. Avoid leaking API URLs in screenshots/logs.
