{"permissions": {"allow": ["mcp__ide__executeCode", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(git checkout -- app/analysis/function-level/page.tsx)", "Bash(sed -i '' 's/\\/\\/ 加载选项数据/\\/\\/ Load options data/g' /Users/<USER>/Documents/utr_ui/app/analysis/function-level/page.tsx)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "Bash(npm run lint)", "Bash(python analyze_csv.py)", "<PERSON><PERSON>(python:*)", "Bash(npm run build:*)", "Bash(node:*)", "Bash(grep:*)", "WebFetch(domain:github.com)", "WebSearch", "Bash(npm install:*)", "Bash(npm uninstall:*)"], "deny": []}}